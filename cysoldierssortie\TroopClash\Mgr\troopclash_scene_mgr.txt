---@class troopclash_scene_mgr : fusion_mgrbase
---@field lifeScope troopclash_lifescope
local mgr = bc_Class("troopclash_scene_mgr", Fusion.MgrBase)
---@type troopclash_res_mgr
mgr.resMgr = nil
---@type fusion_mono
mgr.mono = nil
---@type troopclash_ui_mgr
mgr.uiMgr = nil

mgr.DataSrc = nil
mgr.LevelRoot = nil
mgr.PoolParent = nil
mgr.NavMesh = nil
---@type troopclash_player
mgr.playerCtrl = nil
---@type troopclash_camera
mgr.cameraCtrl = nil
---@type troopclash_team
mgr.teamCtrl = nil
---@type troopclash_prop
mgr.propCtrl = nil

mgr.readyFlag = nil
---@type fusion_bindable boolean是否在战斗中
mgr.BattleFlagBind = nil
---@type fusion_bindable number计时器
mgr.TimerBind = nil
---@type fusion_bindable number计时器秒数
mgr.TimeSecondBind = nil

---@type troopclash_enemyData[] 记录随时间生成的怪物
mgr.TeamDatasByTime = nil
---@type troopclash_enemyData[] 记录固定位置生成的怪物
mgr.TeamDatasByPos = nil
---@type troopclash_playerData 玩家数据
mgr.PlayerData = nil
---@type troopclash_propData[] 记录随时间生成的道具
mgr.PropDatasByTime = nil
---@type troopclash_propData[] 记录固定位置生成的道具
mgr.PropDatasByPos = nil
---@type fusion_bindable 任务数量计数
mgr.TaskNumBind = nil
---@type number 任务数量最大值
mgr.TaskNumMax = nil

mgr.tween_Win = nil

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.resMgr = self.lifeScope:GetMgr("troopclash_res_mgr")
    self.mono = self.lifeScope:GetMgr("fusion_mono")
    self.uiMgr = self.lifeScope:GetMgr("troopclash_ui_mgr")
    self.BattleFlagBind = require("fusion_bindable").New(false)
    self.TimerBind = require("fusion_bindable").New(0)
    self.TimeSecondBind = require("fusion_bindable").New(-1)
    self.TaskNumBind = require("fusion_bindable").New(0)
    self.TaskNumBind:Register(function(value)
        --任务目标全部消灭，胜利
        if self.readyFlag and self.BattleFlagBind.value and value < 1 then
            self:GameOver(true)
        end
    end)
end

function mgr:Ready()
    self.readyFlag = false
    self:InitLevelData()
    local neeGameEntry = TroopClash_Define.CS.GameObject.Instantiate(self.resMgr.NeeGameEntryPrefab)
    neeGameEntry.transform:SetParent(self.mono.globalMonoGO.transform)
    neeGameEntry.transform.localPosition = TroopClash_Define.CacheVector3.Zero
    neeGameEntry.transform.localScale = TroopClash_Define.CacheVector3.One
    --通过luaMono来设置luaclass，GameLuaBehavior之间的绑定
    TroopClash_Define.CS.NeeGame.Instance.createLuaComponentDel = function(go)
        local luaMono = go:GetComponent(TroopClash_Define.TypeOf.LuaMono)
        local src = luaMono.luaBehaviour
        local str = luaMono.luaName
        local tempClass = require(str).New(luaMono, luaMono.referCol, luaMono.luaData)
        luaMono.luaComp = tempClass
        tempClass.luaMono = luaMono
        if luaMono.isStaticType then
            cysoldierssortie_AddMgr(luaMono.luaName, luaMono.luaComp)
        end
        src.enabled = true
        src:BindLuaTable(tempClass)
        if luaMono.hasLuaMonoEvent then
            local comps = luaMono:GetComponents(TroopClash_Define.TypeOf.LuaMonoEvent)
            for i = 0, comps.Length - 1 do
                comps[i]:Bind(luaMono)
            end
        end
    end
    local mainScene = TroopClash_Define.CS.GameObject.Instantiate(self.resMgr.MainScenePrefab)
    mainScene.transform:SetParent(self.mono.globalMonoGO.transform)
    mainScene.transform.localPosition = TroopClash_Define.CacheVector3.Zero
    mainScene.transform.localScale = TroopClash_Define.CacheVector3.One
    self.PoolParent = TroopClash_Define.CS.GameObject("PoolParent").transform
    self.PoolParent:SetParent(self.mono.globalMonoGO.transform)
    self.PoolParent.localPosition = TroopClash_Define.CacheVector3.Zero
    self.PoolParent.localScale = TroopClash_Define.CacheVector3.One
    self.DataSrc = {}
    local neeRefer = mainScene:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
    self.LevelRoot = self.DataSrc.LevelRoot
    --生成场景
    local scene = TroopClash_Define.CS.GameObject.Instantiate(self.resMgr.ScenePrefab)
    scene.transform:SetParent(self.LevelRoot)
    scene.transform.localPosition = TroopClash_Define.CacheVector3.Zero
    scene.transform.localScale = TroopClash_Define.CacheVector3.One
    self.SceneDataSrc = {}
    local sceneRefer = scene:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection)
    sceneRefer:Bind(self.SceneDataSrc)
    self.NavMesh = TroopClash_Define.NavMesh.AddNavMeshData(self.resMgr.SceneNavMesh)
    self:InitEnv()

    --注册出战按钮事件
    self._onFight = function()
        self.playerCtrl:ShowSelectHeroUI(false)
        self.playerCtrl:SlotToTargetPoint()
        self.playerCtrl:CalculateColliderSize()
        self.cameraCtrl:ReadyToBattle(function()
            TroopClash_Define.minigame_mgr.SetStartGame()
            self.BattleFlagBind:SetValue(true)
        end)
    end
    TroopClash_Define.event.Register(TroopClash_Define.event.SOLDIER_FIGHT, self._onFight)

    self.playerCtrl = require("troopclash_player").New(self, self.DataSrc.Player)
    self.cameraCtrl = require("troopclash_camera").New(self)
    self.teamCtrl = require("troopclash_team").New(self, self.resMgr)
    self.propCtrl = require("troopclash_prop").New(self, self.resMgr)
end

function mgr:InitEnv()
    local RenderSettings = CS.UnityEngine.RenderSettings
    RenderSettings.ambientMode = 3
    RenderSettings.ambientLight = { r = 0.4705, g = 0.4705, b = 0.4705, a = 1.0 }
    RenderSettings.fog = false
    CS.MiniGame.Fog.EngineHeightFog.Instance:EnableFog(false)
end

function mgr:GameStart()
    self.TaskNumBind:SetValue(self.TaskNumMax)
    self.BattleFlagBind:SetValue(false)
    self.TimerBind:SetValue(0)
    self.TimeSecondBind:SetValue(-1)
    self.playerCtrl:Reset()
    self.cameraCtrl:Reset()
    self.teamCtrl:Reset()
    self.propCtrl:Reset()
    self.readyFlag = true
end

--- 处理关卡数据
function mgr:InitLevelData()
    local allEnemyFlag = self.resMgr.CurLvConfig.PassType == TroopClash_Define.LevelPassType.AllEnemy
    self.TaskNumMax = 0
    local tmpPlayerData = self.resMgr.LevelData.Player
    ---@class troopclash_playerData
    self.PlayerData = {
        Pos = tmpPlayerData.Pos
    }
    self.TeamDatasByTime = {}
    self.TeamDatasByPos = {}
    local tmpTimeIndex = 0
    local tmpPosIndex = 0
    local enemyDatas = self.resMgr.LevelData.EnemyGroup
    if enemyDatas ~= nil then
        for _, v in ipairs(enemyDatas) do
            ---@class troopclash_enemyData
            local data = {
                TeamID = v.TeamID, --队伍id
                Type = v.Type,     --生成类型
                Pos = nil,         --生成坐标
                Delay = nil,       --生成延迟
            }
            if data.Type == TroopClash_Define.TeamSpawnType.Pos then
                data.Pos = v.Pos
                tmpPosIndex = tmpPosIndex + 1
                self.TeamDatasByPos[tmpPosIndex] = data
            elseif data.Type == TroopClash_Define.TeamSpawnType.Timer then
                data.Delay = v.Delay
                tmpTimeIndex = tmpTimeIndex + 1
                self.TeamDatasByTime[tmpTimeIndex] = data
            end
            local tmpTeamConfig = self.resMgr:GetTeamConfigById(data.TeamID)
            ---@param v troopclash_TeamUnitData
            for i, v in ipairs(tmpTeamConfig.UnitDatas) do
                if allEnemyFlag then
                    self.TaskNumMax = self.TaskNumMax + v.Count
                else
                    local unitConfig = self.resMgr:GetUnitConfigById(v.UnitId)
                    if unitConfig.UnitType == cysoldierssortie_unit_type.BossEnemy then
                        self.TaskNumMax = self.TaskNumMax + v.Count
                    end
                end
            end
        end
    end
    self.PropDatasByTime = {}
    self.PropDatasByPos = {}
    local tmpPropTimeIndex = 0
    local tmpPropPosIndex = 0
    local propDatas = self.resMgr.LevelData.Props
    if propDatas ~= nil then
        for _, v in ipairs(propDatas) do
            ---@class troopclash_propData
            local data = {
                PropID = v.PropID, --队伍id
                Type = v.Type,     --生成类型
                Pos = nil,         --生成坐标
                Delay = nil,       --生成延迟
            }
            if data.Type == TroopClash_Define.TeamSpawnType.Pos then
                data.Pos = v.Pos
                tmpPropPosIndex = tmpPropPosIndex + 1
                self.PropDatasByPos[tmpPropPosIndex] = data
            elseif data.Type == TroopClash_Define.TeamSpawnType.Timer then
                data.Delay = v.Delay
                tmpPropTimeIndex = tmpPropTimeIndex + 1
                self.PropDatasByTime[tmpPropTimeIndex] = data
            end
        end
    end
end

function mgr:EnemyDead(character)
    self.teamCtrl:EnemyDie(character)
    if self.resMgr.CurLvConfig.PassType == TroopClash_Define.LevelPassType.AllEnemy then
        self.TaskNumBind:SetValue(self.TaskNumBind.value - 1)
    elseif character._unit_type == cysoldierssortie_unit_type.BossEnemy then
        self.TaskNumBind:SetValue(self.TaskNumBind.value - 1)
    end
end

---获取到寻路网格内的安全位置
function mgr:GetNavMeshPosition(pos)
    local res, hit = TroopClash_Define.NavMesh.SamplePosition(pos, 1000, -1)
    return res, res and hit.position or pos
end

function mgr:GetSpawnTeamPos()
    local viewCenterPos = self.cameraCtrl.ViewCenterWorldPos
    local spawnPos = nil
    local minX = viewCenterPos.x - self.cameraCtrl.camBorderSizeHalf.x
    local maxX = viewCenterPos.x + self.cameraCtrl.camBorderSizeHalf.x
    local minZ = viewCenterPos.z - self.cameraCtrl.camBorderSizeHalf.y
    local maxZ = viewCenterPos.z + self.cameraCtrl.camBorderSizeHalf.y
    local sideList = { 1, 1, 2, 2, 2, 2, 3, 3, 4, 4, 4, 4 }
    local spawnDir = sideList[math.random(1, #sideList)]
    -- 上方
    if spawnDir == 4 then
        spawnPos = TroopClash_Define.CS.Vector3(math.lerp(minX, maxX, math.random()), 0, maxZ)
    elseif spawnDir == 2 then
        -- 下方
        spawnPos = TroopClash_Define.CS.Vector3(math.lerp(minX, maxX, math.random()), 0, minZ)
    elseif spawnDir == 1 then
        -- 左方
        spawnPos = TroopClash_Define.CS.Vector3(minX, 0, math.lerp(minZ, maxZ, math.random()))
    else
        -- 右方
        spawnPos = TroopClash_Define.CS.Vector3(maxX, 0, math.lerp(minZ, maxZ, math.random()))
    end
    _, spawnPos = self:GetNavMeshPosition(spawnPos)
    spawnPos.y = 0
    return spawnPos
end

function mgr:GetSpawnPropPos()
    local boundMin = 0.2
    local boundMax = 0.3
    -- 1/4屏随机位置
    local rPos = TroopClash_Define.CS.Vector3(math.lerp(boundMin, boundMax, math.random()), 0,
        math.lerp(boundMin, boundMax, math.random()))
    local spawnDir = math.random()
    -- 左下角
    if spawnDir < 0.25 then
    elseif spawnDir < 0.5 then
        -- 左上角
        rPos.z = 1 - rPos.z
    elseif spawnDir < 0.75 then
        -- 右下角
        rPos.x = 1 - rPos.x
    else
        -- 右上角
        rPos.x = 1 - rPos.x
        rPos.z = 1 - rPos.z
    end
    local viewCenterPos = self.cameraCtrl.ViewCenterWorldPos
    local minX = viewCenterPos.x - self.cameraCtrl.camBorderSizeHalf.x
    local maxX = viewCenterPos.x + self.cameraCtrl.camBorderSizeHalf.x
    local minZ = viewCenterPos.z - self.cameraCtrl.camBorderSizeHalf.y
    local maxZ = viewCenterPos.z + self.cameraCtrl.camBorderSizeHalf.y
    rPos.x = math.lerp(minX, maxX, rPos.x)
    rPos.z = math.lerp(minZ, maxZ, rPos.z)
    _, rPos = self:GetNavMeshPosition(rPos)
    rPos.y = 0
    return rPos
end

function mgr:OnUpdate(deltaTime)
    if not self.readyFlag or not self.BattleFlagBind.value then
        return
    end
    self.TimerBind:SetValue(self.TimerBind.value + deltaTime)
    self.TimeSecondBind:SetValue(math.floor(self.TimerBind.value))
    self.playerCtrl:Update(deltaTime)
    self.teamCtrl:Update(deltaTime)
    self.propCtrl:Update(deltaTime)

    self:GetSpawnTeamPos()
end

function mgr:OnLateUpdate(deltaTime)
    if not self.readyFlag or not self.BattleFlagBind.value then
        return
    end
    self.cameraCtrl:LateUpdate(deltaTime)
end

function mgr:GameOver(win)
    if not self.readyFlag then
        return
    end
    self.readyFlag = false
    self.playerCtrl:StopAll()
    self.BattleFlagBind:SetValue(false)
    self:KillTween_Win()
    if win then
        cysoldierssortie_PlaySfx(cysoldierssortie_FxName.gameWin)
    else
        cysoldierssortie_PlaySfx(cysoldierssortie_FxName.gameFail)
    end
    --等待结算界面进度条增长后显示胜利or失败界面
    self.tween_Win = TroopClash_Define.DOVirtual.DelayedCall(0.5, function()
        if win then
            TroopClash_Define.minigame_mgr.BroadcastMsg(cysoldierssortie_MsgCode.STAGE_SUCCESS)
        else
            TroopClash_Define.minigame_mgr.BroadcastMsg(cysoldierssortie_MsgCode.STAGE_FAIL)
        end
    end)
end

function mgr:KillTween_Win()
    if self.tween_Win ~= nil then
        self.tween_Win:Kill()
        self.tween_Win = nil
    end
end

function mgr:__delete()
    self:KillTween_Win()
    TroopClash_Define.event.Unregister(TroopClash_Define.event.SOLDIER_FIGHT, self._onFight)
    self._onFight = nil
    self.readyFlag = false
    self.BattleFlagBind = false
    if self.NavMesh ~= nil then
        self.NavMesh:Remove()
        self.NavMesh = nil
    end
    if self.playerCtrl ~= nil then
        self.playerCtrl:Delete()
        self.playerCtrl = nil
    end
    if self.cameraCtrl ~= nil then
        self.cameraCtrl:Delete()
        self.cameraCtrl = nil
    end
    if self.teamCtrl ~= nil then
        self.teamCtrl:Delete()
        self.teamCtrl = nil
    end
    if self.propCtrl ~= nil then
        self.propCtrl:Delete()
        self.propCtrl = nil
    end
end

return mgr
