local poolItemBase = TinyRush_CreateClass("bcmanyknives_effect_particle_item"):baseClass(require("tinyrush_gopoolitem"))

poolItemBase.sceneMgr = nil
poolItemBase.particle = nil
poolItemBase.type = nil

poolItemBase.pauseUnRegister = nil

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    self.particle = self.gameObject:GetComponentInChildren(typeof(CS.UnityEngine.ParticleSystem))
end

function poolItemBase:GetColliderKey()
    return self.gameObject
end

function poolItemBase:Init(sceneMgr, type)
    self.sceneMgr = sceneMgr
    self.type = type
    if self.pauseUnRegister == nil then
        self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
            self:PauseListener(value)
        end)
    end
end
function poolItemBase:RefreshPause()
    self:PauseListener(self.sceneMgr.pauseBind.value)
end

function poolItemBase:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.tween ~= nil then
        self.tween.timeScale = timeScale
    end
end

--- func desc
---@param actionType number :1:延迟消失
function poolItemBase:Play(actionType, ...)
    self:KillTween()
    self.transform:SetParent(self.sceneMgr.levelRoot.transform)
    if self.particle ~= nil then
        self.particle:Simulate(0, true)
        self.particle:Play()
    end
    if actionType == 1 then
        local startPos, dur, scale = ...
        self.transform.localScale = bc_CS_Vector3.one * (scale ~= nil and scale or 1)
        self.transform.position = startPos
        self.tween = ManyKnivesDefine.DOVirtual.DelayedCall(dur, function()
            self.sceneMgr:PushEffect(self.type, self)
        end, false)
    elseif actionType == 2 then
        local startPos, dur = ...
        self.transform.position = startPos
        self.tween = ManyKnivesDefine.DOVirtual.DelayedCall(dur, function()
            self.sceneMgr:PushEffect(self.type, self)
        end, false)
    end
    self:RefreshPause()
end

function poolItemBase:dispose()
    self:KillTween()
    self.__base:dispose()
end

function poolItemBase:recycle()
    self:KillTween()
    if self.pauseUnRegister ~= nil then
        self.pauseUnRegister:unRegister()
        self.pauseUnRegister = nil
    end
end

function poolItemBase:KillTween()
    if self.tween ~= nil then
        self.tween:Kill()
        self.tween = nil
    end
end

return poolItemBase
