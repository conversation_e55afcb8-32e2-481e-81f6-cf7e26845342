local cysoldierssortie_mgr_actorInstance = bc_Class("cysoldierssortie_mgr_pool") --类名用小游戏名加后缀保证全局唯一
local game_scheme 	= require "game_scheme"
local cysoldierssortie_comp_character = require "cysoldierssortie_comp_character"
local table = table
local minigame_buff_mgr= require "minigame_buff_mgr"
local cysoldierssortie_lst_group_type = cysoldierssortie_lst_group_type
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local LookAtTargetSystem = CS.cysoldierssortie.LookAtTargetSystem
local LookAtTargetSystemInstance
local log = log
local bc_Time = bc_Time
local cysoldierssortie_lua_queue = require("cysoldierssortie_lua_queue")

local minigame_mgr = require "minigame_mgr"
function cysoldierssortie_mgr_actorInstance.__init(self, lua<PERSON>ono, referCol, luaData, ...)
    if luaMono then
        self.luaMono = luaMono
    end
    if referCol then
        referCol:Bind(self)
    end
    if luaData then
        cysoldierssortie_InitLuaData(self, luaData)
    end
    local res = xpcall(function()
        LookAtTargetSystemInstance = LookAtTargetSystem.Instance
    end,debug.traceback)
    self._waitLoadLst = cysoldierssortie_lua_queue.New()
    self._waitLoadHeroLst = cysoldierssortie_lua_queue.New()
end
-- lua脚本正式开始

--生命周期函数
function cysoldierssortie_mgr_actorInstance:OnEnable(data)
    if self.enabledOnce then
        return
    end
    self.enabledOnce = true;

    self.dataSrc = cysoldierssortie_CshapToLuaValue(data)
    self.gameObject = self.dataSrc.selfCshap.gameObject
    self.transform = self.dataSrc.selfCshap.transform
end

function cysoldierssortie_mgr_actorInstance:InitData()
    --最大实体数量显示
    self._max_view_actor = cysoldierssortie_TroopClash and TroopClash_Define.Params.MaxViewActor or 60
    --当前实体显示的实体数量
    self._view_actor = 0
    --等待实体显示队列
    self._view_actor_queue = {}
    --临时记录下boss
    self._boss_lst = {}
    --拥有预警组件的actor
    self._early_warning_actor = {}
    --生成唯一sid
    self._sid = 1
end

function cysoldierssortie_mgr_actorInstance:Start()
    self:InitData()
end

function cysoldierssortie_mgr_actorInstance:AddEarlyWarningActor(actor,startPos)
    self._early_warning_actor[#self._early_warning_actor+1] = {actor = actor,startPos = startPos}
end

function cysoldierssortie_mgr_actorInstance:GenerateSid()
    self._sid = self._sid +1
    return self._sid
end

function cysoldierssortie_mgr_actorInstance:AddEarlyWarningComp()
    if not self._early_warning_actor and #self._early_warning_actor>0 then
        return
    end
    for i=#self._early_warning_actor,1,-1 do
        local actor =  self._early_warning_actor[i].actor
        local startPos =  self._early_warning_actor[i].startPos
        actor:AddComponent(cysoldierssortie_comp_name.EarlyWarning,{character = actor,startPos = startPos})
        table.remove(self._early_warning_actor,i)
    end
end


function cysoldierssortie_mgr_actorInstance:CreateCharacterData(unitId,localPos,heroID,_hp,_attack,_player)
    local curHeroId = 0
    local unit_type = cysoldierssortie_unit_type.NormalEnemy
    local hp = 100
    local atk = 2.5
    local atkRange = 1
    local moveSpeed = 10
    local enemyRange = 15
    local scale = 1
    local hitDeceleration = 0
    local bloodOffset = 0
    local lst_group_type = cysoldierssortie_lst_group_type.HeroLst
    local level = 1
    self.LevelMgr = self.LevelMgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local level_atk_increase_ratio =self.LevelMgr.curLevel.MiniLevelCfg.AtkIncreaseRatio
    local atkIncreaseRatioByLevel =level_atk_increase_ratio or  0        --管卡士兵攻击增幅表
    local weapons = {}
    local sid = self:GenerateSid()
    
    if unitId then
        local unit_cfg = nil
        if cysoldierssortie_TroopClash then
            ---@type troopclash_res_mgr
            local resMgr = self.LevelMgr.resMgr
            unit_cfg = resMgr:GetUnitConfigById(unitId)
        elseif cysoldierssortie_KingShot then
            ---@type kingshot_res_mgr
            local resMgr = self.LevelMgr.resMgr
            unit_cfg = resMgr:GetUnitConfigById(unitId)
        else
            unit_cfg = game_scheme:MiniUnit_0(unitId)
        end
        if unit_cfg then
            unit_type = unit_cfg.UnitType
            bloodOffset=unit_cfg.BloodOffset or 0
            atkRange = unit_cfg.AtkRange
            if cysoldierssortie_TroopClash and _player then
                atkRange = TroopClash_Define.Params.PlayerAtkRange
            end
            if cysoldierssortie_KingShot and _player then
                atkRange = KingShot_Define.Params.PlayerAtkRange
            end
            moveSpeed = unit_cfg.MoveSpeed
            enemyRange = unit_cfg.EnemyRange
            if cysoldierssortie_TroopClash then
                enemyRange =_player and TroopClash_Define.Params.PlayerSearchRange or TroopClash_Define.Params.EnemySearchRange
            end
            if cysoldierssortie_KingShot then
                enemyRange =_player and KingShot_Define.Params.PlayerSearchRange or KingShot_Define.Params.EnemySearchRange
            end
            scale = unit_cfg.Scale / 100
            hitDeceleration =  unit_cfg.HitDeceleration
            level=unit_cfg.UnitLevel
            curHeroId = heroID or unit_cfg.ModelID
            bloodOffset=unit_cfg.BloodOffset or 0
            hp = _hp or unit_cfg.HP
            atk = _attack or  unit_cfg.ATK
            lst_group_type = cysoldierssortie_lst_group_type.SoldierLst
            local skillID = unit_cfg.SkillID
          
            if skillID and skillID.data then
                local skill_data_array = skillID.data
                if skill_data_array[0] then
                    for k=0,#skill_data_array do
                        local weapon = {}
                        local skill_cfg = nil
                        if cysoldierssortie_TroopClash then
                            ---@type troopclash_res_mgr
                            local resMgr = self.LevelMgr.resMgr
                            skill_cfg = resMgr:GetSkillConfigById(skill_data_array[k])
                        elseif cysoldierssortie_KingShot then
                            ---@type kingshot_res_mgr
                            local resMgr = self.LevelMgr.resMgr
                            skill_cfg = resMgr:GetSkillConfigById(skill_data_array[k])
                        else
                            skill_cfg = game_scheme:MiniSkill_0(skill_data_array[k])
                        end
                        weapon._attackType = skill_cfg.AttackType
                        weapon._damageCoefficient = skill_cfg.DamageCoefficient
                        weapon._attackSpeed = skill_cfg.AttackSpeed / 10000                 --冷却时间
                        weapon._skillLoop =   skill_cfg.SkillLoop and (skill_cfg.SkillLoop / 10000) or 1                     --技能生命周期
                        weapon._skillPriority = skill_cfg.SkillPriority and  skill_cfg.SkillPriority or 1
                        weapon._criticalHit = skill_cfg.CriticalHit
                        weapon._damageRange = skill_cfg.lockRange
                        weapon._ballisiticVelocity = skill_cfg.BallisticVelocity
                        weapon._ballisiticRange = skill_cfg.BallisticRange
                        weapon._pierce = skill_cfg.pierce
                        weapon._skillEffectPath = skill_cfg.SpecialEffectsPath
                        weapon._showEffectsPath = skill_cfg.ShowEffectsPath
                        if skill_cfg.DstEffectsPath ~= "" then
                            weapon._dstEffectsPath =  skill_cfg.DstEffectsPath
                        end
                        weapon._bulletScale = skill_cfg.SpecialEffectScaling > 0 and skill_cfg.SpecialEffectScaling / 100 or 1
                        weapon._skillID = skill_data_array[k]
                        weapon._startCD = skill_cfg.startCD
                        weapon._maxDamageNum = skill_cfg.MaxDamageNumber or 0
                        weapon._nBuffIDs = skill_cfg.nBuffIDs
                        weapon._nSkillRepeatCnt = skill_cfg.nSkillRepeatCnt > 0 and skill_cfg.nSkillRepeatCnt or 1
                        weapon._nTriggerProb = skill_cfg.nTriggerProb
                        weapon._repeatMinInterval = skill_cfg.RepeatMinInterval > 0 and skill_cfg.RepeatMinInterval/10000 or 0
                        weapon._overrideAtkRange = skill_cfg.OverrideAtkRange / 100
                        if  skill_cfg.strAttackAction and skill_cfg.strAttackAction > 0 then
                            weapon._strAttackAction = "Skill0"..skill_cfg.strAttackAction
                        end

                        if skill_cfg.IsUltra then
                            weapon._isUltra = skill_cfg.IsUltra > 0 and true or false
                        end
                        --星级增幅
                        if skill_cfg.StarRatingIncrease then
                            weapon._starRatingIncrease = skill_cfg.StarRatingIncrease
                        end

                        weapons[#weapons+1] = weapon
                    end
                end
            end
        end
    end
    
    local data = 
    {
        Level = level,   Attack = atk,  HP = hp,    HeroID = curHeroId,
        Star=1, player = _player,   UnitType = unit_type,   AttackRange = atkRange, 
        MoveSpeed = moveSpeed,  EnemyRange = enemyRange,    LocalPos = localPos,    UnitID = unitId, 
        Scale = scale,  HitDeceleration = hitDeceleration,  BloodOffset = bloodOffset,  Sid = sid,
        AtkIncreaseRatioByLevel = atkIncreaseRatioByLevel,  LstGroupType = lst_group_type,
        Weapons = weapons
    }
    return data
end

--unitId 单位id， parent,forceView,heroID(英雄id),_hp,_attack,_player
function cysoldierssortie_mgr_actorInstance:EnqueueWaitLoadModel(character)
    if not character._player then
        self._waitLoadLst = self._waitLoadLst or {}
        if not self._waitLoadLst then
            self._waitLoadLst = cysoldierssortie_lua_queue.New()
        end
        self._waitLoadLst:Enqueue(character)
    else
        if not self._waitLoadHeroLst then
            self._waitLoadHeroLst = cysoldierssortie_lua_queue.New()
        end
        self._waitLoadHeroLst:Enqueue(character)
    end
end

local spawnInterval = 0.03333
function cysoldierssortie_mgr_actorInstance:Update()
    if not self._updateTimer or bc_Time.time > self._updateTimer then
        self._updateTimer = spawnInterval + bc_Time.time
        self:DequeueHeroWaitLoadModel()
        if not minigame_mgr.GetIsStartGame() then
            return
        end
        self:DequeueWaitLoadModel() 
    end
end

function cysoldierssortie_mgr_actorInstance:DequeueWaitLoadModel()
    if not self._waitLoadLst then
        return 
    end
    
    local character = self._waitLoadLst:Dequeue()
    if character then
        character._character_entity:CreateModel()
    end
end

function cysoldierssortie_mgr_actorInstance:DequeueHeroWaitLoadModel()
    if not self._waitLoadHeroLst then
        return
    end

    local character = self._waitLoadHeroLst:Dequeue()
    if character then
        character._character_entity:CreateModel()
    end
end

function cysoldierssortie_mgr_actorInstance:CreateCharacter(unitId,localPos,parent,forceView,heroID,_hp,_attack,_player,playEffect)
    local data = self:CreateCharacterData(unitId,localPos,heroID,_hp,_attack,_player)
    local character = cysoldierssortie_comp_character.New()
    character:CreateData(data)
    character:CreateEntity(parent,playEffect or false)
    self:EnqueueWaitLoadModel(character)
    self.LevelMgr = self.LevelMgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel =  self.LevelMgr.curLevel
    if not character._player then
        --先处理敌人
        curLevel:AddEnemy(character)
        minigame_buff_mgr.AddBuffCfg(unitId,character)
        minigame_buff_mgr.CheckCondition(character,minigame_buff_mgr.ConditionType.Incubate)
        if not forceView then
            self:EnqueueActor(character)
        end
        local res = xpcall(function()
            LookAtTargetSystemInstance:RegisterEnemy(character.transform,character._enemyRange)
        end,debug.traceback)
    else
        if  character._isDrone then--神兽
            return character
        end
        local enemyRange = character._enemyRange
        if curLevel:IsRunnerMode() then
            enemyRange = enemyRange 
        end
        local res = xpcall(function()
            LookAtTargetSystemInstance:RegisterSoldier(character.transform,enemyRange)
        end,debug.traceback)
    end

    return character
end

function cysoldierssortie_mgr_actorInstance:EnqueueActor(character)
    local maxViewActor = 60
    local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel = levelMgr.curLevel
    if curLevel then
        maxViewActor =  curLevel:GetMaxViewEnemyCount()
    end
    if self._view_actor > self._max_view_actor then
        character:SetViewActor(false)
        self._view_actor_queue[#self._view_actor_queue+1] = character
    else
        character:SetViewActor(true)
        self._view_actor = self._view_actor + 1
    end
end

function cysoldierssortie_mgr_actorInstance:DequeueActor(character)
    if character._view_actor == true then
        self._view_actor = self._view_actor - 1
        
        if #self._view_actor_queue <=0 then
            return
        end
        local actor = self._view_actor_queue[1]
        actor:SetViewActor(true)
        table.remove(self._view_actor_queue,1)
    else
        table.remove_value(self._view_actor_queue,character)
    end
end



return cysoldierssortie_mgr_actorInstance