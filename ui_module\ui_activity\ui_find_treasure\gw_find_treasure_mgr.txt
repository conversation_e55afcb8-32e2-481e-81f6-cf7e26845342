﻿-- 寻宝大作战

local require   = require
local ipairs    = ipairs
local pairs     = pairs
local table     = table
local string    = string
local tonumber  = tonumber
local lang = require "lang"

local game_scheme = require "game_scheme"
local gw_task_mgr = require "gw_task_mgr"
local net_activity_module = require "net_activity_module"
local event = require "event"
local gw_event_activity_define = require "gw_event_activity_define"
local flow_text = require "flow_text"
local error_code_pb = require "error_code_pb"


module("gw_find_treasure_mgr")


-- 是否请求活动基本信息
local isReqActData = false

local unreceiveRewardArr = {}
local receivedRewardArr = {}
local hadDrawCount = 0
local bigRewardIndex = 0
local isSaveBig = false
local currentLevel = 1
local hadDrawRewardIDDic = {}

-- 设置活动基础数据
function SetActData(msgData)
    if not msgData then return end
    isReqActData = true
    
    if msgData.arrUnreceiveReward then
        unreceiveRewardArr = {}
        for i, rewardID in ipairs(msgData.arrUnreceiveReward) do
            unreceiveRewardArr[i] = rewardID
        end
    end
    if msgData.arrReceiveReward then
        receivedRewardArr = {}
        for i, rewardID in ipairs(msgData.arrReceiveReward) do
            receivedRewardArr[i] = rewardID
        end
    end
    if msgData.arrGridRewardID then
        hadDrawRewardIDDic = {} --（1-12）的宫格里对应的rewardid，如果没有抽过则为0
        for i, rewardID in ipairs(msgData.arrGridRewardID) do
            hadDrawRewardIDDic[i] = rewardID
        end
    end
    if msgData.nTotalDrawCnt then
        hadDrawCount = msgData.nTotalDrawCnt
    end
    if msgData.nBigRewardGridID then
        bigRewardIndex = msgData.nBigRewardGridID -- 已选中的大奖对应的宫格id（1-3）
    end
    if msgData.nCurLayerID then
        currentLevel = msgData.nCurLayerID
    end
    if msgData.nDefaultSelect then
        isSaveBig = msgData.nDefaultSelect == 1
    end
    
    event.Trigger(gw_event_activity_define.GW_FIND_TREASURE_ACTIVITY_DATA)
end

-- 设置领取奖励结果
function SetReceiveAwardResult(resultData)
    if resultData.errorcode == error_code_pb.enErr_NoError then
        for _, data in pairs(unreceiveRewardArr) do
            table.insert(receivedRewardArr, data)
        end
        unreceiveRewardArr = {}
        
        event.Trigger(gw_event_activity_define.GW_FIND_TREASURE_RECEIVE_AWARD)
    else
        flow_text.Add(lang.Get(100000 + resultData.errorcode))
    end
end

-- 设置抽奖结果
function SetDrawResult(resultData)
    if resultData.errorcode == error_code_pb.enErr_NoError then
        if resultData.nResultRewardID and resultData.nResultGridIdx then
            local rewardID = resultData.nResultRewardID
            local hadDrawIndex = resultData.nResultGridIdx

            hadDrawRewardIDDic[hadDrawIndex] = rewardID

            -- 插入未领取
            table.insert(unreceiveRewardArr, rewardID)
            hadDrawCount = hadDrawCount + 1

            event.Trigger(gw_event_activity_define.GW_FIND_TREASURE_DRAW_AWARD, hadDrawIndex)
        end
    else
        flow_text.Add(lang.Get(100000 + resultData.errorcode))
    end
end

-- 设置大奖结果
function SetBigRewardResult(resultData)
    if resultData.errorcode == error_code_pb.enErr_NoError then
        if resultData.nBigRewardGridID then
            bigRewardIndex = resultData.nBigRewardGridID
        end
        if resultData.nDefaultSelect then
            isSaveBig = resultData.nDefaultSelect == 1
        end

        event.Trigger(gw_event_activity_define.GW_FIND_TREASURE_SELECT_BIG)
    else
        flow_text.Add(lang.Get(100000 + resultData.errorcode))
    end
end

-- 设置大奖下标
function SetBigIndex(bigIndex)
    bigRewardIndex = bigIndex
end

-- 设置是否保存大奖
function SetSaveBigIndex(isSave)
    isSaveBig = isSave
end

-- 设置进入下一层
function SetIntoNextLevel(maxLevel)
    if currentLevel < maxLevel then
        if not isSaveBig then
            bigRewardIndex = 0
            
            net_activity_module.MSG_GEAR_SUPPLY_SETREWARD_REQ(bigRewardIndex, isSaveBig)
        end
        currentLevel = currentLevel + 1
        hadDrawRewardIDDic = {}
    end
end

-- 获取抽奖层数
function GetLevel()
    return currentLevel
end

-- 获取已抽奖次数
function GetHadDrawCount()
    return hadDrawCount
end

-- 获取大奖下标
function GetBigRewardIndex()
    return bigRewardIndex
end

-- 获取是否保存
function GetSaveBig()
    return isSaveBig
end

-- 获取抽奖字典
function GetHadDrawRewardIDDic()
    return hadDrawRewardIDDic
end

-- 获取未领奖奖励数组
function GetUnReceiveRewardArray()
    return unreceiveRewardArr
end

-- 获取已领奖奖励数组
function GetReceivedRewardArray()
    return receivedRewardArr
end

-- 获取翻开宝箱个数
function GetOpenBoxNum()
    local count = 0
    for i, rewardID in pairs(hadDrawRewardIDDic) do
        if rewardID > 0 then
            count = count + 1
        end
    end
    
    return count
end

-- 清理数据
function ClearData()
    unreceiveRewardArr = {}
    receivedRewardArr = {}
    hadDrawCount = 0
    bigRewardIndex = 0
    isSaveBig = false
    currentLevel = 1
    hadDrawRewardIDDic = {}
end

function OnActivityUpdate(_, actID, isOpen, __, oldOpen)
    if not isOpen or isReqActData then return end
    
    local festival_activity_cfg = require "festival_activity_cfg"
    local cfg = festival_activity_cfg.GetActivityMainCfgByActivityID(actID)
    if cfg~=nil and cfg.headingCode == festival_activity_cfg.ActivityCodeType.FindTreasure then
        net_activity_module.MSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ()
    end
end

event.Register(event.USER_DATA_RESET, ClearData)
event.Register(gw_event_activity_define.GW_ONE_ACTIVITY_UPDATE, OnActivityUpdate)
