---@class troopclash_mgr_cam : fusion_mgrbase
---@field lifeScope troopclash_lifescope
local mgr = bc_Class("troopclash_mgr_cam", Fusion.MgrBase)
---@type troopclash_scene_mgr
mgr.sceneMgr = nil
---@type troopclash_camera
mgr.cameraCtrl = nil

---@type boolean
mgr.readyFlag = nil

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.sceneMgr = self.lifeScope:GetMgr("troopclash_scene_mgr")
end

function mgr:Ready()
    self.cameraCtrl = self.sceneMgr.cameraCtrl
    self.readyFlag = false
end

function mgr:GameStart()
    self.readyFlag = true
end

function mgr:__delete()
    self.readyFlag = false
end

function mgr:WorldToScreenPoint(positionWS)
    return self.cameraCtrl:WorldToScreenPoint(positionWS)
end

function mgr:ScreenToWorldPoint(positionSS)
    return self.cameraCtrl:ScreenToWorldPoint(positionSS)
end

function mgr:GetFarthestDistance()
    return math.huge, math.huge
end

return mgr
