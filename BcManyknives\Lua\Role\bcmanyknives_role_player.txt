local rolePlayer = TinyRush_CreateClass("bcmanyknives_role_player"):baseClass(require("bcmanyknives_rolebase_item"))

local vec2Zero = bc_CS_Vector2.zero

rolePlayer.collectRangeListener = nil
rolePlayer.collectRangeCollider = nil

rolePlayer.joystickUnReg = nil
rolePlayer.center = nil
rolePlayer.lastCtrlVel = nil
-- 回血特效
rolePlayer.fx_addHP = nil
-- 获得道具
rolePlayer.fx_getProp = nil
rolePlayer.fx_craze = nil
rolePlayer.fx_fleetfoot = nil
-- 狂暴
rolePlayer.crazeBind = nil
rolePlayer.crazeTimerBind = nil
-- 大宝剑
rolePlayer.bigSwordBind = nil
rolePlayer.bigSwordTimerBind = nil
-- 无敌效果Tween
rolePlayer.bigSowrdTween = nil
-- 飞毛腿
rolePlayer.fleetfootBind = nil
rolePlayer.fleetfootTimerBind = nil
-- 武器特效攻击
rolePlayer.bladeFxState = nil
rolePlayer.bladeFxFlag = nil
rolePlayer.bladeFxTimer = nil
rolePlayer.bladeFxTimerCD = nil
-- 记录发射次数
rolePlayer.bladeFxFlag2 = nil
rolePlayer.bladeFxTimer2 = nil
rolePlayer.bladeFxTimerCD2 = nil
-- 被击退Tween
rolePlayer.repulseTween = nil
rolePlayer.repulseFlag = nil
-- 刀刃的父对象
rolePlayer.bladeTmpPool = nil
rolePlayer.bladeTmpList = nil
-- 记录闪电技能id,每次只能造成一次伤害
rolePlayer.lightID = nil
-- 闪烁
local colYellow = bc_CS_Color(1, 1, 0, 1)
local colWhite = bc_CS_Color(1, 1, 1, 1)

-- 触碰到的敌人,key:roleBase  value: timer
rolePlayer.hurtByRolePair = nil
-- 碰到的敌人计数
rolePlayer.hurtByRoleCount = nil

function rolePlayer:ctor(...)
    self.__base:ctor(...)
    self.center = self.dataSrc["center"]
    self.fx_addHP = self.dataSrc["fx_huixue"]
    self.fx_getProp = self.dataSrc["fx_huoqudaoju"]
    self.fx_craze = self.dataSrc["fx_kuangbao"]
    self.fx_fleetfoot = self.dataSrc["fx_jiasu_trail"]
    local tinyrush_bindable = require("tinyrush_bindable")
    self.bigSwordBind = tinyrush_bindable.new(false)
    self.bigSwordTimerBind = tinyrush_bindable.new(0)
    self.crazeBind = tinyrush_bindable.new(false)
    self.crazeTimerBind = tinyrush_bindable.new(0)
    self.fleetfootBind = tinyrush_bindable.new(false)
    self.fleetfootTimerBind = tinyrush_bindable.new(0)
    self.collectRangeCollider = self.dataSrc["collectRangeCollider"]
    self.collectRangeCollider.gameObject.layer = ManyKnivesDefine.layerID.triggerPlayer
    ManyKnivesDefine.Func_IgnoreCollision(self.collider, self.collectRangeCollider)
    self.collectRangeListener = self.dataSrc["collectRangeListener"]
    -- 注册监听事件
    self.collectRangeListener:RegisterTriggerEnter2D(function(collider2D)
        self:CollectTriggerEnter2D(collider2D)
    end)
    local parentAndPrefab = self.dataSrc["bladeTmp"]
    self.bladeTmpPool =
        require("tinyrush_gopool").new(parentAndPrefab, parentAndPrefab, "bcmanyknives_bladeparent_item")
    self.bladeNumBind:register(function(value)
        if self.sceneMgr.startFlag and value >= ManyKnivesDefine.bladeMaxNum then
            self.sceneMgr.tutMgr:ShowBladeMaxGuide()
        end
    end)
end

function rolePlayer:Init(...)
    self.__base:Init(...)
    self:MoveListener(vec2Zero)
    self.fx_addHP.gameObject:SetActive(false)
    self.fx_getProp.gameObject:SetActive(false)
    self.fx_craze.gameObject:SetActive(false)
    self.fx_fleetfoot.gameObject:SetActive(false)
    self.bigSwordBind:setValue(false, true)
    self.bigSwordTimerBind:setValue(0, true)
    self.crazeBind:setValue(false, true)
    self.crazeTimerBind:setValue(0, true)
    self.fleetfootBind:setValue(false, true)
    self.fleetfootTimerBind:setValue(0, true)
    self.bladeTmpPool:clear()
    self.bladeTmpList = {}
    self:InitBlade(self.roleData.bladeType, self.roleData.bladeNum)
    self:KillBigSowrdTween()
    self:SetFillColor(colWhite)
    self.collectRangeCollider.radius = ManyKnivesDefine.playerCollectRange
    self.collectRangeCollider.enabled = false
    self.repulseFlag = false
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.idle)
    self.hurtByRolePair = {}
    self.hurtByRoleCount = 0
    self.lightID = 0
end

--- 玩家刀刃重载
function rolePlayer:InitBlade(bladeType, bladeNum)
    self.bladeList = {}
    self.curBladeType = bladeType
    self.bladeNumBind:setValue(bladeNum)
    local rotation = 360 / bladeNum * ManyKnivesDefine.bladeSide
    for i = 1, bladeNum do
        local deg = (i - 1) * rotation
        local tmpParent = self.bladeTmpPool:popOne()
        tmpParent.gameObject:SetActive(true)
        tmpParent.transform:SetParent(self.bladeTran)
        tmpParent.transform.localPosition = bc_CS_Vector3(ManyKnivesDefine.bladeRadius * math.cos(math.rad(deg)),
            ManyKnivesDefine.bladeRadius * math.sin(math.rad(deg)), 0)
        tmpParent.transform.localRotation = bc_CS_Quaternion.Euler(0, 0, deg - 90)
        tmpParent.transform.localScale = bc_CS_Vector3.one
        local tmpGO = self.sceneMgr:BladePoolPopOne(bladeType)
        tmpGO:Init(self.sceneMgr, bladeType, self)
        tmpGO:SetParentIndex(tmpParent, 1)
        tmpParent:Init(self.sceneMgr, tmpGO)
        tmpParent:SetIndex(i)
        self.bladeTmpList[i] = tmpParent
        self.bladeList[tmpGO.gameObject] = tmpGO
    end
end

function rolePlayer:CollectTriggerEnter2D(collider2D)
    if self.deadFlag or self.sceneMgr.overFlag then
        return
    end
    local splitStrs = string.bc_split(collider2D.name, ManyKnivesDefine.names.split)
    local type = tonumber(splitStrs[1])
    -- 判断是否可以捡刀刃
    if type == ManyKnivesDefine.triggerType.blade then
        local targetClass = self.sceneMgr.bladePairWithGO[collider2D.gameObject]
        if targetClass == nil then
            return
        end
        if not targetClass.valid and targetClass.propFlag and self.bladeNumBind.value + 1 <=
            ManyKnivesDefine.bladeMaxNum then
            self:CollectBlade(targetClass)
            self.sceneMgr.audioMgr:PlayKnifePickSound()
        end
    end
end

function rolePlayer:OnTriggerExit2D(collider2D)
    self.__base:OnTriggerExit2D(collider2D)
    if self.deadFlag or self.sceneMgr.overFlag then
        return
    end
    local splitStrs = string.bc_split(collider2D.name, ManyKnivesDefine.names.split)
    local type = tonumber(splitStrs[1])
    local targetClass = nil
    local targetObj = collider2D.gameObject
    if type == ManyKnivesDefine.triggerType.role then
        if self.hurtByRolePair[targetObj] ~= nil then
            self.hurtByRolePair[targetObj] = nil
            self.hurtByRoleCount = self.hurtByRoleCount - 1
        end
    end
end

function rolePlayer:OnTriggerEnter2D(collider2D)
    self.__base:OnTriggerEnter2D(collider2D)
    if self.deadFlag or self.sceneMgr.overFlag then
        return
    end
    local splitStrs = string.bc_split(collider2D.name, ManyKnivesDefine.names.split)
    local type = tonumber(splitStrs[1])
    local targetClass = nil
    local targetObj = collider2D.gameObject
    -- 身体碰到了道具,道具对主角生效
    if type == ManyKnivesDefine.triggerType.prop then
        targetClass = self.sceneMgr.propPairWithGO[targetObj]
        -- 触发道具：目前只有主角用，主角已重载
        self:PropTrigger(targetClass:Trigger())
    elseif type == ManyKnivesDefine.triggerType.role then
        if self.hurtByRolePair[targetObj] == nil then
            self.hurtByRolePair[targetObj] = ManyKnivesDefine.roleDebuffConfig.hurtByRoleCD
            self.hurtByRoleCount = self.hurtByRoleCount + 1
        end
    end
    if not self.invincible then
        -- 碰到了特效
        if type == ManyKnivesDefine.triggerType.effect then
            local dmg = nil
            local effectType = tonumber(splitStrs[3])
            targetClass = self.sceneMgr.effectPairWithGO[targetObj]
            -- 只有玩家生效的特效
            -- 敌人发射的球
            if effectType == ManyKnivesDefine.effectType.enemyBall then
                targetClass:Trigger()
                dmg = targetClass.roleBase.roleData.skillDmg
                self:RoleTrigger(targetClass.roleBase.transform.position, dmg)
            elseif effectType == ManyKnivesDefine.effectType.bossCircle then
                targetClass:Trigger()
                dmg = targetClass.roleBase.circleBossDmg
                self.sceneMgr:PlayerRepulse(targetClass.roleBase.transform.position, 10)
                self:RoleTrigger(targetClass.roleBase.transform.position, dmg)
            end
        end
    end
end

function rolePlayer:Start()
    self.__base:Start()
    -- 移动控制监听
    self.joystickUnReg = self.uiMgr.uiCtrl.joystickBindable:register(function(value)
        self:MoveListener(value)
    end)
    self.bigSwordBind:invokeEvent()
    self.bigSwordTimerBind:invokeEvent()
    self.crazeBind:invokeEvent()
    self.crazeTimerBind:invokeEvent()
    self.fleetfootBind:invokeEvent()
    self.fleetfootTimerBind:invokeEvent()
    self.collectRangeCollider.enabled = true
    self:InitBladeSkill()
end

function rolePlayer:PauseListener(pause)
    self.__base:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.repulseTween ~= nil then
        self.repulseTween.timeScale = timeScale
    end
end

-- 被击退
function rolePlayer:PlayerRepulse(pos)
    self:KillRepulseTween()
    self.repulseFlag = true
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.idle)
    self.repulseTween = self.transform:DOMove(pos, 0.5):SetEase(ManyKnivesDefine.Ease.OutQuad)
    self.repulseTween:OnComplete(function()
        self.repulseFlag = false
    end)
    self:RefreshPause()
end

function rolePlayer:KillRepulseTween()
    if self.repulseTween ~= nil then
        self.repulseTween:Kill()
        self.repulseTween = nil
    end
end

function rolePlayer:KillBigSowrdTween()
    if self.bigSowrdTween ~= nil then
        self.bigSowrdTween:Kill()
        self.bigSowrdTween = nil
    end
end

function rolePlayer:MoveListener(value)
    if self.deadFlag then
        return
    end
    self.lastCtrlVel = value
end

function rolePlayer:OnFixedUpdate(deltaTime)
    if self.deadFlag or not self.sceneMgr.startFlag then
        return
    end
    -- 控制移动
    if not self.deadFlag and not self.repulseFlag and self.lastCtrlVel ~= nil then
        local tmpPos = self.sceneMgr.cameraCtrl:LimitPosInMap(
            self.rigidbody.position + self.lastCtrlVel * (self.moveSpBind.value * deltaTime))
        -- self.transform.position = bc_CS_Vector3(tmpPos.x, tmpPos.y, 0)
        self.rigidbody:MovePosition(tmpPos)
        if self.lastCtrlVel.x ~= 0 then
            self:SetDisplayFlip(self.lastCtrlVel.x < 0)
        end
        if self.lastCtrlVel.x ~= 0 or self.lastCtrlVel.y ~= 0 then
            self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        else
            self.animCtrl:Play(ManyKnivesDefine.AnimatorState.idle)
        end
    end
end

function rolePlayer:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    if self.deadFlag or not self.sceneMgr.startFlag then
        return
    end
    -- 碰到敌人身体伤害
    if not self.invincible and self.hurtByRoleCount > 0 then
        for k, v in pairs(self.hurtByRolePair) do
            v = v + deltaTime
            if v >= ManyKnivesDefine.roleDebuffConfig.hurtByRoleCD then
                v = 0
                local dmg = nil
                local targetClass = self.sceneMgr.rolePairWithGO[k]
                if targetClass ~= nil then
                    -- 冲刺中属于技能伤害
                    if targetClass.rushFlag then
                        -- Boss技能伤害特殊配置
                        if targetClass.isBoss then
                            dmg = targetClass.rushBossDmg
                        else
                            dmg = targetClass.roleData.skillDmg
                        end
                    else
                        dmg = targetClass.roleData.bodyDmg
                    end
                    self:RoleTrigger(targetClass.transform.position, dmg)
                end
            end
            self.hurtByRolePair[k] = v
        end
    end

    -- 狂暴状态，道具旋转速度增加50%（暂定），道具技能施放速度和数量增加50%，
    if self.crazeBind.value then
        self.crazeTimerBind:setValue(self.crazeTimerBind.value + deltaTime)
        if self.crazeTimerBind.value >= ManyKnivesDefine.playerEffect_Config.CD_craze then
            self.crazeBind:setValue(false)
            self.bladeSpBind:setValue(ManyKnivesDefine.bladeInitSpeed)
            self.fx_craze.gameObject:SetActive(false)
        end
    end
    -- 大宝剑状态
    if self.bigSwordBind.value then
        self.bigSwordTimerBind:setValue(self.bigSwordTimerBind.value + deltaTime)
        if self.bigSwordTimerBind.value >= ManyKnivesDefine.playerEffect_Config.CD_bigSword then
            self.bigSwordBind:setValue(false)
            self.fx_craze.transform.localScale = bc_CS_Vector3.one * ManyKnivesDefine.playerEffect_Config.fx_craze_size
            self.collectRangeCollider.radius = ManyKnivesDefine.playerCollectRange
            self.invincible = false
            self:KillBigSowrdTween()
            self:SetFillColor(colWhite, false)
            self:SetFillPhase(0)
            for _, v in pairs(self.bladeList) do
                v:SetScale(1)
            end
        end
    end
    -- 飞毛腿
    if self.fleetfootBind.value then
        self.fleetfootTimerBind:setValue(self.fleetfootTimerBind.value + deltaTime)
        if self.fleetfootTimerBind.value >= ManyKnivesDefine.playerEffect_Config.CD_fleetfoot then
            self.fleetfootBind:setValue(false)
            self.curMoveSp = self.roleData.speed
            self.moveSpBind:setValue(self.curMoveSp)
            self.fx_fleetfoot.gameObject:SetActive(false)
        end
    end
    -- 武器特效攻击
    if self.bladeFxFlag then
        if self.crazeBind.value then
            self.bladeFxTimer = self.bladeFxTimer + deltaTime * ManyKnivesDefine.playerEffect_Dmg.crazeMulti
        else
            self.bladeFxTimer = self.bladeFxTimer + deltaTime
        end
        -- 冰冻
        if self.curBladeType == ManyKnivesDefine.propType.blade_snow then
            -- 几秒发射一次子弹
            if self.bladeFxTimer >= self.bladeFxTimerCD then
                self.bladeFxTimer = 0
                self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.diski_firlat)
                -- 每次往一个方向发射4个子弹
                local firNum = ManyKnivesDefine.playerEffect_Config.snow_Num
                if self.crazeBind.value then
                    firNum = math.floor(firNum * ManyKnivesDefine.playerEffect_Dmg.crazeMulti)
                end
                local startPos = self.bladeTran.position
                local rotation = 360 / (firNum * 4)
                local offDeg = (self.bladeFxFlag2 % 4) * 90
                self.bladeFxFlag2 = self.bladeFxFlag2 + 1
                for i = 1, firNum, 1 do
                    local iceBall = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_snow01)
                    iceBall:GetColliderKey().name =
                        ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.effectType.snow
                    iceBall:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_snow01, self)
                    local deg = i * rotation + offDeg
                    local x = math.cos(math.rad(deg))
                    local y = math.sin(math.rad(deg))
                    local dir = bc_CS_Vector3(x, y, 0).normalized
                    iceBall:Play(1, startPos, dir, ManyKnivesDefine.playerEffect_Config.snow_FlyLimit,
                        ManyKnivesDefine.playerEffect_Config.snow_FlySp, -90)
                end
            end
            -- 火球
        elseif self.curBladeType == ManyKnivesDefine.propType.blade_fire then
            -- 几秒发射一次子弹
            if self.bladeFxTimer >= self.bladeFxTimerCD then
                -- 发射一圈。
                local firNum = ManyKnivesDefine.playerEffect_Config.fire_Num
                if self.crazeBind.value then
                    firNum = math.floor(firNum * ManyKnivesDefine.playerEffect_Dmg.crazeMulti)
                end
                if self.crazeBind.value then
                    self.bladeFxTimer2 = self.bladeFxTimer2 + deltaTime * ManyKnivesDefine.playerEffect_Dmg.crazeMulti
                else
                    self.bladeFxTimer2 = self.bladeFxTimer2 + deltaTime
                end
                if self.bladeFxTimer2 >= self.bladeFxTimerCD2 then
                    if self.bladeFxFlag2 < 1 then
                        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.character_fireball)
                    end
                    self.bladeFxTimer2 = 0
                    local startPos = self.bladeTran.position
                    local rotation = 360 / firNum
                    local offDeg = (self.bladeFxFlag2 % firNum) * rotation
                    local fireBall = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_fire01)
                    fireBall:GetColliderKey().name =
                        ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.effectType.fire
                    fireBall:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_fire01, self)
                    local x = math.cos(math.rad(offDeg))
                    local y = math.sin(math.rad(offDeg))
                    local dir = bc_CS_Vector3(x, y, 0).normalized
                    fireBall:Play(1, startPos + dir, dir, 20, 10, -90)

                    self.bladeFxFlag2 = self.bladeFxFlag2 + 1
                    if self.bladeFxFlag2 >= firNum then
                        self.bladeFxTimer = 0
                        self.bladeFxFlag2 = 0
                    end
                end
            end
            -- 瘴气
        elseif self.curBladeType == ManyKnivesDefine.propType.blade_miasma then
            if self.bladeFxTimer >= self.bladeFxTimerCD then
                self.bladeFxTimer = 0
                local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_miasma)
                fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_miasma, self)
                fx:GetColliderKey().name = ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                                               ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                                               ManyKnivesDefine.effectType.miasma
                fx:Play(3, self.transform.position, ManyKnivesDefine.playerEffect_Config.miasma_Stay)
            end
            -- 闪电
        elseif self.curBladeType == ManyKnivesDefine.propType.blade_lightning then
            if self.bladeFxTimer >= self.bladeFxTimerCD then
                self.lightID = self.lightID + 1
                self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.lightning)
                self.bladeFxTimer = 0
                local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_lightning01)
                fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_lightning01, self)
                fx:GetColliderKey().name = ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                                               ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                                               ManyKnivesDefine.effectType.lightning
                local scale = ManyKnivesDefine.playerEffect_Config.lightning_raduis
                if self.crazeBind.value then
                    scale = scale * ManyKnivesDefine.playerEffect_Dmg.crazeMulti
                end
                fx:Play(2, self.effectParent, scale, ManyKnivesDefine.playerEffect_Config.lightning_Dur, self.lightID)
            end
        end
    end
end

function rolePlayer:AddBladeForce(parent)
    local newBlade = self.sceneMgr:BladePoolPopOne(self.curBladeType)
    newBlade:Init(self.sceneMgr, self.curBladeType, self)
    newBlade:SetParentIndex(parent, self.bigSwordBind.value and ManyKnivesDefine.playerEffect_Config.bigSwordMulti or 1)
    parent:Init(self.sceneMgr, newBlade)
    self.bladeList[newBlade.gameObject] = newBlade
end
--- 捡起来刀刃
function rolePlayer:CollectBlade(tarBlade)
    local newNum = self.bladeNumBind.value + 1
    if newNum > ManyKnivesDefine.bladeMaxNum then
        newNum = ManyKnivesDefine.bladeMaxNum
        tarBlade:PushInPool()
        return
    end
    self.sceneMgr:ReduceBlade(tarBlade)
    self:repairBladeCicle(newNum)
    local tmpScaleMul = self.bigSwordBind.value and ManyKnivesDefine.playerEffect_Config.bigSwordMulti or 1
    -- 生成新刀刃到后面
    local rotation = 360 / newNum
    local deg = (newNum - 1) * rotation
    local tmpParent = nil
    if newNum > #self.bladeTmpList then
        tmpParent = self.bladeTmpPool:popOne()
        tmpParent.gameObject:SetActive(true)
        tmpParent.transform:SetParent(self.bladeTran)
        self.bladeTmpList[newNum] = tmpParent
    else
        tmpParent = self.bladeTmpList[newNum]
    end
    tmpParent.transform.localPosition = bc_CS_Vector3(ManyKnivesDefine.bladeRadius * math.cos(math.rad(deg)),
        ManyKnivesDefine.bladeRadius * math.sin(math.rad(deg)), 0)
    tmpParent.transform.localRotation = bc_CS_Quaternion.Euler(0, 0, deg - 90)
    tmpParent.transform.localScale = bc_CS_Vector3.one
    tmpParent:Init(self.sceneMgr, tarBlade)
    tmpParent:SetIndex(newNum)
    -- 新刀刃靠近哪里
    local angleOff = bc_CS_Vector3.Angle((tarBlade.transform.position - self.bladeTran.position), self.bladeTran.right)
    local offBladeAngle = self.bladeTran.eulerAngles.z
    tarBlade:FlyInRole(self, tmpParent, angleOff, deg, tmpScaleMul)
    self.bladeList[tarBlade.gameObject] = tarBlade
    self.bladeNumBind:setValue(newNum)
end
-- 整理新的扇形
function rolePlayer:repairBladeCicle(newNum)
    -- 整理出一个扇形
    local rotation = 360 / newNum
    -- 有新刀刃加入,先整理旧刀刃
    if newNum > self.bladeNumBind.value then
        local curBladeTmpNum = #self.bladeTmpList
        local newTmpList = {}
        local tmpI = 0
        for i = 1, curBladeTmpNum, 1 do
            if self.bladeTmpList[i]:IsEmpty() then
                -- 空父物体直接回收
                self.bladeTmpPool:pushOne(self.bladeTmpList[i])
            else
                local deg = tmpI * rotation
                self.bladeTmpList[i]:ResetRotation(
                    bc_CS_Vector3(ManyKnivesDefine.bladeRadius * math.cos(math.rad(deg)),
                        ManyKnivesDefine.bladeRadius * math.sin(math.rad(deg)), 0),
                    bc_CS_Quaternion.Euler(0, 0, deg - 90))
                tmpI = tmpI + 1
                newTmpList[tmpI] = self.bladeTmpList[i]
                newTmpList[tmpI]:SetIndex(tmpI)
            end
        end
        self.bladeTmpList = newTmpList
    end
end
function rolePlayer:addBlade(newBladetype, num)
    -- 每次增加几个
    local newNum = nil
    if num ~= nil then
        newNum = self.bladeNumBind.value + num
    else
        if newBladetype == ManyKnivesDefine.propType.blade_default2 then
            newNum = self.bladeNumBind.value + ManyKnivesDefine.newBladeNumEnemy
        else
            newNum = self.bladeNumBind.value + ManyKnivesDefine.newBladeNum
        end
    end
    if newNum > ManyKnivesDefine.bladeMaxNum then
        newNum = ManyKnivesDefine.bladeMaxNum
    end
    local changeFlag = self.curBladeType ~= newBladetype and newBladetype ~= ManyKnivesDefine.propType.blade_default and
                           newBladetype ~= ManyKnivesDefine.propType.blade_default2
    -- 新刀刃不同，需要回收旧的。如果是默认刀刃，不回收
    if changeFlag then
        for _, v in pairs(self.bladeList) do
            v:Reduce()
        end
        self.bladeList = {}
        self.bladeNumBind:setValue(0, true)
        self.curBladeType = newBladetype
    end
    self:repairBladeCicle(newNum)
    local tmpScaleMul = self.bigSwordBind.value and ManyKnivesDefine.playerEffect_Config.bigSwordMulti or 1
    -- 生成新刀刃到后面
    local rotation = 360 / newNum
    for i = self.bladeNumBind.value + 1, newNum, 1 do
        local deg = (i - 1) * rotation
        local tmpParent = nil
        if i > #self.bladeTmpList then
            tmpParent = self.bladeTmpPool:popOne()
            tmpParent.gameObject:SetActive(true)
            tmpParent.transform:SetParent(self.bladeTran)
            self.bladeTmpList[i] = tmpParent
        else
            tmpParent = self.bladeTmpList[i]
        end
        tmpParent.transform.localPosition = bc_CS_Vector3(ManyKnivesDefine.bladeRadius * math.cos(math.rad(deg)),
            ManyKnivesDefine.bladeRadius * math.sin(math.rad(deg)), 0)
        tmpParent.transform.localRotation = bc_CS_Quaternion.Euler(0, 0, deg - 90)
        tmpParent.transform.localScale = bc_CS_Vector3.one
        local tmpGO = self.sceneMgr:BladePoolPopOne(self.curBladeType)
        tmpGO:Init(self.sceneMgr, self.curBladeType, self)
        tmpGO:SetParentIndex(tmpParent, tmpScaleMul)
        tmpParent:Init(self.sceneMgr, tmpGO)
        tmpParent:SetIndex(i)
        self.bladeList[tmpGO.gameObject] = tmpGO
    end
    self.bladeNumBind:setValue(newNum)
    -- 改变了刀刃类型，重新计数
    if changeFlag then
        self:InitBladeSkill()
    end
end

function rolePlayer:InitBladeSkill()
    self.bladeFxFlag = false
    if self.curBladeType == ManyKnivesDefine.propType.blade_snow then
        self.bladeFxFlag = true
        self.bladeFxTimerCD = ManyKnivesDefine.playerEffect_Config.CD_snow
    elseif self.curBladeType == ManyKnivesDefine.propType.blade_fire then
        self.bladeFxFlag = true
        self.bladeFxTimerCD = ManyKnivesDefine.playerEffect_Config.CD_fire
        self.bladeFxTimerCD2 = ManyKnivesDefine.playerEffect_Config.CD_fire2
    elseif self.curBladeType == ManyKnivesDefine.propType.blade_miasma then
        self.bladeFxFlag = true
        self.bladeFxTimerCD = ManyKnivesDefine.playerEffect_Config.CD_miasma
    elseif self.curBladeType == ManyKnivesDefine.propType.blade_lightning then
        self.bladeFxFlag = true
        self.bladeFxTimerCD = ManyKnivesDefine.playerEffect_Config.CD_lightning
    end
    if self.bladeFxFlag then
        self.bladeFxState = 1
        self.bladeFxTimer = self.bladeFxTimerCD
        self.bladeFxFlag2 = 0
        self.bladeFxTimer2 = self.bladeFxTimerCD2
    end
end

---触发道具
function rolePlayer:PropTrigger(propType)
    if propType == ManyKnivesDefine.propType.blade_default then
        self:addBlade(ManyKnivesDefine.propType.blade_default)
        self.sceneMgr.audioMgr:PlayKnifePickSound()
    elseif propType == ManyKnivesDefine.propType.blade_default2 then
        self:addBlade(ManyKnivesDefine.propType.blade_default2)
        self.sceneMgr.audioMgr:PlayKnifePickSound()
    elseif propType == ManyKnivesDefine.propType.blade_snow then
        self:addBlade(ManyKnivesDefine.propType.blade_snow)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
        self.sceneMgr.tutMgr:ShowPropTopGuide_snow()
    elseif propType == ManyKnivesDefine.propType.blade_fire then
        self:addBlade(ManyKnivesDefine.propType.blade_fire)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
        self.sceneMgr.tutMgr:ShowPropTopGuide_Fire()
    elseif propType == ManyKnivesDefine.propType.blade_miasma then
        self:addBlade(ManyKnivesDefine.propType.blade_miasma)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
        self.sceneMgr.tutMgr:ShowPropTopGuide_miasma()
    elseif propType == ManyKnivesDefine.propType.blade_lightning then
        self:addBlade(ManyKnivesDefine.propType.blade_lightning)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
        self.sceneMgr.tutMgr:ShowPropTopGuide_light()
    elseif propType == ManyKnivesDefine.propType.craze then
        self.crazeBind:setValue(true)
        self.fx_craze.gameObject:SetActive(true)
        self.fx_craze.transform.localScale = bc_CS_Vector3.one *
                                                 ((self.bigSwordBind.value and
                                                     ManyKnivesDefine.playerEffect_Config.bigSwordMulti or 1) *
                                                     ManyKnivesDefine.playerEffect_Config.fx_craze_size)
        self.crazeTimerBind:setValue(0)
        self.bladeSpBind:setValue(ManyKnivesDefine.bladeInitSpeed *
                                      ManyKnivesDefine.playerEffect_Config.craze_bladeSpeedMulti)
        if self.bladeNumBind.value < 1 then
            self:addBlade(self.curBladeType, ManyKnivesDefine.newBladeNum)
        end
        self.sceneMgr.tutMgr:ShowPropTopGuide_Craze()
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
    elseif propType == ManyKnivesDefine.propType.bigSword then
        for _, v in pairs(self.bladeList) do
            v:SetScale(ManyKnivesDefine.playerEffect_Config.bigSwordMulti)
        end
        self.collectRangeCollider.radius = ManyKnivesDefine.playerCollectRange *
                                               ManyKnivesDefine.playerEffect_Config.bigSwordMulti
        self.fx_craze.transform.localScale = bc_CS_Vector3.one * ManyKnivesDefine.playerEffect_Config.bigSwordMulti *
                                                 ManyKnivesDefine.playerEffect_Config.fx_craze_size
        self.bigSwordBind:setValue(true)
        self.invincible = true
        self.bigSwordTimerBind:setValue(0)
        self:KillBigSowrdTween()
        self:SetFillColor(colYellow, false)
        self.bigSowrdTween = ManyKnivesDefine.DOVirtual.Float(1, 0, 0.25, function(value)
            self:SetFillPhase(value)
        end):SetLoops(-1, ManyKnivesDefine.LoopType.Yoyo)
        self.sceneMgr.tutMgr:ShowPropTopGuide_bigSword()
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.Invincible)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
    elseif propType == ManyKnivesDefine.propType.moveSp then
        self.fleetfootBind:setValue(true)
        self.fx_fleetfoot.gameObject:SetActive(true)
        self.fleetfootTimerBind:setValue(0)
        self.curMoveSp = self.roleData.speed * ManyKnivesDefine.playerEffect_Config.moveSpMulti
        self.moveSpBind:setValue(self.curMoveSp)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.accelerate)
        self.sceneMgr.tutMgr:ShowPropTopGuide_Fleetfoot()
    elseif propType == ManyKnivesDefine.propType.heart then
        self.hpValueBind:setValue(self.hpMaxBind.value)
        self.fx_addHP:Simulate(0, true)
        self.fx_addHP:Play()
        self.fx_addHP.gameObject:SetActive(true)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.pick_props)
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.return_blood)
        self.sceneMgr.tutMgr:ShowPropTopGuide_HP()
    end
    self.fx_getProp:Simulate(0, true)
    self.fx_getProp:Play()
    self.fx_getProp.gameObject:SetActive(true)
end

function rolePlayer:dispose()
    self:recycle()
    self.__base:dispose()
end

function rolePlayer:recycle()
    self.__base:recycle()
    if self.joystickUnReg then
        self.joystickUnReg:unRegister()
        self.joystickUnReg = nil
    end
    self:KillRepulseTween()
end

function rolePlayer:Stop()
    self.__base:Stop()
    self:recycle()
end

return rolePlayer
