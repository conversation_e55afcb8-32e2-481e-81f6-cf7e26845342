---@class fusion_mono : fusion_mgrbase Mono调度器
local mono = bc_Class("fusion_mono", Fusion.MgrBase)
-- 全局用的Mono
mono.globalMonoGO = nil
mono.luaContainer = nil
mono.unityTime = nil
---@type boolean
mono.readyFlag = nil
---@type fun(lifeScope:fusion_lifescope,funcType:fusion_MonoLoopType,deltaTime:number)
mono.monoRun = nil
mono.errorKey = nil
---@type boolean 使用pcall模式运行Mono
mono.protectedMode = nil

local event = require "event"

function mono:__init(...)
    local pMode = ...
    self.protectedMode = type(pMode) == "boolean" and pMode or false

    self.globalMonoGO = Fusion.CS.GameObject(mono.__cname)
    Fusion.CS.GameObject.DontDestroyOnLoad(self.globalMonoGO)
    self.luaContainer = self.globalMonoGO:AddComponent(typeof(CS.bc.MiniGameBase.LuaContainer))
    self.luaContainer:BindLuaGameObject(self)
    self.readyFlag = false
    self.errorKey = {}
end

--- 析构函数
function mono:__delete()
    self.readyFlag = false
    Fusion.CS.GameObject.Destroy(self.globalMonoGO)
    self.globalMonoGO = nil
    self.luaContainer = nil
    self.unityTime = nil
end

function mono:InitMonoRun(monoRun)
    self.monoRun = monoRun
    self.unityTime = CS.UnityEngine.Time
    self.readyFlag = true

    self.TriggerUpdate = function()
        self.monoRun(self.lifeScope, Fusion.MonoLoopType.OnUpdate, self.unityTime.deltaTime)
    end
    self.TriggerFixedUpdate = function()
        self.monoRun(self.lifeScope, Fusion.MonoLoopType.OnFixedUpdate, self.unityTime.fixedDeltaTime)
    end
    self.TriggerLateUpdate = function()
        self.monoRun(self.lifeScope, Fusion.MonoLoopType.OnLateUpdate, self.unityTime.deltaTime)
    end
    self.OnErrorCallBackUpdate = function(msg)
        self._OnErrorCallBack(msg, "Update")
    end
    self.OnErrorCallBackFixedUpdate = function(msg)
        self._OnErrorCallBack(msg, "FixedUpdate")
    end
    self.OnErrorCallBackLateUpdate = function(msg)
        self._OnErrorCallBack(msg, "LateUpdate")
    end
end

function mono._OnErrorCallBack(msg, loopType)
    if self.errorKey[msg] then
        return
    end
    self.errorKey[msg] = msg
    bc_Logger.Error("fusion_mono Update Error：" .. msg)
    event.Trigger(event.GAME_EVENT_REPORT, "lua_err", {
        type = "MiniGame_" .. loopType,
        err = msg .. debug.traceback()
    })
end

---------------Unity生命周期------------------
function mono.OnEnable(self)
end

function mono.OnDisable(self)
end

function mono.OnDestroy(self)
end

function mono.Update(self)
    if not self.readyFlag then
        return
    end
    if self.protectedMode then
        xpcall(self.TriggerUpdate, self.OnErrorCallBackUpdate)
    else
        self.TriggerUpdate()
    end
end

function mono.FixedUpdate(self)
    if not self.readyFlag then
        return
    end
    if self.protectedMode then
        xpcall(self.TriggerFixedUpdate, self.OnErrorCallBackFixedUpdate)
    else
        self.TriggerFixedUpdate()
    end
end

function mono.LateUpdate(self)
    if not self.readyFlag then
        return
    end
    if self.protectedMode then
        xpcall(self.TriggerLateUpdate, self.OnErrorCallBackLateUpdate)
    else
        self.TriggerLateUpdate()
    end
end

-------------------

return mono
