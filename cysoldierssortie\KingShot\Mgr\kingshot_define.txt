---@class kingshot_define
local define = {}

define.ApiHelper = CS.XLuaUtil.LuaApiHelper
define.event = require "event"
define.game_scheme = require "game_scheme"
define.minigame_mgr = require "minigame_mgr"
define.minigame_buff_mgr = require "minigame_buff_mgr"
define.TweenEase = CS.DG.Tweening.Ease
define.DOTween = CS.DG.Tweening.DOTween
define.DOVirtual = CS.DG.Tweening.DOVirtual
define.Physics = CS.UnityEngine.Physics
define.NavMesh = CS.UnityEngine.AI.NavMesh
define.IgnoreCollision = define.Physics.IgnoreCollision
define.LookAtTargetSystem = CS.cysoldierssortie.LookAtTargetSystem

define.CS = {
    GameObject = CS.UnityEngine.GameObject,
    Vector3 = CS.UnityEngine.Vector3,
    Vector2 = CS.UnityEngine.Vector2,
    Quaternion = CS.UnityEngine.Quaternion,
    NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame,
    Color = CS.UnityEngine.Color,
}

define.GetTransformPositionXYZ = define.ApiHelper.GetTransformPositionXYZ
define.SetTransformPositionXYZ = define.ApiHelper.SetTransformPositionXYZ
define.SetTransformPositionAndRotation = define.ApiHelper.SetTransformPositionAndRotation
define.SetTransformLocalPositionAndLocalRotation = define.ApiHelper.SetTransformLocalPositionAndLocalRotation
define.SetTransformLocalScale = define.ApiHelper.SetTransformLocalScale

define.TypeOf = {
    NeeReferCollection = typeof(CS.CasualGame.lib_ChuagnYi.NeeG.NeeReferCollection),
    LuaMono = typeof(CS.CasualGame.lib_ChuagnYi.LuaMono),
    LuaMonoEvent = typeof(CS.CasualGame.lib_ChuagnYi.MonoFunc.LuaMonoEvent),
    UISlider = typeof(CS.UnityEngine.UI.Slider),
    UIText = typeof(CS.UnityEngine.UI.Text),
    UIImage = typeof(CS.UnityEngine.UI.Image),
    BakedPath = typeof(CS.cysoldierssortie.PathTool.BakedPath),
}

define.CacheVector3 = {
    Zero = define.CS.Vector3.zero,
    One = define.CS.Vector3.one,
    Forward = define.CS.Vector3.forward,
    Up = define.CS.Vector3.up,
}

define.CacheVector2 = {
    Zero = define.CS.Vector2.zero,
}

define.CacheColor = {
    Black = define.CS.Color.black,
    White = define.CS.Color.white,
    Red = define.CS.Color.red,
}

define.AbPath = {
    
    NeeGameEntry = "cysoldierssortie/kingshot/prefab/neegameentry_kingshot.prefab",
    MainScene = "cysoldierssortie/kingshot/prefab/mainscene.prefab",
    MainPanel = "cysoldierssortie/kingshot/prefab/mainpanel.prefab",
    HeroAnimator = "cysoldierssortie/kingshot/art/heroanimation/heroanimator.controller",
    LevelJsonFormat = "cysoldierssortie/kingshot/data/level/level%d.data",
    MapPrefabFormat = "cysoldierssortie/kingshot/prefab/maps/%s.prefab",
    NavMeshFormat = "cysoldierssortie/kingshot/prefab/maps/%s.asset",
    DataPair = "cysoldierssortie/kingshot/prefab/datapair.prefab",
    PrefabPair = "cysoldierssortie/kingshot/prefab/prefabpair.prefab",
    PropPrefab = "cysoldierssortie/kingshot/prefab/prop.prefab",
    PathPrefab = "cysoldierssortie/kingshot/prefab/path/bakepath1.prefab", --导航路径
}

---@class kingshot_TeamSpawnType
define.TeamSpawnType = {
    Pos = "Pos",     --固定位置刷出，不受时间影响
    Timer = "Timer", --跟随关卡时间刷出
}

define.Params = {
    PlayerMoveSpeed = 20,       --玩家小队移动速度
    PlayerSearchRange = 10,    --每个玩家单位索敌范围
    EnemySearchRange = 100000, -- 每个敌人单位索敌范围
    PlayerAtkRange = 10,       --玩家单位攻击范围
    MaxViewActor = math.huge,  --可显示的最大敌人数量
}

function define.Func_GetDistance(x1, z1, x2, z2)
    local num = x1 - x2
    local num2 = z1 - z2
    return math.sqrt(num * num + num2 * num2)
end

return define
