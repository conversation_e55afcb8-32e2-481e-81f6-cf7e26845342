---@class fusion_gopoolitem
local poolItem = bc_Class("fusion_gopoolitem")
poolItem.gameObject = nil
poolItem.transform = nil

function poolItem:__init(...)
    self:Ctor(...)
end

function poolItem:Ctor(...)
    self.gameObject = ...
    self.transform = self.gameObject.transform
end

function poolItem:Recycle()

end

function poolItem:__delete()
    self:Dispose()
end

function poolItem:Dispose()
    bc_CS_GameObject.Destroy(self.gameObject)
    self.gameObject = nil
    self.transform = nil
end

return poolItem
