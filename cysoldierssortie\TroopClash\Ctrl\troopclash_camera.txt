---@class troopclash_camera
local camera = bc_Class("troopclash_camera")
---@type troopclash_scene_mgr
camera.sceneMgr = nil
camera.followTransf = nil
camera.focusTransf = nil
camera.mainCamera = nil
--- 相机聚焦的方向
camera.norFocusDir = nil
camera.norFocusDirXYZ = nil
---@type number 相机和焦点的距离
camera.defaultNorDis = nil
---@type troopclash_player
camera.playerCtrl = nil

camera.screenSize = nil
camera.camBorderSize = nil
camera.camBorderSizeHalf = nil
camera.viewCenterPos = nil
camera.ViewCenterWorldPos = nil

camera.borderLB = nil
camera.borderRB = nil
camera.borderLT = nil
camera.borderRT = nil
---@type number 记录存在多少个区域
camera.borderCount = nil

camera.tween_Change = nil

local rot_Normal = TroopClash_Define.CS.Vector3(60, 0, 0)
local posOff_Normal = TroopClash_Define.CS.Vector3(0, 36.2, 11.1)
local FOV_Normal = 46
local FOV_Ready = 60
local rot_Ready = TroopClash_Define.CS.Vector3(41, 0, 0)
local readyDistance = 18
local camMoveSp = 8
local Anchor_LB = "Anchor_LB"
local Anchor_RB = "Anchor_RB"
local Anchor_LT = "Anchor_LT"
local Anchor_RT = "Anchor_RT"

function camera:__init(...)
    self.sceneMgr = ...
    self.followTransf = self.sceneMgr.DataSrc.CamFollow
    self.focusTransf = self.sceneMgr.DataSrc.FocusTransf
    self.mainCamera = self.sceneMgr.DataSrc.mainCam
    self.defaultNorDis = TroopClash_Define.CS.Vector3.Distance(TroopClash_Define.CacheVector3.Zero, posOff_Normal)
    local tarNormalRot = TroopClash_Define.CS.Quaternion.Euler(rot_Normal)
    self.norFocusDir = tarNormalRot * TroopClash_Define.CacheVector3.Forward
    self.norFocusDirXYZ = { x = self.norFocusDir.x, y = self.norFocusDir.y, z = self.norFocusDir.z }
    self.playerCtrl = self.sceneMgr.playerCtrl
    self.borderLB = {}
    self.borderRB = {}
    self.borderLT = {}
    self.borderRT = {}
    for k, v in pairs(self.sceneMgr.SceneDataSrc) do
        if string.startwith(k, "Border") then
            self.borderLB[#self.borderLB + 1] = v:Find(Anchor_LB).position
            self.borderRB[#self.borderRB + 1] = v:Find(Anchor_RB).position
            self.borderLT[#self.borderLT + 1] = v:Find(Anchor_LT).position
            self.borderRT[#self.borderRT + 1] = v:Find(Anchor_RT).position
        end
    end
    self.borderCount = #self.borderLB
    self.screenSize = {
        x = self.sceneMgr.lifeScope.SafeArea.ScreenWidth,
        y = self.sceneMgr.lifeScope.SafeArea.ScreenHeight
    }
    self.nearClipPlane = self.mainCamera.nearClipPlane

    local tmpOthoSize = math.tan(math.rad(FOV_Normal * 0.5)) * self.defaultNorDis
    self.camBorderSize = TroopClash_Define.CS.Vector3(tmpOthoSize * 2 * self.mainCamera.aspect, tmpOthoSize * 2, 0)
    self.camBorderSizeHalf = self.camBorderSize * 0.5
    self.viewCenterPos = TroopClash_Define.CS.Vector3(0.5, 0.5, self.defaultNorDis)
end

function camera:Reset()
    self.mainCamera.fieldOfView = FOV_Ready
    self.followTransf.eulerAngles = rot_Ready
    local tmpDir = TroopClash_Define.CS.Quaternion.Euler(rot_Ready) * TroopClash_Define.CacheVector3.Forward
    local newX, newY, newZ = self.playerCtrl:GetSlotFollowXYZ()
    local tmpDis = readyDistance --+ self:GetReadyDisOff()
    newX = newX - tmpDir.x * tmpDis
    newY = newY - tmpDir.y * tmpDis
    newZ = newZ - tmpDir.z * tmpDis
    TroopClash_Define.SetTransformPositionXYZ(self.followTransf, newX, newY, newZ)
end

function camera:ReadyToBattle(callBack)
    self:KillTween_Change()
    local moveDur = 0.5
    local ease = TroopClash_Define.TweenEase.OutQuad
    local newPos = self.playerCtrl:GetCenterVec3() - self.norFocusDir * self.defaultNorDis
    self.tween_Change = TroopClash_Define.DOTween.Sequence()
    self.tween_Change:Append(self.followTransf:DORotate(rot_Normal, moveDur):SetEase(ease))
    self.tween_Change:Insert(0, self.followTransf:DOMove(newPos, moveDur):SetEase(ease))
    self.tween_Change:Insert(0, self.mainCamera:DOFieldOfView(FOV_Normal, moveDur):SetEase(ease))
    self.tween_Change:OnComplete(function()
        self.ViewCenterWorldPos = self:ViewportToWorldPoint(self.viewCenterPos)
        callBack()
    end)
end

function camera:KillTween_Change()
    if self.tween_Change then
        self.tween_Change:Kill()
        self.tween_Change = nil
    end
end

function camera:LateUpdate(deltaTime)
    local ratio = deltaTime * camMoveSp
    local oldX, oldY, oldZ = TroopClash_Define.GetTransformPositionXYZ(self.followTransf)
    local newX, newY, newZ = self.playerCtrl:GetCenterXYZ()
    --边缘约束
    local lbLimit, rtLimit = self:CheckBorder()
    newX = math.clamp(newX, lbLimit.x + self.camBorderSizeHalf.x, rtLimit.x - self.camBorderSizeHalf.x)
    newZ = math.clamp(newZ, lbLimit.z + self.camBorderSizeHalf.y, rtLimit.z - self.camBorderSizeHalf.y)

    newX = newX - self.norFocusDirXYZ.x * self.defaultNorDis
    newY = newY - self.norFocusDirXYZ.y * self.defaultNorDis
    newZ = newZ - self.norFocusDirXYZ.z * self.defaultNorDis
    --平滑移动
    newX = math.lerp(oldX, newX, ratio)
    newY = math.lerp(oldY, newY, ratio)
    newZ = math.lerp(oldZ, newZ, ratio)
    TroopClash_Define.SetTransformPositionXYZ(self.followTransf, newX, newY, newZ)
    self.ViewCenterWorldPos = self:ViewportToWorldPoint(self.viewCenterPos)
end

--- 检测当前在哪个区域内
function camera:CheckBorder()
    --选取视野内最底和最高
    local lbLimit = { x = self.borderLB[1].x, z = self.borderLB[1].z }
    local rtLimit = { x = self.borderRT[1].x, z = self.borderRT[1].z }
    if self.borderCount > 1 then
        for i = 2, self.borderCount, 1 do
            local tmpLB = self.borderLB[i]
            local tmpRT = self.borderRT[i]
            local tmpLBsPos = self:WorldToScreenPoint(tmpLB)
            local tmpRTsPos = self:WorldToScreenPoint(tmpRT)
            if self:CheckCrossScreen(tmpLBsPos, tmpRTsPos) then
                lbLimit.z = math.min(lbLimit.z, tmpLB.z)
                lbLimit.x = math.min(lbLimit.x, tmpLB.x)
                rtLimit.z = math.max(rtLimit.z, tmpRT.z)
                rtLimit.x = math.max(rtLimit.x, tmpRT.x)
            end
        end
    end
    return lbLimit, rtLimit
end

--- 区域是否与屏幕重合
---@param pos any
---@return boolean
function camera:CheckCrossScreen(posMin, posMax)
    return self.screenSize.x > posMin.x and 0 < posMax.x and self.screenSize.y > posMin.y and 0 < posMax.y
end

--- 是否在屏幕内
function camera:CheckInView(wPos)
    return not (wPos.x < self.ViewCenterWorldPos.x - self.camBorderSizeHalf.x or wPos.x > self.ViewCenterWorldPos.x + self.camBorderSizeHalf.x
        or wPos.z < self.ViewCenterWorldPos.z - self.camBorderSizeHalf.y or wPos.z > self.ViewCenterWorldPos.z + self.camBorderSizeHalf.y)
end

function camera:WorldToScreenPoint(positionWS)
    return self.mainCamera:WorldToScreenPoint(positionWS)
end

function camera:ScreenToWorldPoint(positionSS)
    return self.mainCamera:ScreenToWorldPoint(positionSS)
end

function camera:ViewportToWorldPoint(pos)
    return self.mainCamera:ViewportToWorldPoint(pos)
end

function camera:WorldToViewportPoint(pos)
    return self.mainCamera:WorldToViewportPoint(pos)
end

function camera:GetDirFromViewPos(pos)
    local wPos = self:ViewportToWorldPoint(pos)
    return (wPos - self.followTransf.position).normalized
end

function camera:GetPosition()
    return self.mainCamera.transform.position
end

function camera:__delete()
    self:KillTween_Change()
end

return camera
