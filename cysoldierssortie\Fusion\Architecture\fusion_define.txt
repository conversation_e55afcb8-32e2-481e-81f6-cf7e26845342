---@class fusion_define
local Fusion = {}
local log = require "log"
local logTitleFormat = "<color=#EEFF00><b>%s>></b></color>\t"
---@type boolean
local showLogFlag = false
---@type string
local logTitle = nil

---@type fusion_mgrbase
Fusion.MgrBase = require("fusion_mgrbase")

Fusion.CS = {
    GameObject = CS.UnityEngine.GameObject
}

---@class fusion_MonoLoopType
Fusion.MonoLoopType = {
    OnUpdate = 1,
    OnFixedUpdate = 2,
    OnLateUpdate = 3,
}

function Fusion.InitLog(showFlag, title)
    showLogFlag = showFlag
    logTitle = string.format(logTitleFormat, title)
end

local function MsgToString(...)
    local d = { ... }
    local dd = {}
    local ind = 1
    for k, v in pairs(d) do
        d[k] = v ~= nil and tostring(v) or "nil"
        dd[ind] = d[k]
        ind = ind + 1
    end
    local tb = debug.traceback()
    return dd, tb
end

function Fusion.Log(...)
    if not showLogFlag then
        return
    end
    local dd, tb = MsgToString(...)
    log.Log(logTitle .. table.concat(dd, "\t") .. "\n" .. tb)
end

function Fusion.Error(...)
    if not showLogFlag then
        return
    end
    local dd, tb = MsgToString(...)
    log.Error(logTitle .. table.concat(dd, "\t") .. "\n" .. tb)
end

return Fusion
