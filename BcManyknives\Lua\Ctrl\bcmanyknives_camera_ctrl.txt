local cameraCtrl = TinyRush_CreateClass("bcmanyknives_camera_ctrl")

cameraCtrl.lifeScope = nil
cameraCtrl.sceneMgr = nil

cameraCtrl.camera = nil
cameraCtrl.cameraParent = nil
-- cameraCtrl.minBounds = nil
-- cameraCtrl.maxBounds = nil
-- 地图边界
cameraCtrl.mapMinX = nil
cameraCtrl.mapMaxX = nil
cameraCtrl.mapMinY = nil
cameraCtrl.mapMaxY = nil

cameraCtrl.bgSpriteRenderer = nil

cameraCtrl.playerCenter = nil
cameraCtrl.nearClipPlane = nil

cameraCtrl.scaleTween = nil
cameraCtrl.scaleFlag = nil

cameraCtrl.shakeTween = nil
cameraCtrl.followPlayer = nil
cameraCtrl.focusTarget = nil

cameraCtrl.pauseUnRegister = nil

local cameraZ = -100
local minOSize = 14
local maxOSize = 18
local camMoveSp = 8

function cameraCtrl:ctor(...)
    self.lifeScope, self.camera, self.cameraParent, self.bgSpriteRenderer = ...
    self.sceneMgr = self.lifeScope:get("bcmanyknives_scene_mgr")
    self.nearClipPlane = self.camera.nearClipPlane
    self.camera.transparencySortMode = CS.UnityEngine.TransparencySortMode.CustomAxis
    self.camera.transparencySortAxis = bc_CS_Vector3.up
    self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
        self:PauseListener(value)
    end)
end
function cameraCtrl:RefreshPause()
    self:PauseListener(self.sceneMgr.pauseBind.value)
end

function cameraCtrl:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.scaleTween ~= nil then
        self.scaleTween.timeScale = timeScale
    end
    if self.shakeTween ~= nil then
        self.shakeTween.timeScale = timeScale
    end
end

function cameraCtrl:dispose()
    self:KillScaleTween()
    self:KillShakeTween()
    if self.pauseUnRegister ~= nil then
        self.pauseUnRegister:unRegister()
        self.pauseUnRegister = nil
    end
end

function cameraCtrl:Init(player)
    self.playerCenter = player.center
    -- 地图边界
    local bgCenter = self.bgSpriteRenderer.transform.position
    local bgScale = self.bgSpriteRenderer.transform.lossyScale
    local bgSize = self.bgSpriteRenderer.size * 0.5
    self.mapLeftBound = bgCenter.x - (bgSize.x * bgScale.x)
    self.mapRightBound = bgCenter.x + (bgSize.x * bgScale.x)
    self.mapBottomBound = bgCenter.y - (bgSize.y * bgScale.y)
    self.mapTopBound = bgCenter.y + (bgSize.y * bgScale.y)
    self.orthographicSizeScale = (CS.UnityEngine.Screen.height / CS.UnityEngine.Screen.width) * 0.5
    self.viewSizeX = minOSize / self.orthographicSizeScale * 0.5
    self.viewSizeY = self.viewSizeX * (CS.UnityEngine.Screen.height / CS.UnityEngine.Screen.width)
    self.bgSpriteRenderer.size = self.bgSpriteRenderer.size + bc_CS_Vector2(self.viewSizeX * 2, self.viewSizeY * 2)
    player.bigSwordBind:register(function(value)
        self:BigSwordChanged(value)
    end)
    self.scaleFlag = false
end

function cameraCtrl:Reset()
    self:KillScaleTween()
    self:KillShakeTween()
    self.camera.transform.localPosition = bc_CS_Vector3.zero
    local tmpPos = self.playerCenter.position
    tmpPos.z = cameraZ
    self.cameraParent.position = tmpPos
    self.camera.orthographicSize = minOSize
    self.followPlayer = true
    self.focusTarget = nil
end

--- 监听玩家大宝剑状态
function cameraCtrl:BigSwordChanged(value)
    if not self.sceneMgr.overFlag and self.scaleFlag ~= value then
        self:KillScaleTween()
        local starSize = value and minOSize or maxOSize
        local tarSize = value and maxOSize or minOSize
        self.scaleTween = ManyKnivesDefine.DOVirtual.Float(starSize, tarSize, 0.5, function(value)
            self.camera.orthographicSize = value
        end)
        self.scaleFlag = value
        self:RefreshPause()
    end
end
--- 相机聚焦
function cameraCtrl:FocusPos(focus, targetTran, scale)
    self.followPlayer = not focus
    if focus then
        self.focusTarget = targetTran
    end
    if scale then
        self:KillScaleTween()
        self.scaleTween = ManyKnivesDefine.DOVirtual.Float(self.camera.orthographicSize, 8, 0.3, function(value)
            self.camera.orthographicSize = value
        end)
        self:RefreshPause()
    end
end
-- 玩家受伤
function cameraCtrl:Shake_PlayerHurt()
    -- self:Shake(0.1, 0.2, 4, 90)
end
-- 敌人受伤
function cameraCtrl:Shake_EnemyHurt()
    self:Shake(0.05, 0.1, 3, 90)
end
-- 拼刀
function cameraCtrl:Shake_BladeTrigger(dir)
    -- self:Shake(0.05, 0.1, 3, 90, dir)
    self:Shake(0.1, 0.25, 4, 90, dir)
end
-- Boss登场
function cameraCtrl:Shake_BossEntrance()
    self:Shake(0.5, 0.5, 10, 90)
end
-- Boss登场
function cameraCtrl:Shake_BossDie()
    self:Shake(0.5, 0.8, 10, 90)
end
local oriDir = bc_CS_Vector3(1, 1, 0)
-- 屏幕抖动
function cameraCtrl:Shake(duration, strength, vibrato, randomness, dir)
    self:KillShakeTween()
    if dir == nil then
        dir = oriDir
    end
    self.camera.transform.localPosition = bc_CS_Vector3.zero
    self.shakeTween = self.camera.transform:DOShakePosition(duration, dir * strength, vibrato, randomness)
    self:RefreshPause()
end

-- 限制位置在地图内
function cameraCtrl:LimitPosInMap(vec2)
    vec2.x = math.clamp(vec2.x, self.mapLeftBound, self.mapRightBound)
    vec2.y = math.clamp(vec2.y, self.mapBottomBound, self.mapTopBound)
    return vec2
end

function cameraCtrl:OnFixedUpdate(deltaTime)
    -- 非演出状态才跟随玩家
    if self.followPlayer and not self.sceneMgr.NoInjury then
        local oSizeScale = self.camera.orthographicSize / minOSize
        -- 相机移动范围
        self.mapMinX = self.mapLeftBound + self.viewSizeX * oSizeScale
        self.mapMaxX = self.mapRightBound - self.viewSizeX * oSizeScale
        self.mapMinY = self.mapBottomBound + self.viewSizeY * oSizeScale
        self.mapMaxY = self.mapTopBound - self.viewSizeY * oSizeScale
        local tmpPos = self.playerCenter.position
        local clampedX = math.clamp(tmpPos.x, self.mapMinX, self.mapMaxX)
        local clampedY = math.clamp(tmpPos.y, self.mapMinY, self.mapMaxY)
        tmpPos = bc_CS_Vector3(clampedX, clampedY, cameraZ)
        self.cameraParent.position = bc_CS_Vector3.Lerp(self.cameraParent.position, tmpPos, deltaTime * camMoveSp)
    end
end

function cameraCtrl:OnUpdate(deltaTime)
    if not self.followPlayer then
        local tarPos = self.focusTarget.position
        tarPos.z = cameraZ
        self.cameraParent.position = tarPos
        return
    end
    if self.sceneMgr.NoInjury then
        local oSizeScale = self.camera.orthographicSize / minOSize
        -- 相机移动范围
        self.mapMinX = self.mapLeftBound + self.viewSizeX * oSizeScale
        self.mapMaxX = self.mapRightBound - self.viewSizeX * oSizeScale
        self.mapMinY = self.mapBottomBound + self.viewSizeY * oSizeScale
        self.mapMaxY = self.mapTopBound - self.viewSizeY * oSizeScale
        local tmpPos = self.cameraParent.position
        local clampedX = math.clamp(tmpPos.x, self.mapMinX, self.mapMaxX)
        local clampedY = math.clamp(tmpPos.y, self.mapMinY, self.mapMaxY)
        tmpPos = bc_CS_Vector3(clampedX, clampedY, cameraZ)
        self.cameraParent.position = bc_CS_Vector3.Lerp(self.cameraParent.position, tmpPos, deltaTime * camMoveSp)
    end
end

function cameraCtrl:ViewportToWorldPoint(pos)
    return self.camera:ViewportToWorldPoint(bc_CS_Vector3(pos.x, pos.y, self.nearClipPlane))
end
function cameraCtrl:ScreenToWorldPoint(pos)
    return self.camera:ScreenToWorldPoint(bc_CS_Vector3(pos.x, pos.y, self.nearClipPlane))
end
function cameraCtrl:WorldToScreenPoint(pos)
    return self.camera:WorldToScreenPoint(bc_CS_Vector3(pos.x, pos.y, self.nearClipPlane))
end

function cameraCtrl:KillScaleTween()
    if self.scaleTween ~= nil then
        self.scaleTween:Kill()
        self.scaleTween = nil
    end
end
function cameraCtrl:KillShakeTween()
    if self.shakeTween ~= nil then
        self.shakeTween:Kill()
        self.shakeTween = nil
    end
end

return cameraCtrl
