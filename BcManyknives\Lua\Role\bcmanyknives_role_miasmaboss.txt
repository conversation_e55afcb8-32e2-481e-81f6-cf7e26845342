local manyknives_role_boss = TinyRush_CreateClass("bcmanyknives_role_miasmaboss"):baseClass(require(
    "bcmanyknives_role_boss"))

manyknives_role_boss.miasmaConfig = nil
manyknives_role_boss.pathTimer = nil
manyknives_role_boss.rangeTimer = nil
manyknives_role_boss.rangeAttentTimer = nil
manyknives_role_boss.rangeAttentFlag = nil
manyknives_role_boss.rangeGuideList = nil

manyknives_role_boss.miasmaDmg = nil

local vecZero = bc_CS_Vector3.zero
local vecOne = bc_CS_Vector3.one

function manyknives_role_boss:Init(...)
    self.__base:Init(...)
    self.miasmaConfig = self.sceneMgr:GetMiasmaConfigById(self.roleData.skillLevel)
    self.miasmaDmg = self.miasmaConfig.dmg
end

function manyknives_role_boss:Start()
    self.__base:Start()
end

function manyknives_role_boss:Ready()
    self.__base:Ready()
    self.pathTimer = 0
    self.rangeTimer = 0
    self.rangeAttentFlag = false
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
    self:SetAICanMove(true)
    self.sceneMgr.tutMgr:ShowBossGuide_Miasma()
end

function manyknives_role_boss:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    if self.deadFlag or not self.actionFlag then
        return
    end
    self:MoveFollow(deltaTime)
    if self.miasmaConfig.path_Enable then
        self.pathTimer = self.pathTimer + deltaTime
        if self.pathTimer >= self.miasmaConfig.path_CD then
            self.pathTimer = 0
            local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_miasma_boss)
            fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_miasma_boss, self)
            fx:GetColliderKey().name = ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                                           ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                                           ManyKnivesDefine.effectType.miasma
            fx:Play(3, self.transform.position, self.miasmaConfig.path_ExitTime)
        end
    end
    if self.miasmaConfig.range_Enable then
        if not self.rangeAttentFlag then
            self.rangeTimer = self.rangeTimer + deltaTime
            if self.rangeTimer >= self.miasmaConfig.range_CD then
                self.rangeAttentFlag = true
                self.rangeAttentTimer = 0
                -- 选取屏幕中的位置
                local bound = bc_CS_Vector3(self.miasmaConfig.range_Range * 0.5, self.miasmaConfig.range_Range * 0.5, 0)
                local viewMin = self.sceneMgr.cameraCtrl:ViewportToWorldPoint(vecZero) + bound
                local viewMax = self.sceneMgr.cameraCtrl:ViewportToWorldPoint(vecOne) - bound
                local tmpPos = bc_CS_Vector3(math.lerp(viewMin.x, viewMax.x, math.random()),
                    math.lerp(viewMin.y, viewMax.y, math.random()), 0)
                self.rangeGuideList = {}
                local perRange = self.miasmaConfig.range_Range * 0.5
                for i = 1, self.miasmaConfig.range_Num, 1 do
                    local newPos = tmpPos +
                                       bc_CS_Vector3(math.lerp(-perRange, perRange, math.random()),
                            math.lerp(-perRange, perRange, math.random()), 0)
                    newPos = self.sceneMgr:GetSafetyPosition(newPos)
                    newPos.z = 0
                    local circleGuide = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.circleGuide)
                    circleGuide:Init(self.sceneMgr, ManyKnivesDefine.skillNames.circleGuide)
                    circleGuide:Play(3,
                        bc_CS_Vector2(self.miasmaConfig.range_PerRange, self.miasmaConfig.range_PerRange), newPos,
                        bc_CS_Quaternion.identity)
                    self.rangeGuideList[i] = circleGuide
                end
            end
        else
            self.rangeAttentTimer = self.rangeAttentTimer + deltaTime
            if self.rangeAttentTimer >= self.miasmaConfig.range_Attent then
                for _, v in pairs(self.rangeGuideList) do
                    local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_miasma_boss)
                    fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_miasma_boss, self)
                    fx:GetColliderKey().name = ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                                                   ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                                                   ManyKnivesDefine.effectType.miasma
                    fx:Play(3, v.transform.position, self.miasmaConfig.range_ExitTime,
                        self.miasmaConfig.range_PerRange / 6.8)
                end
                self:HideRangeGuide()
                self.rangeTimer = 0
                self.rangeAttentFlag = false
            end
            if self.rangeGuideList ~= nil then
                local process = self.rangeAttentTimer / self.miasmaConfig.range_Attent
                for _, v in pairs(self.rangeGuideList) do
                    v:SetFill(process)
                end
            end
        end
    end
end
function manyknives_role_boss:HideRangeGuide()
    if self.rangeGuideList ~= nil then
        for _, v in pairs(self.rangeGuideList) do
            v:PushInPool()
        end
        self.rangeGuideList = nil
    end
end
function manyknives_role_boss:deaded()
    self.__base:deaded()
end

function manyknives_role_boss:recycle()
    self:HideRangeGuide()
    self.__base:recycle()
end
function manyknives_role_boss:dispose()
    self.__base:dispose()
end

return manyknives_role_boss
