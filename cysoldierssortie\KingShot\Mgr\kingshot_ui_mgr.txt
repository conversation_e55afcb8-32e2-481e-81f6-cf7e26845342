---@class kingshot_ui_mgr : fusion_mgrbase
---@field lifeScope kingshot_lifescope
local mgr = bc_Class("kingshot_ui_mgr", Fusion.MgrBase)
---@type kingshot_res_mgr
mgr.resMgr = nil
---@type fusion_mono
mgr.mono = nil
---@type kingshot_scene_mgr
mgr.sceneMgr = nil

mgr.DataSrc = nil
---@type table 计算后的真实画布分辨率
mgr.RealCanvaSize = nil
---@type boolean
mgr.readyFlag = nil
mgr.touchStartFlag = nil
---@type fusion_delegate
mgr.DragEvent = nil

mgr.sliderDataWithBossSlider = nil

local EventTrigger_Entry = CS.UnityEngine.EventSystems.EventTrigger.Entry
local EventTriggerType = CS.UnityEngine.EventSystems.EventTriggerType
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local cysoldierssortie_ui_hud_anim_config = require("cysoldierssortie_ui_hud_anim_config")

function mgr:__init()
    self.DragEvent = require("fusion_delegate").New()
end

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.resMgr = self.lifeScope:GetMgr("kingshot_res_mgr")
    self.mono = self.lifeScope:GetMgr("fusion_mono")
    self.sceneMgr = self.lifeScope:GetMgr("kingshot_scene_mgr")
end

function mgr:Ready()
    self.readyFlag = false
    local mainPanel = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.MainPanelPrefab)
    mainPanel.transform:SetParent(self.mono.globalMonoGO.transform)
    mainPanel.transform.localPosition = KingShot_Define.CacheVector3.Zero
    mainPanel.transform.localScale = KingShot_Define.CacheVector3.One
    self.DataSrc = {}
    local neeRefer = mainPanel:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)

    local tempRealSize = self.DataSrc.CanvasScaler.referenceResolution
    local safeArea = self.lifeScope.SafeArea
    local realScale = safeArea.ScreenWidth / safeArea.ScreenHeight
    local standardScale = tempRealSize.x / tempRealSize.y
    -- 更宽
    if realScale > standardScale then
        tempRealSize.x = tempRealSize.x * (realScale / standardScale)
    elseif realScale < standardScale then
        tempRealSize.y = tempRealSize.y * (standardScale / realScale)
    end
    self.RealCanvaSize = KingShot_Define.CS.Vector2(math.round(tempRealSize.x), math.round(tempRealSize.y))
    self.ScreenSize = KingShot_Define.CS.Vector2(safeArea.ScreenWidth, safeArea.ScreenHeight)
    self.TopOffY = safeArea.TopRatio * self.RealCanvaSize.y
    local topAnchorY = -50
    if safeArea.TopRatio > 0 then
        topAnchorY = -50 - self.TopOffY
    end
    self.DataSrc.TopRect.anchoredPosition = { x = 0, y = topAnchorY }

    local trigger = EventTrigger_Entry()
    trigger.eventID = EventTriggerType.PointerDown
    trigger.callback:AddListener(function(eventData)
        self:OnPointerDown(eventData)
    end)
    local trigger2 = EventTrigger_Entry()
    trigger2.eventID = EventTriggerType.PointerUp
    trigger2.callback:AddListener(function(eventData)
        self:OnPointerUp(eventData)
    end)
    local trigger3 = EventTrigger_Entry()
    trigger3.eventID = EventTriggerType.Drag
    trigger3.callback:AddListener(function(eventData)
        self:OnDrag(eventData)
    end)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger2)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger3)
    self.joySize = self.DataSrc.Joystick.sizeDelta * 0.5
    self.joyBoundOff = self.joySize.x + 10

    self.sceneMgr.BattleFlagBind:Register(function(value)
        self:BattleFlagListener(value)
    end)

    self.sliderDataWithBossSlider = {}
end

function mgr:GameStart()
    self.touchStartFlag = false
    self.readyFlag = true
    self:ShowJoy(false)
    self:ShowCtrlPanel(false)
    self:ShowInfoRoot(false)
end

function mgr:BattleFlagListener(value)
    if value then
        self:ShowCtrlPanel(true)
        self:ShowInfoRoot(true)
    end
end

function mgr:ShowInfoRoot(show)
    self.DataSrc.InfoRoot.gameObject:SetActive(show)
end

function mgr:ShowJoy(value)
    self.DataSrc.Joystick.gameObject:SetActive(value)
end

function mgr:ShowCtrlPanel(value)
    self.DataSrc.CtrlEventTrigger.gameObject:SetActive(value)
end

function mgr:OnPointerDown(eventData)
    if not self.readyFlag or not self.sceneMgr.BattleFlagBind.value then
        return
    end
    if not self.touchStartFlag then
        self.touchStartFlag = true
    end
    self.DataSrc.Joystick.anchoredPosition = self.RealCanvaSize * (eventData.position / self.ScreenSize)
    self:ShowJoy(true)
end

function mgr:OnPointerUp(eventData)
    if not self.touchStartFlag then
        return
    end
    self.DataSrc.JoyHandle.anchoredPosition = KingShot_Define.CacheVector2.Zero
    self.DragEvent:Invoke(KingShot_Define.CacheVector2.Zero)
    self:ShowJoy(false)
end

function mgr:OnDrag(eventData)
    if not self.touchStartFlag then
        return
    end
    local curPos = self.RealCanvaSize * (eventData.position / self.ScreenSize)
    local off = curPos - self.DataSrc.Joystick.anchoredPosition
    -- 摇杆偏移量限制
    if (off.magnitude > self.joySize.x) then
        off = off.normalized * self.joySize.x
    end
    self.DataSrc.JoyHandle.anchoredPosition = off
    self.DragEvent:Invoke(off.normalized)
    local bgPos = self.DataSrc.Joystick.anchoredPosition
    if curPos.x > bgPos.x + self.joyBoundOff then
        bgPos.x = curPos.x - self.joyBoundOff
    elseif curPos.x < bgPos.x - self.joyBoundOff then
        bgPos.x = curPos.x + self.joyBoundOff
    end
    if curPos.y > bgPos.y + self.joyBoundOff then
        bgPos.y = curPos.y - self.joyBoundOff
    elseif curPos.y < bgPos.y - self.joyBoundOff then
        bgPos.y = curPos.y + self.joyBoundOff
    end
    self.DataSrc.Joystick.anchoredPosition = bgPos
end

function mgr:WorldPos2CanvasVec3(wPos)
    local anchorPos = self.sceneMgr.cameraCtrl:WorldToViewportPoint(wPos)
    anchorPos.x = self.RealCanvaSize.x * (anchorPos.x - 0.5)
    anchorPos.y = self.RealCanvaSize.y * (anchorPos.y - 0.5)
    return { x = anchorPos.x, y = anchorPos.y, z = 0 }
end

function mgr:__delete()
    if self.DragEvent ~= nil then
        self.DragEvent:Delete()
        self.DragEvent = nil
    end
end

function mgr:AutoAdaptLocalScale(trans, pZ, oriLocalScale)
end

function mgr:ClearCamZ()
end

function mgr:UpdateBossHpSlider(HpSliderImage, MaxHp, curHp)
    local fillAmount = 0
    local num = 0
    local xNum = 250
    curHp = math.floor(curHp)
    if MaxHp > xNum * 20 then
        xNum = MaxHp / 20
    end
    if MaxHp > xNum then
        num = math.floor(curHp / xNum) + 1
        local amount = curHp % xNum
        num = math.floor(curHp / xNum)
        fillAmount = amount / xNum
    else
        fillAmount = curHp / MaxHp
    end
    HpSliderImage.value = fillAmount
    local dataSrc = self.sliderDataWithBossSlider[HpSliderImage]
    if dataSrc == nil then
        dataSrc = {
            HpText = HpSliderImage.transform:GetChild(2):GetComponent(KingShot_Define.TypeOf.UIText),
            NumText = HpSliderImage.transform:GetChild(3):GetComponent(KingShot_Define.TypeOf.UIText),
            BackgroundImage = HpSliderImage.transform:GetChild(1):GetComponent(KingShot_Define.TypeOf.UIImage),
            fillImage = HpSliderImage.transform:GetChild(1):GetChild(0):GetChild(0):GetComponent(KingShot_Define
                .TypeOf.UIImage)
        }
        self.sliderDataWithBossSlider[HpSliderImage] = dataSrc
    end
    if num == 0 then
        dataSrc.BackgroundImage.color = KingShot_Define.CacheColor.Black
        dataSrc.fillImage.color = KingShot_Define.CacheColor.Red
    elseif num % 2 == 0 then
        dataSrc.BackgroundImage.color = KingShot_Define.CacheColor.White
        dataSrc.fillImage.color = KingShot_Define.CacheColor.Red
    else
        dataSrc.BackgroundImage.color = KingShot_Define.CacheColor.Red
        dataSrc.fillImage.color = KingShot_Define.CacheColor.White
    end

    -- FD1784  292525
    dataSrc.HpText.text = tostring(curHp)
    dataSrc.NumText.text = tostring("x" .. num)
end

function mgr:UpdateHpSliderXYZ(HpSliderImage, curHp, MaxHp, pX, pY, pZ, isBoss, parent, lastPosX, lastPosZ)
    local tmpParent = parent or HpSliderImage.transform.parent
    local canvasPos = self:WorldPos2CanvasVec3({ x = pX, y = pY, z = pZ })
    tmpParent.localPosition = canvasPos

    if isBoss then
        self:UpdateBossHpSlider(HpSliderImage, MaxHp, curHp)
        return
    end
    local amount = curHp / MaxHp
    if amount >= 1 then
        return
    end
    HpSliderImage.value = amount
end

function mgr:DestoryHpSlider(HpSliderImage)
    KingShot_Define.CS.NeeGame.ReturnObject(HpSliderImage.transform.parent.gameObject)
end

function mgr:SpawnHpSlider(playerType, pos, isBoss)
    local name = cysoldierssortie_PoolObjectName.MonsterHpSlider
    local isPlayer = false
    if playerType then
        if playerType == 1 then
            name = cysoldierssortie_PoolObjectName.PlayerHpSlider
        else
            name = cysoldierssortie_PoolObjectName.PlayerHpHero
            isPlayer = true
        end
    end
    if isBoss then
        name = cysoldierssortie_PoolObjectName.BossHpSlider
    end
    local HpSlider = KingShot_Define.CS.NeeGame.PoolObject(name, self.DataSrc.HpSliderParent)
    local imgTransf = HpSlider.transform:Find("Slider")
    local HpSliderImage = imgTransf:GetComponent(KingShot_Define.TypeOf.UISlider)
    HpSliderImage.value = 1
    KingShot_Define.SetTransformLocalScale(imgTransf, 0.5, 0.5, 1)
    KingShot_Define.SetTransformLocalPositionAndLocalRotation(imgTransf, 0, 0, 0, 0, 0, 0)
    if isPlayer then
        local avatar = HpSlider.transform:Find("Avatar")
        avatar.gameObject.layer = 2
        KingShot_Define.SetTransformLocalScale(avatar, 1, 1, 1)
        KingShot_Define.SetTransformLocalPositionAndLocalRotation(avatar, -40, 0, 0, 0, 0, 0)
    end
    HpSlider.transform.position = pos
    KingShot_Define.SetTransformLocalScale(HpSlider.transform, 1.5, 1.5, 1.5)
    return HpSliderImage
end

function mgr:SpawnHpNumAniTextXYZ(attack, pX, pY, pZ, isCritical, skillView)
    isCritical = isCritical or false
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local HpNumAni = nil
    local HpTrans = nil
    local parent = self.sceneMgr.DataSrc.NumTextParent
    if skillView then
        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.SkillHpNumAniText, parent)
        HpTrans = poolMgr:GetTransform(HpNumAni)
    elseif isCritical then
        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.CriticalHpNumAniText, parent)
        HpTrans = poolMgr:GetTransform(HpNumAni)
    else
        HpNumAni = KingShot_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.HpNumAniText, parent)
        HpTrans = poolMgr:GetTransform(HpNumAni)
    end

    KingShot_Define.SetTransformPositionXYZ(HpTrans, pX, pY, pZ)
    local HpNumAniText = nil
    if poolMgr then
        HpNumAniText = poolMgr:GetHpAnimText(HpNumAni)
    end
    if isCritical then
        KingShot_Define.SetTransformLocalScale(HpTrans, 1, 1, 1)
        if HpNumAniText then
            KingShot_Define.SetTransformLocalScale(HpNumAniText.transform, 0.7, 0.7, 1)
        end
    else
        KingShot_Define.SetTransformLocalScale(HpTrans, 0.6, 0.6, 1)
    end
    if HpNumAniText then
        if isCritical then
            cysoldierssortie_lua_util:Append("C")
            cysoldierssortie_lua_util:Append("-")
            cysoldierssortie_lua_util:Append(math.floor(attack))
            HpNumAniText.text = cysoldierssortie_lua_util:ToString()
        else
            cysoldierssortie_lua_util:Append("-")
            cysoldierssortie_lua_util:Append(math.floor(attack))
            HpNumAniText.text = cysoldierssortie_lua_util:ToString()
        end
    end

    local sequenceType = 1
    if skillView then
        if isCritical then
            sequenceType = 4
        else
            sequenceType = 3
        end
    elseif isCritical then
        sequenceType = 2
    end
    cysoldierssortie_ui_hud_anim_config:PlaySequence(HpNumAni, HpTrans, pY, 1, sequenceType)
end

return mgr
