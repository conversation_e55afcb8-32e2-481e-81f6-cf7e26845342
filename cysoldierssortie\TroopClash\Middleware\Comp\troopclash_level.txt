---@class troopclash_level
local level = bc_Class("troopclash_level")
---@type troopclash_mgr_level
level.levelMgr = nil
---@type troopclash_scene_mgr
level.sceneMgr = nil
---@type troopclash_ui_mgr
level.uiMgr = nil
level.MiniLevelCfg = nil
---@type troopclash_player
level.playerLua = nil

local cs_generator = require("xlua.util").cs_generator

function level:__init(...)
    self.levelMgr, self.MiniLevelCfg, self.uiMgr = ...
    self.sceneMgr = self.levelMgr.sceneMgr
end

function level:SetPlayer<PERSON>ua(player)
    self.playerLua = player
    self.playerLua.curLevel = self
    self._ai_nav = true
end

function level:GetUseSoldier(lv)
    return TroopClash_Define.SoldierUnitIDWithLevel[lv]
end

function level:AddEnemy(character)
end

function level:GetAllEnemy()
    return nil
end

--敌人死亡调用，敌人数量为0胜利
function level:RaduceEnemyCount(num, character)
    self.sceneMgr:EnemyDead(character)
end

function level:IsRunnerMode()
    return false
end

function level:GetMaxViewEnemyCount()
    return nil
end

---获取当前敌人数量
function level:GetCurEnemyCount()
    return 60
end

function level:OnStartCoroutine(func, ...)
    return TroopClash_Define.CS.NeeGame.Instance:StartCoroutine(cs_generator(func, ...))
end

function level:PlaySfx(fxName, cd)
    cysoldierssortie_PlaySfx(fxName, 1)
end

function level:IsEnemyAutoMoveForward()
    return true
end

function level:GetPlayerZ()
    local _, _, z = self.playerLua:GetCenterXYZ()
    return z
end

return level
