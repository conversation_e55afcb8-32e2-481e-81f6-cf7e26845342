---@class troopclash_ui_mgr : fusion_mgrbase
---@field lifeScope troopclash_lifescope
local mgr = bc_Class("troopclash_ui_mgr", Fusion.MgrBase)
---@type troopclash_res_mgr
mgr.resMgr = nil
---@type fusion_mono
mgr.mono = nil
---@type troopclash_scene_mgr
mgr.sceneMgr = nil
---@type fusion_lang_mgr
mgr.langMgr = nil

mgr.DataSrc = nil
---@type table 计算后的真实画布分辨率
mgr.RealCanvaSize = nil
---@type boolean
mgr.readyFlag = nil
mgr.touchStartFlag = nil
---@type fusion_delegate
mgr.DragEvent = nil

mgr.sliderDataWithBossSlider = nil
---@type boolean 预警是否显示过
mgr.attentFlag = nil

mgr.tween_Tut = nil
mgr.tween_Task = nil
mgr.tween_BossAttent = nil

local EventTrigger_Entry = CS.UnityEngine.EventSystems.EventTrigger.Entry
local EventTriggerType = CS.UnityEngine.EventSystems.EventTriggerType
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local cysoldierssortie_ui_hud_anim_config = require("cysoldierssortie_ui_hud_anim_config")
local taskFormat = "%d/%d"

function mgr:__init()
    self.DragEvent = require("fusion_delegate").New()
end

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.resMgr = self.lifeScope:GetMgr("troopclash_res_mgr")
    self.mono = self.lifeScope:GetMgr("fusion_mono")
    self.sceneMgr = self.lifeScope:GetMgr("troopclash_scene_mgr")
    self.langMgr = self.lifeScope:GetMgr("fusion_lang_mgr")
end

function mgr:Ready()
    self.readyFlag = false
    local mainPanel = TroopClash_Define.CS.GameObject.Instantiate(self.resMgr.MainPanelPrefab)
    mainPanel.transform:SetParent(self.mono.globalMonoGO.transform)
    mainPanel.transform.localPosition = TroopClash_Define.CacheVector3.Zero
    mainPanel.transform.localScale = TroopClash_Define.CacheVector3.One
    self.DataSrc = {}
    local neeRefer = mainPanel:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)

    local tempRealSize = self.DataSrc.CanvasScaler.referenceResolution
    local safeArea = self.lifeScope.SafeArea
    local realScale = safeArea.ScreenWidth / safeArea.ScreenHeight
    local standardScale = tempRealSize.x / tempRealSize.y
    -- 更宽
    if realScale > standardScale then
        tempRealSize.x = tempRealSize.x * (realScale / standardScale)
    elseif realScale < standardScale then
        tempRealSize.y = tempRealSize.y * (standardScale / realScale)
    end
    self.RealCanvaSize = TroopClash_Define.CS.Vector2(math.round(tempRealSize.x), math.round(tempRealSize.y))
    self.ScreenSize = TroopClash_Define.CS.Vector2(safeArea.ScreenWidth, safeArea.ScreenHeight)
    self.TopOffY = safeArea.TopRatio * self.RealCanvaSize.y
    local topAnchorY = -50
    if safeArea.TopRatio > 0 then
        topAnchorY = -50 - self.TopOffY
    end
    self.DataSrc.TopRect.anchoredPosition = { x = 0, y = topAnchorY }

    local trigger = EventTrigger_Entry()
    trigger.eventID = EventTriggerType.PointerDown
    trigger.callback:AddListener(function(eventData)
        self:OnPointerDown(eventData)
    end)
    local trigger2 = EventTrigger_Entry()
    trigger2.eventID = EventTriggerType.PointerUp
    trigger2.callback:AddListener(function(eventData)
        self:OnPointerUp(eventData)
    end)
    local trigger3 = EventTrigger_Entry()
    trigger3.eventID = EventTriggerType.Drag
    trigger3.callback:AddListener(function(eventData)
        self:OnDrag(eventData)
    end)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger2)
    self.DataSrc.CtrlEventTrigger.triggers:Add(trigger3)
    self.joySize = self.DataSrc.Joystick.sizeDelta * 0.5
    self.joyBoundOff = self.joySize.x + 10

    self.sceneMgr.BattleFlagBind:Register(function(value)
        self:BattleFlagListener(value)
    end)

    self.sliderDataWithBossSlider = {}
    self.DataSrc.TextLv.text = string.format(self.langMgr:GetLangById(1), TroopClash_Define.minigame_mgr.GetHookLevelId())
    self.DataSrc.TextTaskTitle.text = self.langMgr:GetLangById(4)
    self.DataSrc.TextBossAttent.text = self.langMgr:GetLangById(3)
    self.DataSrc.TextTut.text = self.langMgr:GetLangById(2)

    self.sceneMgr.TaskNumBind:Register(function(value)
        self.DataSrc.TextProcess.text = string.format(taskFormat, value, self.sceneMgr.TaskNumMax)
        self.DataSrc.ProcessSlider.value = value / self.sceneMgr.TaskNumMax
    end)
end

function mgr:GameStart()
    self.touchStartFlag = false
    self.readyFlag = true
    self:ShowJoy(false)
    self:ShowCtrlPanel(false)
    self:ShowInfoRoot(false)
    self:ShowTut(false)
    self:ShowTask(false)
    self:ShowBossAttent(false)
    self.attentFlag = false
    local taskDetailId = self.resMgr.CurLvConfig.PassType == TroopClash_Define.LevelPassType.AllEnemy and 5 or 6
    self.DataSrc.TextTaskDetail.text = string.format(self.langMgr:GetLangById(taskDetailId), self.sceneMgr.TaskNumMax)
end

function mgr:BattleFlagListener(value)
    if value then
        self:ShowCtrlPanel(true)
        self:ShowInfoRoot(true)
        self:ShowTut(true)
        self:ShowTask(true)
    end
end

function mgr:ShowTut(show)
    self:KillTween_Tut()
    self.DataSrc.TutRect.gameObject:SetActive(show)
    if show then
        self.DataSrc.TutImg.transform.localScale = TroopClash_Define.CacheVector3.One
        local tarScale = TroopClash_Define.CS.Vector3(1.1, 1.1, 1)
        self.tween_Tut = self.DataSrc.TutImg.transform:DOScale(tarScale, 0.5):SetEase(
            TroopClash_Define.TweenEase.InSine):SetLoops(-1, TroopClash_Define.TweenLoopType.Yoyo)
    end
end

function mgr:ShowTask(show)
    self:KillTween_Task()
    self.DataSrc.TaskCG.gameObject:SetActive(show)
    if show then
        self.DataSrc.TaskCG.alpha = 0
        self.DataSrc.TaskCG.transform.localScale = { x = 1.8, y = 1.8, z = 1 }
        self.tween_Task = TroopClash_Define.DOTween.Sequence()
        self.tween_Task:Append(self.DataSrc.TaskCG.transform:DOScale(TroopClash_Define.CacheVector3.One, 0.2):SetEase(
            TroopClash_Define.TweenEase.Linear))
        self.tween_Task:Insert(0, TroopClash_Define.DOVirtual.Float(0, 1, 0.2, function(value)
            self.DataSrc.TaskCG.alpha = value
        end):SetEase(TroopClash_Define.TweenEase.OutSine))
        self.tween_Task:AppendInterval(1.5)
        self.tween_Task:Append(TroopClash_Define.DOVirtual.Float(1, 0, 0.3, function(value)
            self.DataSrc.TaskCG.alpha = value
        end):SetEase(TroopClash_Define.TweenEase.InSine))
        self.tween_Task:OnComplete(function()
            self.DataSrc.TaskCG.gameObject:SetActive(false)
        end)
    end
end

function mgr:ShowBossAttent(show)
    if self.attentFlag then
        return
    end
    self:KillTween_BossAttent()
    self.DataSrc.BossAttentCG.gameObject:SetActive(show)
    if show then
        self.attentFlag = true
        self.DataSrc.TextBossAttent.transform.localScale = { x = 1, y = 1, z = 1 }
        local tarScale = { x = 1.3, y = 1.3, z = 1 }
        self.DataSrc.BossAttentCG.alpha = 0
        self.tween_BossAttent = TroopClash_Define.DOTween.Sequence()
        self.tween_BossAttent:Append(TroopClash_Define.DOVirtual.Float(0, 1, 0.3, function(value)
            self.DataSrc.BossAttentCG.alpha = value
        end):SetEase(TroopClash_Define.TweenEase.OutSine))
        self.tween_BossAttent:OnComplete(function()
            self.tween_BossAttent = TroopClash_Define.DOTween.Sequence()
            self.tween_BossAttent:Append(self.DataSrc.TextBossAttent.transform:DOScale(tarScale, 0.2)
                :SetEase(TroopClash_Define.TweenEase.InOutQuad)
                :SetLoops(2, TroopClash_Define.TweenLoopType.Yoyo))
            self.tween_BossAttent:AppendInterval(0.15)
            self.tween_BossAttent:SetLoops(3, TroopClash_Define.TweenLoopType.Restart)
            self.tween_BossAttent:OnComplete(function()
                self.tween_BossAttent = TroopClash_Define.DOVirtual.Float(1, 0, 0.3, function(value)
                    self.DataSrc.BossAttentCG.alpha = value
                end):SetEase(TroopClash_Define.TweenEase.InSine)
                self.tween_BossAttent:OnComplete(function()
                    self.DataSrc.BossAttentCG.gameObject:SetActive(false)
                end)
            end)
        end)
    end
end

function mgr:ShowInfoRoot(show)
    self.DataSrc.InfoRoot.gameObject:SetActive(show)
end

function mgr:ShowJoy(value)
    self.DataSrc.Joystick.gameObject:SetActive(value)
end

function mgr:ShowCtrlPanel(value)
    self.DataSrc.CtrlEventTrigger.gameObject:SetActive(value)
end

function mgr:OnPointerDown(eventData)
    if not self.readyFlag or not self.sceneMgr.BattleFlagBind.value then
        return
    end
    if not self.touchStartFlag then
        self:ShowTut(false)
        self.touchStartFlag = true
    end
    self.DataSrc.Joystick.anchoredPosition = self.RealCanvaSize * (eventData.position / self.ScreenSize)
    self:ShowJoy(true)
end

function mgr:OnPointerUp(eventData)
    if not self.touchStartFlag then
        return
    end
    self.DataSrc.JoyHandle.anchoredPosition = TroopClash_Define.CacheVector2.Zero
    self.DragEvent:Invoke(TroopClash_Define.CacheVector2.Zero)
    self:ShowJoy(false)
end

function mgr:OnDrag(eventData)
    if not self.touchStartFlag then
        return
    end
    local curPos = self.RealCanvaSize * (eventData.position / self.ScreenSize)
    local off = curPos - self.DataSrc.Joystick.anchoredPosition
    -- 摇杆偏移量限制
    if (off.magnitude > self.joySize.x) then
        off = off.normalized * self.joySize.x
    end
    self.DataSrc.JoyHandle.anchoredPosition = off
    self.DragEvent:Invoke(off.normalized)
    local bgPos = self.DataSrc.Joystick.anchoredPosition
    if curPos.x > bgPos.x + self.joyBoundOff then
        bgPos.x = curPos.x - self.joyBoundOff
    elseif curPos.x < bgPos.x - self.joyBoundOff then
        bgPos.x = curPos.x + self.joyBoundOff
    end
    if curPos.y > bgPos.y + self.joyBoundOff then
        bgPos.y = curPos.y - self.joyBoundOff
    elseif curPos.y < bgPos.y - self.joyBoundOff then
        bgPos.y = curPos.y + self.joyBoundOff
    end
    self.DataSrc.Joystick.anchoredPosition = bgPos
end

function mgr:WorldPos2CanvasVec3(wPos)
    local anchorPos = self.sceneMgr.cameraCtrl:WorldToViewportPoint(wPos)
    anchorPos.x = self.RealCanvaSize.x * (anchorPos.x - 0.5)
    anchorPos.y = self.RealCanvaSize.y * (anchorPos.y - 0.5)
    return { x = anchorPos.x, y = anchorPos.y, z = 0 }
end

function mgr:KillTween_Tut()
    if self.tween_Tut ~= nil then
        self.tween_Tut:Kill()
        self.tween_Tut = nil
    end
end

function mgr:KillTween_Task()
    if self.tween_Task ~= nil then
        self.tween_Task:Kill()
        self.tween_Task = nil
    end
end

function mgr:KillTween_BossAttent()
    if self.tween_BossAttent ~= nil then
        self.tween_BossAttent:Kill()
        self.tween_BossAttent = nil
    end
end

function mgr:__delete()
    self:KillTween_Tut()
    self:KillTween_Task()
    self:KillTween_BossAttent()
    if self.DragEvent ~= nil then
        self.DragEvent:Delete()
        self.DragEvent = nil
    end
end

function mgr:AutoAdaptLocalScale(trans, pZ, oriLocalScale)
end

function mgr:ClearCamZ()
end

function mgr:UpdateBossHpSlider(HpSliderImage, MaxHp, curHp)
    local fillAmount = 0
    local num = 0
    local xNum = 250
    curHp = math.floor(curHp)
    if MaxHp > xNum * 20 then
        xNum = MaxHp / 20
    end
    if MaxHp > xNum then
        num = math.floor(curHp / xNum) + 1
        local amount = curHp % xNum
        num = math.floor(curHp / xNum)
        fillAmount = amount / xNum
    else
        fillAmount = curHp / MaxHp
    end
    HpSliderImage.value = fillAmount
    local dataSrc = self.sliderDataWithBossSlider[HpSliderImage]
    if dataSrc == nil then
        dataSrc = {
            HpText = HpSliderImage.transform:GetChild(2):GetComponent(TroopClash_Define.TypeOf.UIText),
            NumText = HpSliderImage.transform:GetChild(3):GetComponent(TroopClash_Define.TypeOf.UIText),
            BackgroundImage = HpSliderImage.transform:GetChild(1):GetComponent(TroopClash_Define.TypeOf.UIImage),
            fillImage = HpSliderImage.transform:GetChild(1):GetChild(0):GetChild(0):GetComponent(TroopClash_Define
                .TypeOf.UIImage)
        }
        self.sliderDataWithBossSlider[HpSliderImage] = dataSrc
    end
    if num == 0 then
        dataSrc.BackgroundImage.color = TroopClash_Define.CacheColor.Black
        dataSrc.fillImage.color = TroopClash_Define.CacheColor.Red
    elseif num % 2 == 0 then
        dataSrc.BackgroundImage.color = TroopClash_Define.CacheColor.White
        dataSrc.fillImage.color = TroopClash_Define.CacheColor.Red
    else
        dataSrc.BackgroundImage.color = TroopClash_Define.CacheColor.Red
        dataSrc.fillImage.color = TroopClash_Define.CacheColor.White
    end

    -- FD1784  292525
    dataSrc.HpText.text = tostring(curHp)
    dataSrc.NumText.text = tostring("x" .. num)
end

function mgr:UpdateHpSliderXYZ(HpSliderImage, curHp, MaxHp, pX, pY, pZ, isBoss, parent, lastPosX, lastPosZ)
    local tmpParent = parent or HpSliderImage.transform.parent
    local canvasPos = self:WorldPos2CanvasVec3({ x = pX, y = pY, z = pZ })
    tmpParent.localPosition = canvasPos

    if isBoss then
        self:UpdateBossHpSlider(HpSliderImage, MaxHp, curHp)
        return
    end
    local amount = curHp / MaxHp
    if amount >= 1 then
        return
    end
    HpSliderImage.value = amount
end

function mgr:DestoryHpSlider(HpSliderImage)
    TroopClash_Define.CS.NeeGame.ReturnObject(HpSliderImage.transform.parent.gameObject)
end

function mgr:SpawnHpSlider(playerType, pos, isBoss)
    local name = cysoldierssortie_PoolObjectName.MonsterHpSlider
    local isPlayer = false
    if playerType then
        if playerType == 1 then
            name = cysoldierssortie_PoolObjectName.PlayerHpSlider
        else
            name = cysoldierssortie_PoolObjectName.PlayerHpHero
            isPlayer = true
        end
    end
    if isBoss then
        name = cysoldierssortie_PoolObjectName.BossHpSlider
    end
    local HpSlider = TroopClash_Define.CS.NeeGame.PoolObject(name, self.DataSrc.HpSliderParent)
    local imgTransf = HpSlider.transform:Find("Slider")
    local HpSliderImage = imgTransf:GetComponent(TroopClash_Define.TypeOf.UISlider)
    HpSliderImage.value = 1
    TroopClash_Define.SetTransformLocalScale(imgTransf, 0.5, 0.5, 1)
    TroopClash_Define.SetTransformLocalPositionAndLocalRotation(imgTransf, 0, 0, 0, 0, 0, 0)
    if isPlayer then
        local avatar = HpSlider.transform:Find("Avatar")
        avatar.gameObject.layer = 2
        TroopClash_Define.SetTransformLocalScale(avatar, 1, 1, 1)
        TroopClash_Define.SetTransformLocalPositionAndLocalRotation(avatar, -40, 0, 0, 0, 0, 0)
    end
    HpSlider.transform.position = pos
    TroopClash_Define.SetTransformLocalScale(HpSlider.transform, 1.5, 1.5, 1.5)
    return HpSliderImage
end

function mgr:SpawnHpNumAniTextXYZ(attack, pX, pY, pZ, isCritical, skillView)
    isCritical = isCritical or false
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local HpNumAni = nil
    local HpTrans = nil
    local parent = self.sceneMgr.DataSrc.NumTextParent
    if skillView then
        HpNumAni = TroopClash_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.SkillHpNumAniText, parent)
        HpTrans = poolMgr:GetTransform(HpNumAni)
    elseif isCritical then
        HpNumAni = TroopClash_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.CriticalHpNumAniText, parent)
        HpTrans = poolMgr:GetTransform(HpNumAni)
    else
        HpNumAni = TroopClash_Define.CS.NeeGame.PoolObject(cysoldierssortie_PoolObjectName.HpNumAniText, parent)
        HpTrans = poolMgr:GetTransform(HpNumAni)
    end

    TroopClash_Define.SetTransformPositionXYZ(HpTrans, pX, pY, pZ)
    local HpNumAniText = nil
    if poolMgr then
        HpNumAniText = poolMgr:GetHpAnimText(HpNumAni)
    end
    if isCritical then
        TroopClash_Define.SetTransformLocalScale(HpTrans, 1, 1, 1)
        if HpNumAniText then
            TroopClash_Define.SetTransformLocalScale(HpNumAniText.transform, 0.7, 0.7, 1)
        end
    else
        TroopClash_Define.SetTransformLocalScale(HpTrans, 0.6, 0.6, 1)
    end
    if HpNumAniText then
        if isCritical then
            cysoldierssortie_lua_util:Append("C")
            cysoldierssortie_lua_util:Append("-")
            cysoldierssortie_lua_util:Append(math.floor(attack))
            HpNumAniText.text = cysoldierssortie_lua_util:ToString()
        else
            cysoldierssortie_lua_util:Append("-")
            cysoldierssortie_lua_util:Append(math.floor(attack))
            HpNumAniText.text = cysoldierssortie_lua_util:ToString()
        end
    end

    local sequenceType = 1
    if skillView then
        if isCritical then
            sequenceType = 4
        else
            sequenceType = 3
        end
    elseif isCritical then
        sequenceType = 2
    end
    cysoldierssortie_ui_hud_anim_config:PlaySequence(HpNumAni, HpTrans, pY, 1, sequenceType)
end

return mgr
