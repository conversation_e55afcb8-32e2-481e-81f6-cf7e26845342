---@class tinyrush_spline : TRClass @样条线
local spline = TinyRush_CreateClass("tinyrush_spline")
-- 整条曲线长度
spline.wayLength = nil
-- 构成曲线的点列表
spline.wayPath = nil
--- 初始化
---@param paths tinyrush_vector3[] 传入路径位置数组
---@param vertexCount number 每条曲线线段数
---@param loop boolean 是否循环
function spline:ctor(paths, vertexCount, loop)
    local newPaths = TinyRush_Extern.Func_DrawCatmullRomSpline(vertexCount, loop, paths)
    self.wayPath = {}
    local count = #newPaths
    local tmpWayLength = 0
    local upAxis = TinyRush_Vector3.up()
    for i = 1, count, 1 do
        self.wayPath[i] = {
            pos = newPaths[i]
        }
        if i >= count then
            if loop then
                self.wayPath[i].rot = self.wayPath[1].rot
            else
                self.wayPath[i].rot = self.wayPath[count - 1].rot
            end
        else
            self.wayPath[i].rot = TinyRush_Quaternion.LookRotation((newPaths[i + 1] - newPaths[i]):normalized(), upAxis)
        end
        if i > 1 then
            tmpWayLength = tmpWayLength + TinyRush_Vector3.Distance(self.wayPath[i - 1].pos, self.wayPath[i].pos)
        end
        self.wayPath[i].distanceInCurve = tmpWayLength
    end
    self.wayLength = tmpWayLength
end
--- 采样样条线
---@param p number 传入曲线上的比例0-1
function spline:GetSampleByPercent(p)
    return self:GetSampleByDistance(self.wayLength * p)
end
--- 采样样条线
---@param d number 传入曲线上的长度
function spline:GetSampleByDistance(d)
    if d > self.wayLength then
        d = d - self.wayLength
    end
    local pathNum = #self.wayPath
    local previous = self.wayPath[1]
    local next = self.wayPath[pathNum]
    for i = 2, pathNum, 1 do
        local current = self.wayPath[i]
        if current.distanceInCurve >= d then
            next = current
            break
        end
        previous = current
    end
    local t = 1
    if next.distanceInCurve > previous.distanceInCurve then
        t = (d - previous.distanceInCurve) / (next.distanceInCurve - previous.distanceInCurve)
    end
    return {
        pos = TinyRush_Vector3.Lerp(previous.pos, next.pos, t),
        rot = TinyRush_Quaternion.Lerp(previous.rot, next.rot, t),
        distanceInCurve = math.lerp(previous.distanceInCurve, next.distanceInCurve, t)
    }
end
return spline
