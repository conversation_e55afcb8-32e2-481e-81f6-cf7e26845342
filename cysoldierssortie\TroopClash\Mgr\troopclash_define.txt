---@class troopclash_define
local define = {}

define.ApiHelper = CS.XLuaUtil.LuaApiHelper
define.event = require "event"
define.game_scheme = require "game_scheme"
define.minigame_mgr = require "minigame_mgr"
define.minigame_buff_mgr = require "minigame_buff_mgr"
define.TweenEase = CS.DG.Tweening.Ease
define.TweenLoopType = CS.DG.Tweening.LoopType
define.TweenPathType = CS.DG.Tweening.PathType
define.DOTween = CS.DG.Tweening.DOTween
define.DOVirtual = CS.DG.Tweening.DOVirtual
define.Physics = CS.UnityEngine.Physics
define.NavMesh = CS.UnityEngine.AI.NavMesh
define.IgnoreCollision = define.Physics.IgnoreCollision
define.LookAtTargetSystem = CS.cysoldierssortie.LookAtTargetSystem
define.Matrix4x4 = CS.UnityEngine.Matrix4x4

define.CS = {
    GameObject = CS.UnityEngine.GameObject,
    Vector3 = CS.UnityEngine.Vector3,
    Vector2 = CS.UnityEngine.Vector2,
    Quaternion = CS.UnityEngine.Quaternion,
    NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame,
    Color = CS.UnityEngine.Color,
}

define.GetTransformPositionXYZ = define.ApiHelper.GetTransformPositionXYZ
define.SetTransformPositionXYZ = define.ApiHelper.SetTransformPositionXYZ
define.SetTransformPositionAndRotation = define.ApiHelper.SetTransformPositionAndRotation
define.SetTransformLocalPositionAndLocalRotation = define.ApiHelper.SetTransformLocalPositionAndLocalRotation
define.SetTransformLocalScale = define.ApiHelper.SetTransformLocalScale

define.TypeOf = {
    NeeReferCollection = typeof(CS.CasualGame.lib_ChuagnYi.NeeG.NeeReferCollection),
    LuaMono = typeof(CS.CasualGame.lib_ChuagnYi.LuaMono),
    LuaMonoEvent = typeof(CS.CasualGame.lib_ChuagnYi.MonoFunc.LuaMonoEvent),
    UISlider = typeof(CS.UnityEngine.UI.Slider),
    UIText = typeof(CS.UnityEngine.UI.Text),
    UIImage = typeof(CS.UnityEngine.UI.Image),
    ParticleSystem = typeof(CS.UnityEngine.ParticleSystem),
}

define.CacheVector3 = {
    Zero = define.CS.Vector3.zero,
    One = define.CS.Vector3.one,
    Forward = define.CS.Vector3.forward,
    Up = define.CS.Vector3.up,
    Down = define.CS.Vector3.down,
    Right = define.CS.Vector3.right,
    Left = define.CS.Vector3.left,
}

define.CacheVector2 = {
    Zero = define.CS.Vector2.zero,
}

define.CacheColor = {
    Black = define.CS.Color.black,
    White = define.CS.Color.white,
    Red = define.CS.Color.red,
}

define.AbPath = {
    NeeGameEntry = "cysoldierssortie/troopclash/prefab/neegameentry_troopclash.prefab",
    MainScene = "cysoldierssortie/troopclash/prefab/mainscene.prefab",
    MainPanel = "cysoldierssortie/troopclash/prefab/mainpanel.prefab",
    HeroAnimator = "cysoldierssortie/troopclash/art/heroanimation/heroanimator.controller",
    LevelJsonFormat = "cysoldierssortie/troopclash/data/level/level%d.data",
    MapPrefabFormat = "cysoldierssortie/troopclash/prefab/maps/%s.prefab",
    NavMeshFormat = "cysoldierssortie/troopclash/prefab/maps/%s.asset",
    DataPair = "cysoldierssortie/troopclash/prefab/datapair.prefab",
    PrefabPair = "cysoldierssortie/troopclash/prefab/prefabpair.prefab",
    SoldierPrefab = "art/characters/shibing/prefabs/shibing_simple.prefab",
    LangPath = "cysoldierssortie/troopclash/tablecsv/lang.csv"
}

---@class troopclash_TeamSpawnType
define.TeamSpawnType = {
    Pos = "Pos",     --固定位置刷出，不受时间影响
    Timer = "Timer", --跟随关卡时间刷出
}

---@class troopclash_PropType
define.PropType = {
    Soldier = 1,       --道具小兵
    Weapon = 2,        --道具武器
    SpreadSoldier = 3, --散开小兵
}

---@class troopclash_LevelPassType
define.LevelPassType = {
    AllEnemy = 1, --消灭所有敌人
    BossOnly = 2, --只消灭boss
}

define.Params = {
    PlayerMoveSpeed = 5,                        --玩家小队移动速度
    PlayerSearchRange = 10,                     --每个玩家单位索敌范围
    EnemySearchRange = 100000,                  -- 每个敌人单位索敌范围
    PlayerAtkRange = 10,                        --玩家单位攻击范围
    MaxViewActor = math.huge,                   --可显示的最大敌人数量
    SoldierMaxLevel = 5,                        --小兵最大等级
    SoldierAnim_RunAttack = "Run_Skill01_Loop", --小兵走A动画
}

define.SoldierUnitIDWithLevel = { 101, 102, 103, 104, 105 }

function define.Func_GetDistance(x1, z1, x2, z2)
    local num = x1 - x2
    local num2 = z1 - z2
    return math.sqrt(num * num + num2 * num2)
end

local Func_UnlimitBezierCurve = function(points, t)
    local temp = {}
    local n = #points
    for i = 1, n do
        temp[i] = points[i]
    end
    for i = 1, n do
        for j = 1, n - i do
            temp[j] = define.CS.Vector3.Lerp(temp[j], temp[j + 1], t)
        end
    end
    return temp[1]
end

--- 创建贝塞尔曲线
---@param target table[] 传入路径位置数组
---@param vertexCount number 曲线段数
function define.Func_DrawBezierSplineUnlimit(target, vertexCount)
    if target == nil or #target < 3 then
        return nil
    end
    local pointList = {}
    for ratio = 0, 1.001, 1 / vertexCount do
        local bezierPoint = Func_UnlimitBezierCurve(target, ratio)
        table.insert(pointList, bezierPoint)
    end
    return pointList
end

---圆内随机一个点
function define.Func_InsideUnitCircle(minRadius, maxRadius)
    -- 生成随机角度（0 到 2π）
    local angle = math.random() * 2 * math.pi
    -- 生成随机半径的平方根（确保均匀分布）
    local radius = math.lerp(minRadius, maxRadius, math.random())
    -- 转换为笛卡尔坐标
    local x = radius * math.cos(angle)
    local y = radius * math.sin(angle)
    return { x = x, y = y } -- 或返回两个值 x, y
end

function define.Func_GetPathByCurve(oriPos, endPos, curvePath, curPathCount)
    local scale = define.CS.Vector3.Distance(oriPos, endPos)
    local matrix = define.Matrix4x4.TRS(oriPos,
        define.CS.Quaternion.FromToRotation(define.CacheVector3.Right,
            (endPos - oriPos).normalized), { x = scale, y = scale, z = scale })
    local moveLength = 0
    local newPath = {}
    for i = 1, curPathCount do
        newPath[i] = matrix:MultiplyPoint(curvePath[i])
        if i > 1 then
            moveLength = moveLength + define.CS.Vector3.Distance(newPath[i - 1], newPath[i])
        end
    end
    return newPath, moveLength
end

return define
