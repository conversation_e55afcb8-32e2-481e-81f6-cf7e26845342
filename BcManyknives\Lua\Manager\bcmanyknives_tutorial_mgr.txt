local tutorial_mgr = TinyRush_CreateClass("bcmanyknives_tutorial_mgr"):baseClass(Tiny<PERSON><PERSON>_Scope):interface(
    TinyRush_IInit, TinyRush_IUpdate)

tutorial_mgr.uiMgr = nil
tutorial_mgr.sceneMgr = nil
tutorial_mgr.gameMgr = nil
tutorial_mgr.langMgr = nil
tutorial_mgr.readyFlag = nil

tutorial_mgr.dataSrc = nil
-- 引导
tutorial_mgr.tutBg = nil
tutorial_mgr.maskNor = nil
tutorial_mgr.maskHollow = nil
tutorial_mgr.tutGuideImgBg = nil
tutorial_mgr.tutGuideImg = nil
tutorial_mgr.tutGuideTextBg = nil
tutorial_mgr.tutGuideText = nil
tutorial_mgr.tutHideProcess = nil
tutorial_mgr.tutTopGuideTextBg = nil
tutorial_mgr.tutTopGuideText = nil
tutorial_mgr.guideSp1 = nil
tutorial_mgr.guideSp2 = nil
tutorial_mgr.guideSp3 = nil

tutorial_mgr.tutShowFlag = nil
tutorial_mgr.tutShowTimer = nil
tutorial_mgr.tutTopShowFlag = nil
tutorial_mgr.tutTopShowTimer = nil

tutorial_mgr.lv1Enemy = nil
tutorial_mgr.lv1Flag = nil
tutorial_mgr.lv2Flag = nil
tutorial_mgr.lv2Flag2 = nil
tutorial_mgr.lv2Prop = nil
tutorial_mgr.lv2Timer = nil
tutorial_mgr.lv3Enemy = nil
tutorial_mgr.lv3Flag = nil
tutorial_mgr.lv3Flag2 = nil
tutorial_mgr.lv3Prop = nil
tutorial_mgr.lv3Timer = nil
tutorial_mgr.lv3Flag3 = nil
tutorial_mgr.lv9Enemy = nil
tutorial_mgr.lv9Flag = nil

tutorial_mgr.prefs_fleetfoot = nil
tutorial_mgr.prefs_hp = nil
tutorial_mgr.prefs_bigSword = nil
tutorial_mgr.prefs_miasma = nil
tutorial_mgr.prefs_snow = nil
tutorial_mgr.prefs_fire = nil
tutorial_mgr.prefs_light = nil

local lv2CD = 6
local lv3CD = 5
local tutShowTimerMax = 2
local tutTopShowTimerMax = 3
local vecZero = bc_CS_Vector3.zero
local vecOne = bc_CS_Vector3.one
local bound = 0.5
local prefabKey = {
    fleetfoot = "bcManyKnives_Fleetfoot",
    hp = "bcManyKnives_HP",
    bigSword = "bcManyKnives_BigSword",
    miasma = "bcManyKnives_Miasma",
    snow = "bcManyKnives_Snow",
    fire = "bcManyKnives_Fire",
    light = "bcManyKnives_Light"
}
local PlayerPrefs = CS.UnityEngine.PlayerPrefs

function tutorial_mgr:rush_OnInit()
    self.sceneMgr = self.lifeScope:get("bcmanyknives_scene_mgr")
    self.uiMgr = self.lifeScope:get("bcmanyknives_ui_mgr")
    self.gameMgr = self.lifeScope:get("bcmanyknives_game_mgr")
    self.langMgr = self.lifeScope:get("bcmanyknives_lang_mgr")
end

function tutorial_mgr:rush_OnUpdate(deltaTime)
    if not self.readyFlag then
        return
    end
    if self.tutShowFlag then
        self.tutShowTimer = self.tutShowTimer + deltaTime
        self:TutHideProcess(self.tutShowTimer / tutShowTimerMax)
        if self.tutShowTimer >= tutShowTimerMax then
            self:ShowTutText(nil)
            if self.lv2Flag and self.lv2Prop ~= nil then
                self.lv2Prop.collider.enabled = true
                self.lv2Prop = nil
            end
            if self.lv3Flag3 and self.lv3Prop ~= nil then
                self.lv3Prop.collider.enabled = true
                self.lv3Prop = nil
            end
        end
    end
    if self.tutTopShowFlag then
        self.tutTopShowTimer = self.tutTopShowTimer + deltaTime
        if self.tutTopShowTimer >= tutTopShowTimerMax then
            self:ShowTopTut(nil)
        end
    end
    if self.sceneMgr.pauseBind.value then
        return
    end

    if self.gameMgr.level == 1 then
        if not self.lv1Flag and self.lv1Enemy ~= nil then
            -- 位于屏幕中，开始引导
            if self:CheckInView(self.lv1Enemy.transform.position) then
                self.lv1Flag = true
                self:ShowTutText(self.langMgr:GetLangById(105910003), 1, self.guideSp1)
            end
        end
    elseif self.gameMgr.level == 2 then
        if not self.lv2Flag then
            self.lv2Timer = self.lv2Timer + deltaTime
            if self.lv2Timer >= lv2CD then
                self.lv2Flag = true
                -- 生成一个道具
                local spawnPos = self:GetNearstPlayerPos()
                self.lv2Prop = self.sceneMgr:SpawnOneProp(ManyKnivesDefine.propType.craze, spawnPos)
                self.lv2Prop.collider.enabled = false
                spawnPos = self.lv2Prop.transform.position
                spawnPos = self.sceneMgr.cameraCtrl:WorldToScreenPoint(spawnPos)
                self:ShowTutText(self.langMgr:GetLangById(105910005), 2, bc_CS_Vector2(spawnPos.x, spawnPos.y))
                self.lv2Flag2 = true
            end
        end
    elseif self.gameMgr.level == 3 then
        if not self.lv3Flag and self.lv3Enemy ~= nil then
            -- 位于屏幕中，开始引导
            if self:CheckInView(self.lv3Enemy.transform.position) then
                self.lv3Flag = true
                self:ShowTutText(self.langMgr:GetLangById(105910004), 1, self.guideSp2)
                self.lv3Flag2 = true
                self.lv3Timer = 0
            end
        end
        if self.lv3Flag and self.lv3Flag2 then
            self.lv3Timer = self.lv3Timer + deltaTime
            if self.lv3Timer >= lv3CD then
                self.lv3Flag2 = false
                -- 生成一个道具
                local spawnPos = self:GetNearstPlayerPos()
                self.lv3Prop = self.sceneMgr:SpawnOneProp(ManyKnivesDefine.propType.blade_fire, spawnPos)
                self.lv3Prop.collider.enabled = false
                spawnPos = self.lv3Prop.transform.position
                spawnPos = self.sceneMgr.cameraCtrl:WorldToScreenPoint(spawnPos)
                self:ShowTutText(self.langMgr:GetLangById(105910007), 2, bc_CS_Vector2(spawnPos.x, spawnPos.y))
                self.lv3Flag3 = true
            end
        end
    elseif self.gameMgr.level == 9 then
        if not self.lv9Flag and self.lv9Enemy ~= nil then
            -- 位于屏幕中，开始引导
            if self:CheckInView(self.lv9Enemy.transform.position) then
                self.lv9Flag = true
                self:ShowTutText(self.langMgr:GetLangById(1059100011), 1, self.guideSp3)
            end
        end
    end
end

function tutorial_mgr:CheckInView(enemyPos)
    local viewMin = self.sceneMgr.cameraCtrl:ViewportToWorldPoint(vecZero)
    local viewMax = self.sceneMgr.cameraCtrl:ViewportToWorldPoint(vecOne)
    -- 位于屏幕中，开始引导
    return enemyPos.x > viewMin.x + bound and enemyPos.x < viewMax.x - bound and enemyPos.y > viewMin.y + bound and
               enemyPos.y < viewMax.y - bound
end

function tutorial_mgr:GetNearstPlayerPos()
    local spawnPos = self.sceneMgr.cameraCtrl:ViewportToWorldPoint(bc_CS_Vector3(0.5, 0.5, 0))
    local playerPos = self.sceneMgr.rolePlayer.transform.position
    local dir = spawnPos - playerPos
    dir.z = 0
    dir = dir.normalized
    spawnPos = playerPos + dir * 4
    return spawnPos
end

local tutGuidePosY = -174.5

-- type: 1图片引导，2挖洞引导
function tutorial_mgr:ShowTutText(textId, type, ...)
    self.tutShowFlag = textId ~= nil
    self.tutBg.gameObject:SetActive(self.tutShowFlag)
    self.sceneMgr.pauseBind:setValue(self.tutShowFlag)
    if self.tutShowFlag then
        self.tutGuideText.text = textId
        ManyKnivesDefine.Func_SetTextLayoutWithFixed(self.tutGuideText, 0, 510)
        self.tutGuideTextBg:SetSizeWithCurrentAnchors(CS.UnityEngine.RectTransform.Axis.Vertical,
            self.tutGuideText.preferredHeight + 50)
        if type == 1 then
            self.maskNor.gameObject:SetActive(true)
            self.maskHollow.gameObject:SetActive(false)
            self.tutGuideImgBg.gameObject:SetActive(true)
            local sprite = ...
            self.tutGuideImg.sprite = sprite
            self.tutGuideTextBg.anchoredPosition = bc_CS_Vector2(0, tutGuidePosY)
        else
            self.maskNor.gameObject:SetActive(false)
            self.maskHollow.gameObject:SetActive(true)
            self.tutGuideImgBg.gameObject:SetActive(false)
            local uv = ...
            uv = uv / self.lifeScope.ScreenSize * self.uiMgr.realCanvaSize - self.uiMgr.realCanvaSize * 0.5
            if uv.y < tutGuidePosY + 100 then
                self.tutGuideTextBg.anchoredPosition = bc_CS_Vector2(0, uv.y + 100 + self.tutGuideTextBg.sizeDelta.y)
            end
            uv = (uv + self.maskHollow.rectTransform.sizeDelta * 0.5) / self.maskHollow.rectTransform.sizeDelta
            self.maskHollow.material:SetVector("_CenterPos", bc_CS_Vector4(uv.x, uv.y, 0, 0))
        end
        self.tutShowTimer = 0
        self:TutHideProcess(0)
    end
end

function tutorial_mgr:ShowTopTut(textId)
    self.tutTopShowFlag = textId ~= nil
    self.tutTopGuideTextBg.gameObject:SetActive(self.tutTopShowFlag)
    if self.tutTopShowFlag then
        self.tutTopGuideText.text = textId
        ManyKnivesDefine.Func_SetTextLayoutWithFixed(self.tutTopGuideText, 0, 410)
        self.tutTopGuideTextBg:SetSizeWithCurrentAnchors(CS.UnityEngine.RectTransform.Axis.Vertical,
            self.tutTopGuideText.preferredHeight + 30)
        self.tutTopShowTimer = 0
    end
end

function tutorial_mgr:ShowBladeMaxGuide()
    self:ShowTopTut(self.langMgr:GetLangById(1059100024))
end

function tutorial_mgr:ShowPropTopGuide_Fleetfoot()
    if not self.prefs_fleetfoot then
        self.prefs_fleetfoot = PlayerPrefs.GetInt(prefabKey.fleetfoot) == 1
        if not self.prefs_fleetfoot then
            self:ShowTopTut(self.langMgr:GetLangById(1059100017))
            self.prefs_fleetfoot = true
            PlayerPrefs.SetInt(prefabKey.fleetfoot, 1)
        end
    end
end
function tutorial_mgr:ShowPropTopGuide_HP()
    if not self.prefs_hp then
        self.prefs_hp = PlayerPrefs.GetInt(prefabKey.hp) == 1
        if not self.prefs_hp then
            self:ShowTopTut(self.langMgr:GetLangById(1059100018))
            self.prefs_hp = true
            PlayerPrefs.SetInt(prefabKey.hp, 1)
        end
    end
end
function tutorial_mgr:ShowPropTopGuide_bigSword()
    if not self.prefs_bigSword then
        self.prefs_bigSword = PlayerPrefs.GetInt(prefabKey.bigSword) == 1
        if not self.prefs_bigSword then
            self:ShowTopTut(self.langMgr:GetLangById(1059100019))
            self.prefs_bigSword = true
            PlayerPrefs.SetInt(prefabKey.bigSword, 1)
        end
    end
end
function tutorial_mgr:ShowPropTopGuide_miasma()
    if not self.prefs_miasma then
        self.prefs_miasma = PlayerPrefs.GetInt(prefabKey.miasma) == 1
        if not self.prefs_miasma then
            self:ShowTopTut(self.langMgr:GetLangById(1059100020))
            self.prefs_miasma = true
            PlayerPrefs.SetInt(prefabKey.miasma, 1)
        end
    end
end
function tutorial_mgr:ShowPropTopGuide_snow()
    if not self.prefs_snow then
        self.prefs_snow = PlayerPrefs.GetInt(prefabKey.snow) == 1
        if not self.prefs_snow then
            self:ShowTopTut(self.langMgr:GetLangById(1059100021))
            self.prefs_snow = true
            PlayerPrefs.SetInt(prefabKey.snow, 1)
        end
    end
end
function tutorial_mgr:ShowPropTopGuide_light()
    if not self.prefs_light then
        self.prefs_light = PlayerPrefs.GetInt(prefabKey.light) == 1
        if not self.prefs_light then
            self:ShowTopTut(self.langMgr:GetLangById(1059100022))
            self.prefs_light = true
            PlayerPrefs.SetInt(prefabKey.light, 1)
        end
    end
end

function tutorial_mgr:ShowPropTopGuide_Craze()
    if self.lv2Flag and self.lv2Flag2 then
        self.lv2Flag2 = false
        self:ShowTopTut(self.langMgr:GetLangById(105910006))
    end
end
function tutorial_mgr:ShowPropTopGuide_Fire()
    if self.lv3Flag and self.lv3Flag3 then
        self.lv3Flag3 = false
        self:ShowTopTut(self.langMgr:GetLangById(105910008))
    else
        if not self.prefs_fire then
            self.prefs_fire = PlayerPrefs.GetInt(prefabKey.fire) == 1
            if not self.prefs_fire then
                self:ShowTopTut(self.langMgr:GetLangById(1059100023))
                self.prefs_fire = true
                PlayerPrefs.SetInt(prefabKey.fire, 1)
            end
        end
    end
end
function tutorial_mgr:ShowBossGuide_Rush()
    if self.gameMgr.level == 4 then
        self:ShowTopTut(self.langMgr:GetLangById(105910009))
    end
end
function tutorial_mgr:ShowBossGuide_Fire()
    if self.gameMgr.level == 8 then
        self:ShowTopTut(self.langMgr:GetLangById(1059100010))
    end
end
function tutorial_mgr:ShowBossGuide_Miasma()
    if self.gameMgr.level == 12 then
        self:ShowTopTut(self.langMgr:GetLangById(1059100016))
    end
end

function tutorial_mgr:TutHideProcess(value)
    self.tutHideProcess.fillAmount = value
end

function tutorial_mgr:Ready()
    local max = math.max(self.uiMgr.realCanvaSize.x, self.uiMgr.realCanvaSize.y)
    self.maskHollow.rectTransform.sizeDelta = bc_CS_Vector2(max, max)
    self:ResetGame()
end

function tutorial_mgr:SetLuaB(luaB)
    luaB:Awake()
    self.dataSrc = CshapToLuaTable_New(luaB)
    self.tutBg = self.dataSrc["tutorialBg"]
    self.maskNor = self.dataSrc["mask_Normal"]
    self.maskHollow = self.dataSrc["mask_Hollow"]
    self.tutGuideImgBg = self.dataSrc["tutGuideImgBg"]
    self.tutGuideImg = self.dataSrc["tutGuideImg"]
    self.tutGuideTextBg = self.dataSrc["tutGuideTextBg"]
    self.tutGuideText = self.dataSrc["tutGuideText"]
    self.tutHideProcess = self.dataSrc["tutHideProcess"]
    self.tutTopGuideTextBg = self.dataSrc["topGuideTextBg"]
    self.tutTopGuideText = self.dataSrc["topTutGuideText"]
    self.guideSp1 = self.dataSrc["guide1"]
    self.guideSp2 = self.dataSrc["guide2"]
    self.guideSp3 = self.dataSrc["guide3"]
end

function tutorial_mgr:SetParams(...)
    if self.gameMgr.level == 1 then
        self.lv1Enemy = ...
    elseif self.gameMgr.level == 3 then
        self.lv3Enemy = ...
    elseif self.gameMgr.level == 9 then
        self.lv9Enemy = ...
    end
end

function tutorial_mgr:GameStart()
    self.readyFlag = true
end

function tutorial_mgr:OverBattle()
    self.readyFlag = false
end

function tutorial_mgr:ResetGame()
    self.lv1Enemy = nil
    self.readyFlag = false
    self.lv1Flag = false
    self.lv2Flag = false
    self.lv2Flag2 = false
    self.lv2Timer = 0
    self.lv2Prop = nil
    self.lv3Flag = false
    self.lv3Enemy = nil
    self.lv3Flag2 = false
    self.lv3Flag3 = false
    self.lv3Prop = nil
    self.lv9Flag = false
    self.lv9Enemy = nil
    self:ShowTutText(nil)
    self:ShowTopTut(nil)
end

function tutorial_mgr:dispose()
    self.readyFlag = false
    self.__base:dispose()
end

return tutorial_mgr
