---@class tinyrush_scope : TRClass
---@alias TinyRush_Scope tinyrush_scope
local tinyrush_scope = TinyRush_CreateClass("tinyrush_scope")
---@type tinyrush_lifescope  记录当前位于的生命域
tinyrush_scope.lifeScope = nil

function tinyrush_scope:isScope()
    return true
end

--- 设置生命域
---@param lifeScope tinyrush_lifescope
function tinyrush_scope:setLifeScope(lifeScope)
    self.lifeScope = lifeScope
end

return tinyrush_scope
