---@class tinyrush_load_mgr : <PERSON><PERSON><PERSON>_Scope,<PERSON><PERSON>ush_IInit,TinyRush_IUpdate
local tinyrush_load_mgr = TinyRush_CreateClass("tinyrush_load_mgr"):baseClass(TinyRush_Scope):interface(TinyRush_IInit,
    TinyRush_IUpdate)
tinyrush_load_mgr.loader = nil
-- 是否使用协程方式加载，默认false
tinyrush_load_mgr.loadByCoroutine = nil
-- 是否多线程加载
tinyrush_load_mgr.multiThread = nil
tinyrush_load_mgr.showLoadLog = nil
tinyrush_load_mgr.taskQueue = nil
tinyrush_load_mgr.taskCompleted = nil
tinyrush_load_mgr.taskCo = nil
tinyrush_load_mgr.uTaskCount = nil
tinyrush_load_mgr.uTaskIndex = nil
tinyrush_load_mgr.uTaskContinueFlag = nil
tinyrush_load_mgr.uTaskOverCount = nil
tinyrush_load_mgr.uTaskOverIndex = nil
tinyrush_load_mgr.uTaskComFlag = nil
tinyrush_load_mgr.readyFlag = nil

function tinyrush_load_mgr:ctor(loader, ...)
    self.loader = loader
    local logFlag, cFlag, multiFlag = ...
    if type(logFlag) == "boolean" then
        self.showLoadLog = logFlag
    else
        self.showLoadLog = false
    end
    if type(cFlag) == "boolean" then
        self.loadByCoroutine = cFlag
    else
        self.loadByCoroutine = false
    end
    if type(multiFlag) == "boolean" then
        self.multiThread = multiFlag
    else
        self.multiThread = false
    end
    self.__base:ctor()
end
function tinyrush_load_mgr:dispose()
    self.readyFlag = false
    self.loader = nil
    self.taskQueue = nil
    self.taskCompleted = nil
    self.taskCo = nil
    self.uTaskCount = nil
    self.uTaskIndex = nil
    self.__base:dispose()
end

function tinyrush_load_mgr:rush_OnInit()
    self:clearTask()
end

function tinyrush_load_mgr:clearTask()
    self.taskQueue = {}
    self.taskCompleted = {}
    self.readyFlag = false
end

function tinyrush_load_mgr:rush_OnUpdate()
    if not self.readyFlag or self.multiThread then
        return
    end
    if self.loadByCoroutine then
        if self.taskCo then
            coroutine.resume(self.taskCo, self)
        end
    else
        self:invokeTaskUpdate()
    end
end
function tinyrush_load_mgr:invokeTaskUpdate()
    if self.uTaskCount > 0 and self.uTaskIndex <= self.uTaskCount then
        if not self.uTaskContinueFlag then
            self.uTaskContinueFlag = true
            local tmpT = self.taskQueue[self.uTaskIndex]
            local abName = tmpT[1]
            local func = tmpT[2]
            if self.showLoadLog then
                TinyRush_Log("loadTaskQueue:", self.uTaskIndex .. "/" .. self.uTaskCount, abName)
            end
            self.uTaskIndex = self.uTaskIndex + 1
            if abName ~= nil then
                self.loader(abName, function(obj)
                    func(obj)
                    self.uTaskContinueFlag = false
                    self.uTaskComFlag = self.uTaskIndex > self.uTaskCount and self.uTaskOverCount > 0
                end)
            else
                func()
                self.uTaskContinueFlag = false
                self.uTaskComFlag = self.uTaskIndex > self.uTaskCount and self.uTaskOverCount > 0
            end
        end
    end
    if self.uTaskComFlag and self.uTaskOverIndex <= self.uTaskOverCount then
        if not self.uTaskContinueFlag then
            self.uTaskContinueFlag = true
            local func = self.taskCompleted[self.uTaskOverIndex]
            if self.showLoadLog then
                TinyRush_Log("loadCompleted:", self.uTaskOverIndex .. "/" .. self.uTaskOverCount)
            end
            func()
            self.uTaskContinueFlag = false
            self.uTaskOverIndex = self.uTaskOverIndex + 1
        end
    end
end

---协程任务
function tinyrush_load_mgr.invokeTaskCoroutine(self)
    local taskCount = #self.taskQueue
    local taskNum = taskCount
    local taskIndex = 1
    while taskIndex <= taskCount do
        coroutine.yield()
        local tmpT = self.taskQueue[taskIndex]
        local abName = tmpT[1]
        local func = tmpT[2]
        if self.showLoadLog then
            TinyRush_Log("loadTaskQueue:", taskIndex .. "/" .. taskCount, abName)
            local t = debug.getinfo(func, "lnS")
            for key, value in pairs(t) do
                TinyRush_Log(key, value)
            end
        end
        if abName ~= nil then
            self.loader(abName, function(obj)
                func(obj)
                taskNum = taskNum - 1
            end)
        else
            func()
            taskNum = taskNum - 1
        end
        taskIndex = taskIndex + 1
    end
    while taskNum > 0 do
        coroutine.yield()
    end
    local tcCount = #self.taskCompleted
    for k, v in pairs(self.taskCompleted) do
        if self.showLoadLog then
            TinyRush_Log("loadCompleted:", k .. "/" .. tcCount)
            local t = debug.getinfo(v, "lnS")
            for key, value in pairs(t) do
                TinyRush_Log(key, value)
            end
        end
        v()
        coroutine.yield()
    end
    self.taskCo = nil
end

--- 增加一个任务：每帧只执行一个任务。
function tinyrush_load_mgr:addFrameTask(func)
    self.taskQueue[#self.taskQueue + 1] = {nil, func}
end
--- 加载资源任务添加
---@param abName string
---@param call function
function tinyrush_load_mgr:loadAsset(abName, call)
    self.taskQueue[#self.taskQueue + 1] = {abName, call}
end
--- 添加所有任务完成后的回调
---@param call function
function tinyrush_load_mgr:addLoadedCall(call)
    self.taskCompleted[#self.taskCompleted + 1] = call
end

--- 调用此方法，才开始执行加载任务
function tinyrush_load_mgr:loadingInvoke()
    self.readyFlag = true
    if self.multiThread then
        local taskCount = #self.taskQueue
        local checkCompleted = function()
            taskCount = taskCount - 1
            if taskCount < 1 then
                for _, v in pairs(self.taskCompleted) do
                    v()
                end
            end
        end
        for _, v in pairs(self.taskQueue) do
            local abName = v[1]
            local func = v[2]
            if abName ~= nil then
                self.loader(abName, function(obj)
                    func(obj)
                    checkCompleted()
                end)
            else
                func()
                checkCompleted()
            end
        end
    else
        if self.loadByCoroutine then
            self.taskCo = coroutine.create(self.invokeTaskCoroutine)
        else
            self.uTaskIndex = 1
            self.uTaskCount = #self.taskQueue
            self.uTaskContinueFlag = false
            self.uTaskOverCount = #self.taskCompleted
            self.uTaskOverIndex = 1
            self.uTaskComFlag = false
        end
    end
end

return tinyrush_load_mgr
