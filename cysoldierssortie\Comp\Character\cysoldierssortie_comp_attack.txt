local cysoldierssortie_comp_attack = bc_Class("cysoldierssortie_comp_attack") --类名用小游戏名加后缀保证全局唯一
local cysoldierssortie_config_weapon = require("cysoldierssortie_config_weapon")
local bc_Time = bc_Time
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_PlaySkillSpecial=cysoldierssortie_PlaySkillSpecial
local cysoldierssortie_ui_skill_countdown_obj = require("cysoldierssortie_ui_skill_countdown_obj")
local cysoldierssortie_event_name = cysoldierssortie_event_name
function cysoldierssortie_comp_attack.__init(self,data)
    self._character = data.character
    self._weapons = self._character._weapons
    self._weapon_entitys = {}
    self._cds = {}
    self._nextFireTime = 0
    
    self:CreateWeaponEntity()
    self._RefreshCD = function()
        if not self._weapons or #self._weapons<=0 then
            return
        end
        
        for i=1,#self._weapons do
            local weapon = self._weapons[i]
            if weapon._startCD == 1 then
                self._cds[i] =  bc_Time.time + self._weapons[i]._attackSpeed
            else
                self._cds[i] = 0
            end
            if self._ultraShow then
                self._ultraShow:RefreshCountDownData(self._cds[self._ultraIndex])
            end
        end
    end
    local eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    if eventMgr then
        eventMgr:RegisterEvt(self._RefreshCD, cysoldierssortie_event_name.START_GAME)
    end
end


function  cysoldierssortie_comp_attack.__delete(self)
    self:Release()
    local eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    if eventMgr then
        eventMgr:UnRegisterEvt(self._RefreshCD, cysoldierssortie_event_name.START_GAME)
    end
end


function cysoldierssortie_comp_attack:CreateWeaponEntity()
    if not self._weapons or #self._weapons<=0 then
        return
    end

    for i=1,#self._weapons do
        local weapon = self._weapons[i]
        local weaponType = weapon._attackType
        local weapon_config =  cysoldierssortie_config_weapon.WeaponConfig[weaponType]
        if weapon_config then
            local weapon_entity = weapon_config._createWeaponEntity(self,self._weapons[i])
            self._weapon_entitys[#self._weapon_entitys+1] = weapon_entity
            if weapon._startCD == 1 then
                self._cds[#self._cds+1] =  bc_Time.time + self._weapons[i]._attackSpeed
            else
                self._cds[#self._cds+1] = 0 
            end
            local ultra = weapon_entity._ultra or self._weapons[i]._isUltra
            if ultra then
                self._ultraIndex = i
            end
        end
    end
end

function cysoldierssortie_comp_attack:CreateSkillUltraShow()
    if self._ultraIndex and not self._ultraShow then
        self._ultraShow = cysoldierssortie_ui_skill_countdown_obj.New({character = self._character,endTime = self._cds[self._ultraIndex],cd = self._weapons[self._ultraIndex]._attackSpeed})
    end
end

function cysoldierssortie_comp_attack:GetFirstWeapon()
    if not self._weapons or #self._weapons<=0 then
        return nil
    end
    return self._weapons[1]
end


function cysoldierssortie_comp_attack:Fire()
    if not self._weapon_entitys or #self._weapon_entitys <=0 then
        return
    end

    --上一个技能生命周期没结束
    if bc_Time.time < self._nextFireTime then
        return
    end

    if cysoldierssortie_TroopClash and self._character._player then
        ---@type troopclash_player
        local tmpPlayer = self._character._player
        tmpPlayer:CharacterFire(self._character)
    end

    if cysoldierssortie_KingShot and self._character._player then
        ---@type kingshot_player
        local tmpPlayer = self._character._player
        tmpPlayer:CharacterFire(self._character)
    end
    
    local curWeaponIndex = nil
    local priority = -1
    for weaponIndex = 1,#self._weapon_entitys do
        local cd = self._cds[weaponIndex] or 1
        if bc_Time.time >= cd then
            local canSelect = true
            if self._weapons[weaponIndex]._overrideAtkRange and self._weapons[weaponIndex]._overrideAtkRange > 0 then
                if self._character._player then
                    local targetObjs =  self._character._player:GetTargetObjsByWeaponID(self._weapons[weaponIndex]._skillID)
                    if #targetObjs<=0 then
                        canSelect = false
                    end
                end 
            end
            
            if canSelect and self._weapons[weaponIndex]._skillPriority > priority then
                priority = self._weapons[weaponIndex]._skillPriority
                curWeaponIndex = weaponIndex
            end
        end 
    end

    if curWeaponIndex then
        local next_cd = bc_Time.time + self._weapons[curWeaponIndex]._attackSpeed
        self._cds[curWeaponIndex] = next_cd
        local weapon_entity = self._weapon_entitys[curWeaponIndex]
        self._nextFireTime = self._weapons[curWeaponIndex]._skillLoop + bc_Time.time
        --武器逻辑(技能逻辑)
        if weapon_entity then
            weapon_entity:Fire()
            cysoldierssortie_PlaySkillSpecial(weapon_entity._weaponData._skillID)
            if weapon_entity._ultra or self._weapons[curWeaponIndex]._isUltra then
                if self._ultraShow then
                    self._ultraShow:RefreshCountDown(next_cd)
                end
            end
        end
    end
end

function cysoldierssortie_comp_attack:TriggerFire()
    for weaponIndex = 1,#self._weapon_entitys do
        local weapon_entity = self._weapon_entitys[weaponIndex]
        --武器逻辑(技能逻辑)
        if weapon_entity then
            weapon_entity:Fire()
            cysoldierssortie_PlaySkillSpecial(weapon_entity._weaponData._skillID)
        end
    end
end

function cysoldierssortie_comp_attack:StopFire()
    --动画逻辑
    if not self._animator then
        self._animator = self._character:GetAnimator()
    end
    if self._animator then
        self._animator:ResetTrigger(cysoldierssortie_hero_anim_set.Ability)
    end
end

--当前技能生命周期没结束
function cysoldierssortie_comp_attack:Countdown()
    if not self._nextFireTime then
        return 0
    end
    
    local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
    if not attackMgr:EnableProxy(self._character._unitID,self._character._unit_type) then
        return self._nextFireTime - bc_Time.time
    end
    
    local proxy_count_down =  attackMgr:GetSkillCountDownByUnitID(self._character._unitID)
    return proxy_count_down or 0
end

function cysoldierssortie_comp_attack:Release()
    --检测代理
    local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
    if attackMgr:EnableProxy(self._character._unitID,self._character._unit_type) then
        if attackMgr then
            attackMgr:UnregisterAttackCompProxy(self)
        end
    end

    for weaponIndex = 1,#self._weapon_entitys do
        local weapon_entity = self._weapon_entitys[weaponIndex]
        if weapon_entity then
            weapon_entity:OnDisable()
        end
    end

    if self._ultraShow then
        self._ultraShow.Delete(self._ultraShow)
        self._ultraShow = nil
    end
    
    self._weapons = nil
    self._cds = nil
    self._weapon_entitys = nil
    self._ultraIndex = nil
end

return cysoldierssortie_comp_attack