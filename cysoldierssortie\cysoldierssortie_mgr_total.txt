--少部分游戏需要联网同步数据
local isConnectedToNet = false
local util = require "util"
local MonoAgent = CS.cysoldierssortie.MonoAgent
local LuaAgent = require "cysoldierssortie_lua_lifetime_agent"
local MainLoop = CS.War.Script.MainLoop
local cysoldierssortie_mgr_total = bc_Class("cysoldierssortie_mgr_total") --类名用小游戏名加后缀保证全局唯一
local bc_IsNotNull = bc_IsNotNull
cysoldierssortie_mgr_total.dataSrc = nil --gameobject 上 gameluabehaviour组件数据

function cysoldierssortie_mgr_total.__init(self, enter, ...)
    self.enter = enter
    cysoldierssortie_AddMgr(cysoldierssortie_MgrName.total, self)
    cysoldierssortie_AddMgr(cysoldierssortie_MgrName.event, require("cysoldierssortie_event").New())
    cysoldierssortie_AddMgr(cysoldierssortie_MgrName.json, require("cysoldierssortie_json"))
    cysoldierssortie_AddMgr(cysoldierssortie_MgrName.TimerMgr, require("bc_timer_manager"):GetInstance())
    
    local eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    LuaAgent:Init(eventMgr)
    MonoAgent.Instance:BindLua(LuaAgent)
end
-- lua脚本正式开始

local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local NeeGamePath = CS.CasualGame.lib_ChuagnYi.NeeGamePath

-- 加载游戏关卡,分两种情况：

-- 1.如果是在LevelBuildScene中，此时游戏处于前中期开发阶段，所有的关卡(NeeLevel.cs)应该摆在---BuildScene物体下方，而不做成预制体，方便快速删减调整等
-- BuildSceneLevelManager.cs是lua入口，进入cysoldierssortie_enter，在cysoldierssortie_enter中创建cysoldierssortie_mgr，调用LoadLevel函数
-- LoadLevel会读取场景中的NeeGame物体，而不是去从ab/prefab生成
-- LoadLevel会删掉除了唯一开启的Neelevel以外所有的NeeLevel，并且把唯一NeeLevel设置为NeeGame.curSceneLevel

-- 2. 如果是在LuaScene中，此时游戏处于后期测试调优阶段
-- 此时先从gameAbName加载neegameentry预制体(NeeGame）
-- 再根据游戏类型的不同，通过ab/prefab引用/csv来设置NeeGame.curSceneLevel

-- 3.NeeGame.curSceneLevelc是NeeGame.curlevel的预制体，每次进游戏/重置游戏都删掉现有的curlevel生成一个副本成为新的curLevel）
function cysoldierssortie_mgr_total:LoadLevel(level)
    self.levelIndex = level;
    local tmpLvId = self.levelIndex
    local tmpLvId = 500001
    local game_scheme = require "game_scheme"
    local miniLvConfig = game_scheme:MiniLevel_0(tmpLvId)
    local minigame_mgr = require "minigame_mgr"
    minigame_mgr.SetLoadResState(1) --1开始加载neegameentry

    --是小兵大作战模式，进入专属入口
    if miniLvConfig.LevelMode == cysoldierssortie_LevelMode.KingShot then
        cysoldierssortie_KingShot = true
        cysoldierssortie_AddMgr(cysoldierssortie_MgrName.KingShotTotal,
            require(cysoldierssortie_MgrName.KingShotTotal).New(self.enter, miniLvConfig))
    elseif miniLvConfig.LevelMode == cysoldierssortie_LevelMode.TroopClash then
        cysoldierssortie_TroopClash = true
            cysoldierssortie_AddMgr(cysoldierssortie_MgrName.TroopClashTotal,
                    require(cysoldierssortie_MgrName.TroopClashTotal).New(self.enter, miniLvConfig))
    else
        --先读取本地存储数据，再开始游戏
        self:LoadData(function()
            if util.IsObjNull(NeeGame.Instance) then
                local gameAbName = "cysoldierssortie/prefab/neegameentry.prefab"
                cysoldierssortie_LoadPrefab(gameAbName, self.LoadLevel2)
            else
                self:LoadLevel2()
            end
        end)
    end
end

function cysoldierssortie_mgr_total:LoadLevel2()
    local minigame_mgr = require "minigame_mgr"
    minigame_mgr.SetLoadResState(2)--2加载neegameentry完成
    NeeGame.DontDestroyOnLoad(NeeGame.Instance)
    NeeGamePath.GameNameStatic = NeeGame.Instance.gameName
    NeeGamePath.instance = NeeGame.Instance.NeeGamePath
    --通过luaMono来设置luaclass，GameLuaBehavior之间的绑定

    NeeGame.Instance.createLuaComponentDel = function(go)

        local luaMono = go:GetComponent(typeof(CS.CasualGame.lib_ChuagnYi.LuaMono))
        --go:SetActive(false)
        --luaMono:OnValidate2();
        local src = luaMono.luaBehaviour
        local str = luaMono.luaName


        local tempClass = require(str).New(luaMono, luaMono.referCol, luaMono.luaData)
        luaMono.luaComp = tempClass
        tempClass.luaMono = luaMono

        if luaMono.isStaticType then
            cysoldierssortie_AddMgr(luaMono.luaName, luaMono.luaComp)
        end

        src.enabled = true
        --print("bind", luaMono.luaName, src, str, tempClass)
        src:BindLuaTable(tempClass)
        if luaMono.hasLuaMonoEvent then
            local comps = luaMono:GetComponents(typeof(CS.CasualGame.lib_ChuagnYi.MonoFunc.LuaMonoEvent))
            for i = 0, comps.Length - 1 do
                comps[i]:Bind(luaMono)
            end
        end

        --go:SetActive(true)
    end

end

function cysoldierssortie_mgr_total:ReloadLevel(level)
    if self.canReload then
        cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level):ReloadLevel(level)
    end
end

function cysoldierssortie_mgr_total:DestroyMainGame()
    if NeeGame.Instance and bc_IsNotNull(NeeGame.Instance) and NeeGame.Instance.gameObject and bc_IsNotNull(NeeGame.Instance.gameObject) then
        NeeGame.Destroy(NeeGame.Instance.gameObject)
    end
end

function cysoldierssortie_mgr_total:LoadData(doneFunction)
    doneFunction()
end

function cysoldierssortie_mgr_total:SwitchMainLoop(isStartGame,state)
    self._isStartGame = isStartGame
    self._main_loop_counter = 0
    if not self._isStartGame then
        if bc_IsNotNull(MainLoop.Instance) then
            MainLoop.Instance.enabled = true
        end
        return
    end
    if bc_IsNotNull(MainLoop.Instance) then
        MainLoop.Instance.enabled = state
    end
end

function cysoldierssortie_mgr_total:AddMainLoopCounter()
    if not self._isStartGame then
        return
    end
    self._main_loop_counter = self._main_loop_counter or 0
    self._main_loop_counter = self._main_loop_counter + 1
    if self._main_loop_counter >0 then
        if bc_IsNotNull(MainLoop.Instance) then
            MainLoop.Instance.enabled = true
        end
    end
end

function cysoldierssortie_mgr_total:ReduceMainLoopCounter()
    if not self._isStartGame then
        return
    end
    if not self._main_loop_counter or self._main_loop_counter<=0 then
        return
    end
    self._main_loop_counter = self._main_loop_counter -1
    if self._main_loop_counter <=0 then
        if bc_IsNotNull(MainLoop.Instance) then
            MainLoop.Instance.enabled = false
        end
    end
end

return cysoldierssortie_mgr_total