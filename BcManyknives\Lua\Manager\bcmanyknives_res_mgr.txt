local manyknives_res_mgr = TinyRush_CreateClass("bcmanyknives_res_mgr"):baseClass(TinyRush_Scope):interface(
    TinyRush_IInit)

manyknives_res_mgr.loadMgr = nil
manyknives_res_mgr.gameMgr = nil

manyknives_res_mgr.allRoleTable = nil
manyknives_res_mgr.allBladeTable = nil
manyknives_res_mgr.allLevelTable = nil
manyknives_res_mgr.allPropTable = nil
manyknives_res_mgr.allSkillTable = nil
manyknives_res_mgr.mainScenePrefab = nil
manyknives_res_mgr.mainPanelPrefab = nil

manyknives_res_mgr.langData = nil
-- 音频
manyknives_res_mgr.allAudioPrefab = nil
-- 配置表
manyknives_res_mgr.config_roles = nil
manyknives_res_mgr.config_level = nil
manyknives_res_mgr.config_levelBlade = nil
manyknives_res_mgr.config_rushBoss = nil
manyknives_res_mgr.config_fireBoss = nil
manyknives_res_mgr.config_miasmaBoss = nil

manyknives_res_mgr.fontAR = nil

function manyknives_res_mgr:rush_OnInit()
    self.loadMgr = self.lifeScope:get("tinyrush_load_mgr")
    self.gameMgr = self.lifeScope:get("bcmanyknives_game_mgr")
end

--- 获取关卡的预制
---@param lvIndex number
function manyknives_res_mgr:GetLevelPrefab(lvIndex)
    return self.allLevelTable[string.format(ManyKnivesDefine.levelNameFormat, lvIndex)]
end
--- 获取刀刃的预制
---@param name string
function manyknives_res_mgr:GetBladePrefab(name)
    return self.allBladeTable[name]
end
--- 获取角色的预制
---@param name string
function manyknives_res_mgr:GetRolePrefab(name)
    return self.allRoleTable[name]
end
--- 获取道具的预制
---@param name string
function manyknives_res_mgr:GetPropPrefab(name)
    return self.allPropTable[name]
end
--- 获取技能的预制
---@param name string
function manyknives_res_mgr:GetSkillPrefab(name)
    return self.allSkillTable[name]
end

function manyknives_res_mgr:loadAllAssets(call)
    -- 加三个空帧
    local emptyFrame = function()
    end
    self.loadMgr:addFrameTask(emptyFrame)
    self.loadMgr:addFrameTask(emptyFrame)
    self.loadMgr:addFrameTask(emptyFrame)
    self.loadMgr:loadAsset(ManyKnivesDefine.allBladePath, function(obj)
        local tmp = obj:GetComponent(typeof(CS.GameLuaBehaviour_New))
        tmp:Awake()
        self.allBladeTable = CshapToLuaValue_New(tmp)
    end)
    self.loadMgr:loadAsset(ManyKnivesDefine.allRolePath, function(obj)
        local tmp = obj:GetComponent(typeof(CS.GameLuaBehaviour_New))
        tmp:Awake()
        self.allRoleTable = CshapToLuaValue_New(tmp)
    end)
    self.loadMgr:loadAsset(string.format(ManyKnivesDefine.allLevelPath, math.ceil(self.gameMgr.level / 10) * 10),
        function(obj)
            local tmp = obj:GetComponent(typeof(CS.GameLuaBehaviour_New))
            tmp:Awake()
            self.allLevelTable = CshapToLuaValue_New(tmp)
            local levelDataSrc = self.allLevelTable["Datas"]
            levelDataSrc:Awake()
            levelDataSrc = CshapToLuaValue_New(levelDataSrc)
            -- 当前关卡配置
            self.config_level = self.loadCSVByString(levelDataSrc[string.format(ManyKnivesDefine.levelDataFormat,
                self.gameMgr.level)].text, 2, 7)
        end)
    self.loadMgr:loadAsset(ManyKnivesDefine.allPropPath, function(obj)
        local tmp = obj:GetComponent(typeof(CS.GameLuaBehaviour_New))
        tmp:Awake()
        self.allPropTable = CshapToLuaValue_New(tmp)
    end)
    self.loadMgr:loadAsset(ManyKnivesDefine.allSkillPath, function(obj)
        local tmp = obj:GetComponent(typeof(CS.GameLuaBehaviour_New))
        tmp:Awake()
        self.allSkillTable = CshapToLuaValue_New(tmp)
    end)
    self.loadMgr:loadAsset(ManyKnivesDefine.mainScenePath, function(obj)
        self.mainScenePrefab = obj
    end)
    self.loadMgr:loadAsset(ManyKnivesDefine.mainPanelPath, function(obj)
        self.mainPanelPrefab = obj
    end)
    self.loadMgr:loadAsset(ManyKnivesDefine.allAudioPath, function(obj)
        self.allAudioPrefab = obj
    end)
    self.loadMgr:loadAsset(ManyKnivesDefine.langPath, function(obj)
        self.langData = obj
    end)
    self.loadMgr:loadAsset(ManyKnivesDefine.fontARPath, function(obj)
        self.fontAR = obj
    end)

    -- 配置表
    self.loadMgr:loadAsset(ManyKnivesDefine.allDatasPath, function(obj)
        local tmp = obj:GetComponent(typeof(CS.GameLuaBehaviour_New))
        tmp:Awake()
        local rootDataSrc = CshapToLuaValue_New(tmp)
        self.config_levelBlade = self.loadCSVByString(rootDataSrc["autoBlade"].text, 1, 3)
        self.config_rushBoss = self.loadCSVByString(rootDataSrc["rushConfig"].text, 1, 14)
        self.config_fireBoss = self.loadCSVByString(rootDataSrc["fireConfig"].text, 1, 14)
        self.config_miasmaBoss = self.loadCSVByString(rootDataSrc["miasmaConfig"].text, 1, 12)
        local roleTmp = rootDataSrc["Roles"]
        roleTmp:Awake()
        local roleDataSrc = CshapToLuaValue_New(roleTmp)
        self.config_roles = {}
        for _, v in pairs(ManyKnivesDefine.roleNames) do
            self.config_roles[v] = self.loadCSVByString(roleDataSrc[v].text, 2, 16)
        end
    end)

    self.loadMgr:addLoadedCall(call)
    self.loadMgr:loadingInvoke()
end

function manyknives_res_mgr.loadCSVByString(data, headLine, maxCol)
    -- 按行划分
    local lineStr = string.bc_split(data, '\r\n')
    local allLine = {}
    local lineStrLength = #lineStr
    for i = headLine + 1, lineStrLength, 1 do
        if string.bc_IsNilOrEmpty(lineStr[i]) then
            break
        end
        local breakFlag = false
        -- 一行中，每一列的内容
        local content = string.bc_split(lineStr[i], ",")
        local col = math.min(maxCol, #content)
        local tmpRow = i - headLine
        allLine[tmpRow] = {}
        for j = 1, col, 1 do
            if string.bc_IsNilOrEmpty(content[j]) then
                breakFlag = true
                allLine[tmpRow] = nil
                break
            end
            allLine[tmpRow][j] = content[j]
        end
        if breakFlag then
            break
        end
    end
    return allLine
end

return manyknives_res_mgr
