--- Created by nyz.
--- DateTime: 2024/11/6 11:14
--- Des:

local require = require
local pairs = pairs
local ipairs = ipairs
local table = table
local log = log
local data_mgr = require "data_mgr"
local gw_ed = require("gw_ed")
local game_scheme = require "game_scheme"
local event = require "event"
local ui_window_mgr = require "ui_window_mgr"
local event_armsRace_define = require("event_armsRace_define")
local net_armsRace_module = require "net_armsRace_module"
local activity_pb = require "activity_pb"
local net_activity_module = require "net_activity_module"
local player_mgr = require "player_mgr"
local event_activity_define = require("event_activity_define")
local GWTaskMgr = require "gw_task_mgr"
local event_task_define = require "event_task_define"
local net_prop_module = require "net_prop_module"
local point_slider_define = require "point_slider_define"
local player_prefs = require "player_prefs"
local gw_event_activity_define = require "gw_event_activity_define"
local GWG = GWG
local string = string

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWArmCompetitionActivityData
module("gw_arm_competition_activity_data")
local M = {}
local self = M --简化写法，静态类中直接也用Self获取自身
---所有数据存储
local _d = data_mgr:CreateData("gw_arm_competition_activity_data")
---非服务器数据存储
local mc = _d.mde.const
local msg_item_match_funs =
{
   --当前协议中没有数组，不需要处理
}
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@see 初始化
function M.Init()
    --注册事件
    event.Register(event_armsRace_define.TMSG_ARMSRACE_INFO_RSP, self.SetArmsRaceInfo)
    event.Register(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP, self.SetRankListData)
    event.Register(event_armsRace_define.TMSG_ARMSRACE_TEAM_REPLACE_RSP, self.OnChangeRaceRSP)
    --gw_ed.mgr:Register(gw_ed.GW_HOME_MAIN_UPGRADE,self.OnBuildingUpgradeOrLevelPass)
    --event.Register(event.CHANGE_PASS_LEVEL, self.OnBuildingUpgradeOrLevelPass)
    event.Register(event_task_define.REFRESH_TASK,self.UpdateTaskData)
    event.Register(event_armsRace_define.TMSG_ARMSRACE_POIN_PROCESS_RSP,self.GetPointChessCallback)
    event.Register(event_armsRace_define.TMSG_ARMSRACE_TASKFINISH_RSP,self.UpdatePoint)
    event.Register(event_armsRace_define.TMSG_ARMSRACE_ROUND_REPLACE_RSP, self.OnUpdateRace)
    event.Register(event.ADD_ITEM, self.OnItemUpdate)
    --event.Register(event_activity_define.TMSG_WORDBOSS_DMGRECORD_NTF, M.OnWorldBossDamageNtf)
    mc.armCompetitionRankingReward = {} --初始化读排行榜奖励表数据，只需要读一次。
    local len = game_scheme:RankingRewards_nums()
    for i=0,len - 1 do
        local temp = game_scheme:RankingRewards(i)
        if temp.rewardtype == 2 then
            table.insert(mc.armCompetitionRankingReward,temp)
        end
    end
    
    mc.armCompetitionData = nil
    mc.armRaceThemeTimeData = {}
    local len2 = game_scheme:ArmsRaceThemeTime_nums()
    local day = 1 --从第一天开始
    for i = 0,len2 - 1 do
        local temp = game_scheme:ArmsRaceThemeTime(i)
        local data =
        {
            data = temp;
            dayCount = -1;
            startTime = temp.ServerTime.data[0]
        }
        if temp.ServerTime.data[0] == 0 then
            data.dayCount = day
            day = day + 1
        end
        table.insert(mc.armRaceThemeTimeData,data)
    end
    
    mc.totalTaskData = {}
    mc.rankList = {}
    mc.savePlayerRankList = {} --存储当前的玩家排行榜信息，排名发生变化时使用。id为玩家id，value为当前排名
    --mc.activityData = game_scheme:ActivityMain_0(204)

    mc.itemList = {} --用于监听道具变化的数组。为了防止存在多个道具需要显示slider而作为通用数组
    mc.itemListInit = false
    mc.armsRaceTargetData = {}
    self.OnInitActTask = function(eventName,activityId, isOpen)
        if mc.taskIsInit then
            return
        end
        local festival_activity_mgr = require "festival_activity_mgr"
        local festival_activity_cfg = require "festival_activity_cfg"
        mc.actId = festival_activity_mgr.GetActivityIdByCodeType(festival_activity_cfg.ActivityCodeType.ArmCompetition)
        if mc.actId == 0 then
            --log.Error("军备竞赛未开启！")
            return
        end
        mc.armsRaceTargetData = {}
        local boxTaskList = {}
        local len3 = game_scheme:ArmsRaceTarget_nums()
        for i = 0,len3-1 do
            local temp = game_scheme:ArmsRaceTarget(i)
            local taskData = game_scheme:TaskMain_0(temp.TaskMainID)
            mc.armsRaceTargetData[temp.BoxID] = taskData;
            table.insert(boxTaskList,temp.TaskMainID)
        end
        
        mc.taskIsInit = true
        GWTaskMgr.SetActivityTaskId(mc.actId,boxTaskList)
    end
    event.Register(gw_event_activity_define.GW_ONE_ACTIVITY_UPDATE, self.OnInitActTask)
    
    --提前把sliderUI显示出来
    --ui_window_mgr:ShowModule("ui_point_slider")
end

function M.OnInitActTask()

end

function M.OnItemUpdate(_,goodsID,goodsNum)
    local itemCount = player_mgr.GetPlayerOwnNum(goodsID)
    local oriNum = itemCount - goodsNum
    local isInit = net_prop_module.GetFirstLoginCreateRogueEntityFinishState()
    if isInit then
        if goodsID == 101 and goodsNum > 0 then --TODO 101为召唤券，后面扩展时再处理
            local cfgData = game_scheme:Item_0(goodsID)
            if cfgData then
                local msg =
                {
                    oldValue = oriNum,
                    newValue = itemCount,
                    cfg = cfgData,
                    sliderType = point_slider_define.SliderType.item,
                }
                self.OnShowItemSlider(msg)
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.START_HERO_MAIN_GUIDE)
            end
        end
    else
        --log.Error("No Init")
    --    if goodsID == 101 then
    --        mc.itemListInit = true
    --    end
    end
end

---当建筑升级或者通关主线时，需要进行一次检测。
function M.OnBuildingUpgradeOrLevelPass()

end

function M.IsShowLoginPopUp()
    local function_open_mgr = require "function_open_mgr"
    local value = function_open_mgr.CheckFunctionIsOpen(1008)
    return value and player_prefs.GetCacheData("showArmCompetition",0) == 0 and GWG.GWHomeMgr.buildingData.GetBuildingMainLevel() < game_scheme:InitBattleProp_0(8305).szParam.data[0]
end

function M.SetLoginPopUp(value)
    player_prefs.SetCacheData("showArmCompetition",value)
end

function M.UpdateTaskData()
    --log.Error("Update")
    self.GetTaskData()
end

function M.GetTaskData()
    --log.Error("Update")
    mc.totalTaskData = {}
    if mc.armCompetitionData then
        mc.armCompetitionData.goalProcess = {}
        for i,v in pairs(mc.armsRaceTargetData) do
            local taskData = GWTaskMgr.GetTaskData(v.TaskID)
            if taskData then
                table.insert(mc.totalTaskData,taskData)
                if taskData.status then
                    table.insert(mc.armCompetitionData.goalProcess,i)
                end
            end            
        end
        event.Trigger(event.UPDATE_ARM_COMPETITION_PANEL)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,mc.actId)
    end
end

function M.CheckPointChessState(_,id)
    if mc.armCompetitionData then
        for i,v in ipairs(mc.armCompetitionData.pointProcess) do
            if id == v then
                return false
            end
        end
    end
    return true
end

function M.CheckTotalChessState(_,id)
    if mc.armCompetitionData then
        for i,v in pairs(mc.armCompetitionData.goalProcess) do
            if id == v then
                return false
            end
        end
    end
    return true
end

---region 网络数据
function M.OnGetArmCompetitionInfo()
    self.GetArmsRaceInfo()
    self.GetRankListData()
end
---endRegion

---@public 利用已有数据重新刷新表现
function M.RefreshShowByData()
   
end
---发送消息给服务器请求军备竞赛信息
function M.GetArmsRaceInfo()
    net_armsRace_module.MSG_ARMSRACE_INFO_REQ()
end

---赋值当前军备竞赛的信息。
function M.SetArmsRaceInfo(_,msg)
    mc.armCompetitionData = {}
    mc.armCompetitionData.ArmsRaceRoudsID = msg.ArmsRaceRoudsID;
    mc.armCompetitionData.ArmsRaceThemeTimeID = msg.ArmsRaceThemeTimeID;
    mc.armCompetitionData.endTime = msg.endTime;
    mc.armCompetitionData.point = msg.point; --子进度
    mc.armCompetitionData.pointProcess = msg.pointProcess or {};
    mc.armCompetitionData.goalProcess = {};
    for i,v in pairs(mc.armsRaceTargetData) do
        local taskData = GWTaskMgr.GetTaskData(v.TaskID)
        if taskData then
            table.insert(mc.totalTaskData,taskData)
            if taskData.status then
                table.insert(mc.armCompetitionData.goalProcess,i)
            end
        end
    end
end

function M.UpdatePoint(_,msg)
    if not mc.armCompetitionData then --没有军备竞赛信息，直接跳出
        return
    end
    local raceCfg = game_scheme:ArmsRaceRounds_0(mc.armCompetitionData.ArmsRaceRoudsID)
    if raceCfg then
        local data =
        {
            startPos = mc.armCompetitionData.point,
            endPos = msg.point,
            pos_1 = raceCfg.Points1,
            pos_2 = raceCfg.Points2,
            pos_3 = raceCfg.Points3,
            sliderType = point_slider_define.SliderType.point,
        }
        mc.armCompetitionData.point = msg.point;
        self.OnShowPointSlider(data)
        event.Trigger(event.ON_POINT_UPDATE)
    else
        if mc.armCompetitionData.ArmsRaceRoudsID then
            log.Error("不存在ID为"..mc.armCompetitionData.ArmsRaceRoudsID.."的轮次！")
        else
            log.Error("服务器下发的军备竞赛数据中，ArmsRaceRoudsID为nil！")
        end
        
    end

end

function M.GetRedDotCount()
    if not mc.armCompetitionData then
        return 0;
    end
    local totalChessCount = 0; --满足条件的箱子，无论是否已经领取
    --先计算总的进度
    local pointIndex = game_scheme:InitBattleProp_0(8126).szParam.data[0]
    local itemCount = player_mgr.GetPlayerOwnNum(pointIndex)
    local point_1 = mc.armsRaceTargetData[1].ConditionValue1
    local point_2 = mc.armsRaceTargetData[2].ConditionValue1
    local point_3 = mc.armsRaceTargetData[3].ConditionValue1
    if itemCount >= point_1 then
        totalChessCount = totalChessCount + 1
        if itemCount >= point_2 then
            totalChessCount = totalChessCount + 1
            if itemCount >= point_3 then
                totalChessCount = totalChessCount + 1
            end
        end
    end
    
    --再计算当前轮次的进度
    local raceCfg = game_scheme:ArmsRaceRounds_0(mc.armCompetitionData.ArmsRaceRoudsID)
    if raceCfg then
        if mc.armCompetitionData.point >= raceCfg.Points1 then
            totalChessCount = totalChessCount + 1
            if mc.armCompetitionData.point >= raceCfg.Points2 then
                totalChessCount = totalChessCount + 1
                if mc.armCompetitionData.point >= raceCfg.Points3 then
                    totalChessCount = totalChessCount + 1
                end
            end
        end
    else
        --log.Error("ArmsRaceRounds不存在该轮次记录！ID:"..mc.armCompetitionData.ArmsRaceRoudsID)
    end
    
    --从总宝箱中减去已经领取的宝箱数（）
    totalChessCount = totalChessCount - #mc.armCompetitionData.pointProcess - #mc.armCompetitionData.goalProcess
    return totalChessCount
end

function M.GetPointChessCallback(_,msg)
    local rewardData = {}
    local reward_mgr = require "reward_mgr"
    local raceCfg = game_scheme:ArmsRaceRounds_0(mc.armCompetitionData.ArmsRaceRoudsID)
    rewardData = reward_mgr.GetRewardGoodsList2(raceCfg["BoxreRward"..msg.rewardId])
    --table.insert(rewardData, reward_mgr.GetRewardGoodsList2(raceCfg["BoxreRward"..msg.rewardId]))
    if rewardData and #rewardData >= 1 then
        local listData = { title = "", dataList = rewardData }
        local showData = {}
        table.insert(showData, listData)
        local ui_reward_result = require "ui_reward_result_new"
        ui_reward_result.SetInputParam(showData)
        ui_window_mgr:ShowModule("ui_reward_result_new")
    end
    mc.armCompetitionData.pointProcess = msg.pointProcess
    --self.UpdateTaskData()
    event.Trigger(event.UPDATE_ARM_COMPETITION_PANEL) --通知刷新界面
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,mc.actId)
    --打点 领取主题进度宝箱
    local reportMsg = {
        ThemeBox_Type = msg.rewardId, --1 第一档 2第二档 3第三档
    }
    event.EventReport("ArmRace_ThemeBox_Get", reportMsg)
end

---请求军备竞赛排行榜列表
function M.GetRankListData()
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_ARMSRACE)
end

---领取总宝箱
function M.GetTotalBox(_,id)
    local temp = mc.armsRaceTargetData[id]
    if temp then
        for i,v in ipairs(mc.armCompetitionData.goalProcess) do
            if id == v then
                return
            end
        end
        GWTaskMgr.ReceiveTaskReward(temp.TaskID,mc.actId)
        --打点 领取每日进度宝箱
        local reportMsg = {
            DailyBox_Type = id, --1 第一档 2第二档 3第三档
        }
        event.EventReport("ArmRace_DailyBox_Get", reportMsg)
    end
end

---领取下面的分支宝箱
function M.GetBox(_,id)
    net_armsRace_module.MSG_ARMSRACE_POIN_PROCESS_REQ(id)
end

function M.UpdateBox(pointProcess)
    mc.armCompetitionData.pointProcess = pointProcess
    event.Trigger(event.UPDATE_ARM_COMPETITION_PANEL) --通知刷新界面
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,mc.actId)
end

function M.SetRankListData(_,msg)
    if msg.actRankType == activity_pb.ACTTYPE_ARMSRACE and mc.armCompetitionData then
        local raceCfg = game_scheme:ArmsRaceRounds_0(mc.armCompetitionData.ArmsRaceRoudsID)
        
        local maxScore = -1
        if raceCfg then
            maxScore = raceCfg.Points3
        end

        mc.rankList = {}
        mc.rankList.data = {}
        
        for i,v in ipairs(msg.rankInfo) do
            local temp = {}
            temp.rank = v.rank;
            temp.dbid = v.dbid;
            temp.score = v.score;
            if v.score > maxScore then
                maxScore = v.score
            end
            temp.roleLv = v.roleLv;
            --适配faceID 2025.4.2 将faceID转换为faceStr
            local faceStr = v.faceID
            if v.faceStr and not string.IsNullOrEmpty(v.faceStr) then
                faceStr = v.faceStr
            end
            temp.faceID = faceStr;
            temp.frameID = v.frameID;
            temp.sex = v.sex;
            temp.playerName = v.name;
            temp.leagueName = v.leagueName;
            temp.leagueShortName = v.leagueShortName
            temp.worldId = v.worldId;
            if mc.savePlayerRankList[v.dbid] and mc.savePlayerRankList[v.dbid] ~= v.rank then
                temp.showChange = true;
                temp.changeValue = mc.savePlayerRankList[v.dbid] - v.rank
            else
                temp.showChange = false;
                temp.changeValue = 0
            end
            table.insert(mc.rankList.data,temp)
            mc.savePlayerRankList[v.dbid] = v.rank
        end
        table.sort(mc.rankList.data,function(a, b) 
            return a.rank < b.rank
        end)
        mc.rankList.maxScore = maxScore
    end
    event.Trigger(event.LOAD_RANK_LIST_FINISH) --通知军备竞赛排行榜已经刷新
end

---主题轮换
function M.OnChangeRaceRSP(_,msg)
    mc.armCompetitionData = {}
    mc.armCompetitionData.ArmsRaceRoudsID = msg.ArmsRaceRoudsID;
    mc.armCompetitionData.ArmsRaceThemeTimeID = msg.ArmsRaceThemeTimeID;
    mc.armCompetitionData.endTime = msg.endTime;
    mc.armCompetitionData.point = msg.point;
    mc.armCompetitionData.goalProcess = {};
    mc.armCompetitionData.pointProcess = msg.pointProcess or {};
    for i,v in pairs(mc.armsRaceTargetData) do
        local taskData = GWTaskMgr.GetTaskData(v.TaskID)
        if taskData then
            table.insert(mc.totalTaskData,taskData)
            if taskData.status then
                table.insert(mc.armCompetitionData.goalProcess,i)
            end
        end
    end
    event.Trigger(event.ON_RACE_CHANGE) --通知军备竞赛内容已轮换
end

function M.OnUpdateRace(_,msg)
    mc.armCompetitionData.ArmsRaceRoudsID = msg.ArmsRaceRoudsID;
    event.Trigger(event.ON_RACE_CHANGE) --通知军备竞赛内容已轮换
end

function M.GetArmCompetitionData()
    return mc.armCompetitionData
end

function M.OnGetRankList()
    return mc.rankList
end

function M.GetArmsRaceTarget()
    return mc.armsRaceTargetData
end

function M.GetRaceThemeTimeData()
    return mc.armRaceThemeTimeData
end

function M.GetRankingCfgData(typeId)
    return mc.armCompetitionRankingReward
end

function M.OnShowPointSlider(msg)
    local temp = ui_window_mgr:GetWindowObj("ui_point_slider")
    if not temp then
        ui_window_mgr:ShowModule("ui_point_slider",nil,nil,msg)
    else
        event.Trigger(event.SHOW_POINT_SLIDER,msg)
    end
end

function M.OnShowItemSlider(msg)
    local temp = ui_window_mgr:GetWindowObj("ui_point_slider")
    if not temp then
        ui_window_mgr:ShowModule("ui_point_slider",nil,nil,msg)
    else
        event.Trigger(event.SHOW_ITEM_SLIDER,msg)
    end
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--- 数据清理
function M.Dispose()
    event.Unregister(event_armsRace_define.TMSG_ARMSRACE_INFO_RSP, self.SetArmsRaceInfo)
    event.Unregister(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP, self.SetRankListData)
    event.Unregister(event_armsRace_define.TMSG_ARMSRACE_TEAM_REPLACE_RSP, self.OnChangeRaceRSP)
    --gw_ed.mgr:Unregister(gw_ed.GW_HOME_MAIN_UPGRADE,self.OnBuildingUpgradeOrLevelPass)
   -- event.Unregister(event.CHANGE_PASS_LEVEL, self.OnBuildingUpgradeOrLevelPass)
    event.Unregister(event_task_define.REFRESH_TASK,self.UpdateTaskData)
    event.Unregister(event_armsRace_define.TMSG_ARMSRACE_POIN_PROCESS_RSP,self.GetPointChessCallback)
    event.Unregister(event_armsRace_define.TMSG_ARMSRACE_TASKFINISH_RSP,self.UpdatePoint)
    event.Unregister(event_armsRace_define.TMSG_ARMSRACE_ROUND_REPLACE_RSP, self.OnUpdateRace)
    event.Unregister(event.ADD_ITEM, self.OnItemUpdate)
    _d.mde:clear()
    mc = _d.mde.const
    ui_window_mgr:UnloadModule("ui_point_slider")
end
return M