---@class kingshot_player
local player = bc_Class("kingshot_player")
---@type kingshot_scene_mgr
player.sceneMgr = nil
player.DataSrc = nil
player.Transform = nil
player.HeroRoot = nil
player.TargetPoints = nil
player.SlotPoints = nil
player.SlotArray = nil

---@type kingshot_playerunit[] 所有玩家单位
player.unitList = nil
player.unitCount = nil
---@type kingshot_playerunit[]
player.unitWithCharacter = nil
---@type kingshot_playerunit[] 所有英雄单位
player.heroList = nil

player.curVelocity = nil

---@type boolean 是否在移动
player.MotionFlag = nil

---@type fusion_delegateUnRegister
player.dragUnreg = nil
---@type fusion_bindableUnRegister
player.battleFlagUnreg = nil

---@type fusion_bindable 角色半径
player.RadiusBind = nil

local playerUnitClass = require "kingshot_playerunit"
local emptyArray = {}

function player:__init(...)
    self.sceneMgr, self.Transform = ...
    self.DataSrc = {}
    self.Transform:GetComponent(KingShot_Define.TypeOf.NeeReferCollection):Bind(self.DataSrc)
    self.TargetPoints = {}
    self.SlotPoints = {}
    self.SlotArray = {}
    self.HeroRoot = self.DataSrc._heroRoot
    for i = 1, 5 do
        self.SlotPoints[i] = self.HeroRoot:Find("Point_" .. i)
        self.SlotArray[i] = self.SlotPoints[i]:Find("Slot")
        self.TargetPoints[i] = self.HeroRoot:Find("TargetPoint_" .. i)
    end

    self.unitWithCharacter = {}
    self.unitList = {}
    self.unitCount = 0
    self.heroList = {}

    self.battleFlagUnreg = self.sceneMgr.BattleFlagBind:Register(function(value)
        self:BattleFlagListener(value)
    end)
    self.RadiusBind = require("fusion_bindable").New(0)
    self.RadiusBind:Register(function(value)
        --- 调整碰撞体大小
        self.DataSrc.Collider.radius = value
        self.DataSrc.Collider.center = { x = -value, y = value, z = 0 }
    end)
end

function player:Reset()
    if self.dragUnreg == nil then
        self.dragUnreg = self.sceneMgr.uiMgr.DragEvent:Add(function(vec2)
            self:JoystickOnDrag(vec2)
        end)
    end
    self.DataSrc.Rigidbody.centerOfMass = KingShot_Define.CacheVector3.Zero
    self.curVelocity = KingShot_Define.CS.Vector3(0, 0, 0)
    KingShot_Define.SetTransformPositionXYZ(self.Transform, self.sceneMgr.PlayerData.Pos.x,
        self.sceneMgr.PlayerData.Pos.y, self.sceneMgr.PlayerData.Pos.z)
    self:MotionColliderEnabled(false)
    self.RadiusBind:SetValue(1)
    self._actor_instance_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    self.HeroRoot.gameObject:SetActive(true)
    self:ShowSelectHeroUI(true)
end

function player:MotionColliderEnabled(flag)
    self.DataSrc.Collider.enabled = flag
end

function player:BattleFlagListener(value)
    if value then
        self:MotionColliderEnabled(true)
        for i, v in ipairs(self.SlotArray) do
            v.gameObject:SetActive(false)
        end
    end
end

function player:JoystickOnDrag(vec2)
    self.curVelocity = KingShot_Define.CS.Vector3(vec2.x, 0, vec2.y)
end

function player:ShowSelectHeroUI(show)
    self.DataSrc._selectHeroUI:SetActive(show)
end

function player:GetCenterXYZ()
    return KingShot_Define.GetTransformPositionXYZ(self.DataSrc.TeamCenter)
end

function player:GetSlotFollowXYZ()
    return KingShot_Define.GetTransformPositionXYZ(self.DataSrc.rfCameraFollow)
end

function player:GetCenterVec3()
    return self.DataSrc.TeamCenter.position
end

function player:Update(deltaTime)
    --是否在移动
    self.MotionFlag = self.curVelocity.x ~= 0 or self.curVelocity.z ~= 0
    self.DataSrc.Rigidbody.velocity = self.curVelocity * KingShot_Define.Params.PlayerMoveSpeed
    local tarRotate = nil
    if self.MotionFlag then
        tarRotate = KingShot_Define.CS.Quaternion.LookRotation(self.curVelocity)
    end
    for _, v in ipairs(self.unitList) do
        v:SetMoveFlag(self.MotionFlag)
        v:Update(tarRotate, deltaTime)
    end
end

function player:__delete()
    if self.dragUnreg ~= nil then
        self.dragUnreg:UnRegister()
        self.dragUnreg = nil
    end
    if self.battleFlagUnreg ~= nil then
        self.battleFlagUnreg:UnRegister()
        self.battleFlagUnreg = nil
    end
end

--- 角色开始射击
function player:CharacterFire(character)
    local unit = self.unitWithCharacter[character]
    unit:Fire()
end

function player:CreateAttackRange(unitID, attackRange)
end

function player:GetTargetObjs(character)
    return emptyArray
end

function player:GetTargetObjsByWeaponID(weaponID)
    return emptyArray
end

function player:RemoveRangeEnemy()
end

function player:GetUnitIDByHeroID(heroID, level)
    level = level or 1
    self._unitIDMapHeroID = self._unitIDMapHeroID or {}
    local levelKey = heroID .. "@" .. level
    local unitID = self._unitIDMapHeroID[levelKey]
    if not unitID then
        for _, unit in pairs(self.sceneMgr.resMgr.UnitConfigs) do
            if unit.UnitType == 4 or unit.UnitType == 6 then -- 4—英雄
                if unit.ModelID and unit.ModelID > 0 then
                    local tmpKey = unit.ModelID .. "@" .. unit.UnitLevel
                    if not self._unitIDMapHeroID[tmpKey] then
                        self._unitIDMapHeroID[tmpKey] = unit.ID
                    end
                end
            end
        end
        unitID = self._unitIDMapHeroID[levelKey]
    end
    return unitID
end

--unitId 单位id(士兵传nil)，hero英雄数据
function player:CreatePlayerCharacter(unitId, hero, slotIndex, playEffect) --CreateCharacter
    local curHeroId = nil
    local hp = nil
    local atk = nil
    if hero then
        curHeroId = hero and hero.heroID
        hp = hero and hero.battleProp.hp
        atk = hero and hero.battleProp.attack
    end

    local localPos = KingShot_Define.CacheVector3.Zero
    local parent = nil
    slotIndex = slotIndex == nil and self:GetEmptyHeroSlot() or slotIndex
    parent = self.SlotPoints[slotIndex]

    local character = self._actor_instance_mgr:CreateCharacter(unitId, localPos, parent, true, curHeroId, hp, atk, self,
        playEffect)
    ---@type kingshot_playerunit
    local heroUnit = playerUnitClass.New(unitId, character, hero)
    self.unitCount = self.unitCount + 1
    self.unitList[self.unitCount] = heroUnit
    self.unitWithCharacter[character] = heroUnit
    if curHeroId ~= nil then
        self.heroList[#self.heroList + 1] = heroUnit
    end

    heroUnit:Reset()
    return character
end

function player:GetEmptyHeroSlot()
    for i = 1, #self.SlotPoints do
        local slot = self.SlotPoints[i]
        if slot.childCount < 2 then
            return i
        end
    end
    return nil
end

function player:GetCHeroList()
    return self.heroList
end

---玩家单位死亡,英雄和小兵都调用
function player:OnHeroDead(character)
    if not self.sceneMgr.BattleFlagBind.value then
        if character._unit_type == cysoldierssortie_unit_type.Hero then
            table.remove_value(self.heroList, self.unitWithCharacter[character])
        end
    else
        local unit = self.unitWithCharacter[character]
        unit:Die()
        self.unitWithCharacter[character] = nil
        table.remove_value(self.unitList, unit)
        self.unitCount = self.unitCount - 1
        if self.unitCount < 1 then
            self.sceneMgr:GameOver(false)
        end
    end
end

function player:RecycleSoldierPos(localPos, parent)
    -- self._soldier_pos_pool = self._soldier_pos_pool or {}
    -- self._soldier_pos_pool[#self._soldier_pos_pool + 1] = { LocalPos = localPos, Parent = parent }
end

function player:GetAllSoldierDps()
    return 0
    -- if not self._soldier_lst then
    --     return 0
    -- end

    -- local soldierAttack = 0
    -- local soldierAttackSpeed = 0
    -- local soldierCriticalHit = 0
    -- for i = 1, #self._soldier_lst do
    --     local character = self._soldier_lst[i]
    --     if character._unit_type == cysoldierssortie_unit_type.Soldier or
    --         character._unit_type == cysoldierssortie_unit_type.Soldier2 then
    --         soldierAttack = character._attack
    --         if character._weapons and #character._weapons > 0 then
    --             local weapon = character._weapons[1]
    --             soldierAttackSpeed = (1 / weapon._attackSpeed)
    --             soldierCriticalHit = weapon._criticalHit / 10000
    --         end
    --         break
    --     end
    -- end
    -- local allSoldierDpsNum = soldierAttack * soldierAttackSpeed * (1 + soldierCriticalHit) * (#self._soldier_lst)
    -- return allSoldierDpsNum
end

return player
