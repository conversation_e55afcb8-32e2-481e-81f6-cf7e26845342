TinyRush_Extern = {}
local checkNums = function(nums)
    local n = nums
    if n >= 0 then
        return n
    else
        n = 0 - n
        n = 0xffffffff - n + 1
    end
    return n
end
local resultCover = function(n)
    local num = n
    if num >= 0x80000000 then
        num = num - 0xffffffff - 1
    end
    return num
end
TinyRush_Extern.Xor = function(num1, num2)
    local tmp1 = checkNums(num1)
    local tmp2 = checkNums(num2)
    local ret = 0
    local count = 0
    repeat
        local s1 = tmp1 % 2
        local s2 = tmp2 % 2
        if s1 ~= s2 then
            ret = ret + 2 ^ count
        end
        tmp1 = math.modf(tmp1 / 2)
        tmp2 = math.modf(tmp2 / 2)
        count = count + 1
    until (tmp1 == 0 and tmp2 == 0)
    return resultCover(ret)
end
TinyRush_Extern.And = function(num1, num2)
    local tmp1 = checkNums(num1)
    local tmp2 = checkNums(num2)
    local ret = 0
    local count = 0
    repeat
        local s1 = tmp1 % 2
        local s2 = tmp2 % 2
        if s1 == s2 and s1 == 1 then
            ret = ret + 2 ^ count
        end
        tmp1 = math.modf(tmp1 / 2)
        tmp2 = math.modf(tmp2 / 2)
        count = count + 1
    until (tmp1 == 0 and tmp2 == 0)
    return resultCover(ret)
end
TinyRush_Extern.Or = function(num1, num2)
    local tmp1 = checkNums(num1)
    local tmp2 = checkNums(num2)
    local ret = 0
    local count = 0
    repeat
        local s1 = tmp1 % 2
        local s2 = tmp2 % 2
        if s1 == s2 and s1 == 0 then

        else
            ret = ret + 2 ^ count
        end
        tmp1 = math.modf(tmp1 / 2)
        tmp2 = math.modf(tmp2 / 2)
        count = count + 1
    until (tmp1 == 0 and tmp2 == 0)
    return resultCover(ret)
end
TinyRush_Extern.GetSign = function(num)
    if num >= 0 then
        return 1  -- 正数
    else
        return -1 -- 负数
    end
end
TinyRush_Extern.ToInteger = function(num)
    return num < 0 and math.ceil(num) or math.floor(num)
end
TinyRush_Extern.GetHashCode = function(number)
    return TinyRush_Extern.Xor(TinyRush_Extern.ToInteger(number), TinyRush_Extern.ToInteger(number / (2 ^ 32)))
end
---@type tinyrush_vector3
TinyRush_Vector3 = require("tinyrush_vector3")
---@type tinyrush_vector2
TinyRush_Vector2 = require("tinyrush_vector2")
---@type tinyrush_quaternion
TinyRush_Quaternion = require("tinyrush_quaternion")

local UnityAxisHorizontal = CS.UnityEngine.RectTransform.Axis.Horizontal
local UnityAxisVertical = CS.UnityEngine.RectTransform.Axis.Vertical
--- 设置布局，限定宽/高。(支持UGUI.Text，TMP_Text)
--- @param text table UI.Text
--- @param axis number @指定轴：0水平，1垂直
--- @param maxValue number 指定轴最大值
--- @param bgRect table UI.RectTransform 文本框背景
--- @param offSize table Vector2 背景尺寸Off
TinyRush_Extern.Func_SetUGUITextLayoutWithFixed = function(text, axis, maxValue, bgRect, offSize)
    local curValue = nil
    local unityAxis = nil
    if axis == 0 then
        curValue = text.preferredWidth
        unityAxis = UnityAxisHorizontal
    else
        curValue = text.preferredHeight
        unityAxis = UnityAxisVertical
    end
    if curValue > maxValue then
        curValue = maxValue
    end
    local nextAxis = nil
    text.rectTransform:SetSizeWithCurrentAnchors(axis, curValue)
    if axis == 0 then
        nextAxis = UnityAxisVertical
        curValue = text.preferredHeight
    else
        nextAxis = UnityAxisHorizontal
        curValue = text.preferredWidth
    end
    text.rectTransform:SetSizeWithCurrentAnchors(nextAxis, curValue)
    if bgRect ~= nil and offSize ~= nil then
        bgRect.sizeDelta = text.rectTransform.sizeDelta + offSize
    end
end
--- 通过权重，拿受控随机下标
--- @return number 返回下标
TinyRush_Extern.Func_GetRandomByWeight = function(weights)
    local weightLength = #weights
    if weightLength < 2 then
        return 1
    end
    -- 计算权重总和
    local totalWeights = 0
    for i = 1, weightLength, 1 do
        totalWeights = totalWeights + weights[i]
    end
    local remaining_distance = math.random() * totalWeights
    for i = 1, weightLength, 1 do
        remaining_distance = remaining_distance - weights[i]
        if remaining_distance < 0 then
            return i
        end
    end
    return 1
end
local Func_GetCatmullRomPosition = function(t, p0, p1, p2, p3)
    local a = 2 * p1
    local b = p2 - p0
    local c = 2 * p0 - 5 * p1 + 4 * p2 - p3
    local d = -p0 + 3 * p1 - 3 * p2 + p3
    local pos = 0.5 * (a + (b * t) + (c * t * t) + (d * t * t * t))
    return pos
end
local Func_GetCatmullRomSplinePos = function(vertexCount, pos, p0, p1, p2, p3)
    local resolution = 1 / vertexCount
    local startIndex = #pos
    for i = 1, vertexCount, 1 do
        pos[startIndex + i] = Func_GetCatmullRomPosition(i * resolution, p0, p1, p2, p3)
    end
end
--- --- 创建样条线,算法：Catmull-Rom
---@param vertexCount number 每条曲线线段数
---@param loop boolean 是否循环
---@param target TinyRush_Vector3[] 传入路径位置数组
TinyRush_Extern.Func_DrawCatmullRomSpline = function(vertexCount, loop, target)
    local targetCount = #target
    if target == nil or targetCount < 3 then
        return nil
    end
    local pos = { target[1] }
    local current = 1
    while current < targetCount do
        if current == 1 then
            Func_GetCatmullRomSplinePos(vertexCount, pos, target[1], target[1], target[2], target[3])
        elseif current == targetCount - 1 then
            Func_GetCatmullRomSplinePos(vertexCount, pos, target[current - 1], target[current], target[current + 1],
                target[current + 1])
        else
            Func_GetCatmullRomSplinePos(vertexCount, pos, target[current - 1], target[current], target[current + 1],
                target[current + 2])
        end
        current = current + 1
    end
    if loop then
        current = targetCount
        Func_GetCatmullRomSplinePos(vertexCount, pos, target[current - 1], target[current], target[1], target[2])
    end
    return pos
end
--- 获取安全区域数据
---@return TR_SafeArea
TinyRush_Extern.Func_GetSafeArea = function()
    ---@class TR_SafeArea
    local safeArea = {
        TopRatio = 0, -- 屏幕占比(0-1)
        BotRatio = 0, --屏幕占比(0-1)
        ScreenWidth = 0,
        ScreenHeight = 0
    }
    local wxFlag = false
    local platform = CS.UnityEngine.Application.platform
    if (platform == CS.UnityEngine.RuntimePlatform.WebGLPlayer) then
        local util = require "util"
        -- 判断是不是微信h5平台
        if util ~= nil and util.GetWechatSystemInfo ~= nil then
            local systemInfo = util.GetWechatSystemInfo()
            if systemInfo ~= nil then
                local statusBarTop = 0
                if CS.WeChatWASM.WXSDKManagerHandler ~= nil then
                    local tmpWXSDKManager = CS.WeChatWASM.WXSDKManagerHandler.Instance
                    if tmpWXSDKManager ~= nil then
                        local statusBarInfo = tmpWXSDKManager:GetMenuButtonBoundingClientRect()
                        if statusBarInfo ~= nil then
                            statusBarTop = statusBarInfo.bottom
                        end
                    end
                end
                safeArea.TopRatio = math.max(systemInfo.safeArea.top, statusBarTop) / systemInfo.screenHeight
                safeArea.ScreenHeight = systemInfo.screenHeight
                safeArea.ScreenWidth = systemInfo.screenWidth
                wxFlag = true
            end
        end
    end
    if not wxFlag then
        local unityScreen = CS.UnityEngine.Screen
        if unityScreen.height > unityScreen.safeArea.yMax then
            safeArea.TopRatio = (unityScreen.height - unityScreen.safeArea.yMax) / unityScreen.height
        end
        if unityScreen.safeArea.yMin > 0 then
            safeArea.BotRatio = unityScreen.safeArea.yMin / unityScreen.height
        end
        safeArea.ScreenHeight = unityScreen.height
        safeArea.ScreenWidth = unityScreen.width
    end
    return safeArea
end
