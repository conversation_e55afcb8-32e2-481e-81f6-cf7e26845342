---@class kingshot_buildingunit : fusion_gopoolitem
local unit = bc_Class("kingshot_buildingunit", require("fusion_gopoolitem"))

-- 导入必要的模块
local log = require "log"
local LookAtTargetSystemInstance = nil

---@type kingshot_scene_mgr
unit.sceneMgr = nil
---@type kingshot_building
unit.Ctrl = nil
unit.DataSrc = nil
---@type kingshot_BuildingConfig
unit.config = nil
---@type kingshot_buildingData
unit.buildingData = nil

---@type kingshot_buildingitem
unit.item = nil

---@type cysoldierssortie_comp_character
unit.character = nil
---@type number 射击计时器
unit.fireTimer = nil
---@type number 射击间隔
unit.fireInterval = nil
---@type number 攻击范围
unit.attackRange = nil
---@type number 攻击力
unit.attackPower = nil
---@type boolean 是否可以攻击
unit.canAttack = nil

function unit:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)

    -- 初始化LookAtTargetSystemInstance
    local res = xpcall(function()
        LookAtTargetSystemInstance = KingShot_Define.LookAtTargetSystem.Instance
    end, debug.traceback)
end

function unit:EnableCollider(flag)
    self.DataSrc.Collider.enabled = flag
end

function unit:Init(sceneMgr, ctrl, data, config)
    self.sceneMgr = sceneMgr
    self.Ctrl = ctrl
    self.buildingData = data
    self.config = config
    KingShot_Define.SetTransformPositionXYZ(self.transform, self.buildingData.Pos.x, self.buildingData.Pos.y,
            self.buildingData.Pos.z)
    self.gameObject:SetActive(true)

    self.item = self.Ctrl:PopOneBuildingItem()
    self.item.transform:SetParent(self.transform)
    KingShot_Define.SetTransformLocalPositionAndLocalRotation(self.item.transform, 0, 0, 0, 0, 0, 0)
    self.item:Init(self.config)

    self:EnableCollider(true)

    self.DataSrc.ColListener:RegisterTriggerEnter(function(other)
        self:OnCollisionEnter(other)
    end)

    self.isDie = false

    -- 初始化攻击相关属性
    self.fireTimer = 0
    self.fireInterval = 1.0 -- 1秒发射一次
    self.attackRange = KingShot_Define.Params.PlayerAtkRange -- 使用玩家的攻击范围
    self.attackPower = 10 -- 攻击力
    self.canAttack = true -- 是否可以攻击

    -- 创建建筑的角色实体，用于索敌和攻击
    local actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    if actorMgr then
        -- 创建一个Building类型的角色 (unitId=7)
        self.character = actorMgr:CreateCharacter(
            10, -- unitId (Building type)
            { x = 0, y = 0, z = 0 }, -- localPos
            self.transform, -- parent
            true, -- forceView
            nil, -- heroID
            100, -- hp
            self.attackPower, -- attack
            self, -- player
            false -- playEffect
        )
    end
end

function unit:OnCollisionEnter(collider)
    if self.isDie then
        return
    end
    if not self.config.IsMain and self.item:CanUpgrade() then
        self.item:Upgrade()
    end
end

-- 获取建筑碰撞盒顶部位置
function unit:GetColliderTopPosition()
    local collider = self.DataSrc.Collider
    local position = self.transform.position
    local bounds = collider.bounds
    local topPosition = KingShot_Define.CS.Vector3(
        position.x,
        bounds.max.y,
        position.z
    )
    return topPosition
end

function unit:CharacterFire(character)
    
end
-- 发射子弹
function unit:Fire(targetGo)
    if not targetGo or not self.canAttack then
        return
    end

    -- 获取子弹发射位置（碰撞盒顶部）
    local firePosition = self:GetColliderTopPosition()

    -- 计算子弹方向（朝向目标）
    local targetPosition = targetGo.transform.position
    local direction = targetPosition - firePosition
    direction.y = 0 -- 保持水平方向
    direction = direction.normalized

    -- 创建子弹
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if poolMgr then
        local skillGo = poolMgr:AcquireObj("cysoldierssortie_comp_base_bullet_entity", poolMgr.transform)
        if skillGo then
            -- 设置子弹位置和方向
            skillGo.transform.position = firePosition
            skillGo.transform.forward = direction

            -- 配置子弹参数
            local skillEntity = cysoldierssortie_GetLuaComp(skillGo.gameObject)
            if skillEntity then
                local skillParam = {
                    character = self.character,
                    ballisiticVelocity = 20, -- 子弹速度
                    ballisiticRange = self.attackRange, -- 子弹射程
                    attack = self.attackPower, -- 子弹伤害
                    skillPath = "cysoldierssortie_comp_base_bullet_entity", -- 子弹预制体路径
                    bullet_scale = 1, -- 子弹大小
                    recycle_time = 3, -- 子弹回收时间
                }
                skillEntity:CreateData(skillParam)
            end
        end
    end

    -- 重置发射计时器
    self.fireTimer = 0
end

function unit:Update(deltaTime)
    if self.isDie or not self.canAttack or not self.character then
        return
    end

    self.character:UpdateAI()

    -- 更新发射计时器
    self.fireTimer = self.fireTimer + deltaTime

    -- 检查是否可以发射
    if self.fireTimer >= self.fireInterval then
        -- 尝试获取目标
        local targetGo = nil
        pcall(function()
            targetGo = self.character:GetTargetGo()
        end)

        -- 如果有目标，则发射子弹
        --if targetGo then
        --    self:Fire(targetGo)
        --end
    end
end

---单位死亡
---cysoldierssortie_comp_character 调用
function unit:OnHeroDead(character)
   
end
---cysoldierssortie_comp_character 调用
function unit:RecycleSoldierPos(localPos, parent)

end
---cysoldierssortie_comp_character 调用
function unit:GetUnitIDByHeroID(heroID, level)
    
end
---cysoldierssortie_comp_character 调用
function unit:CreateAttackRange(unitID, attackRange)
    
end


return unit
