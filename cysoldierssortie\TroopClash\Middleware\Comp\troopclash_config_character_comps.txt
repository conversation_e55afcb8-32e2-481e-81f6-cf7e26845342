local troopclash_config_fsm = require("troopclash_config_fsm")
local weaponSpawnPoint = { x = 0, y = 1, z = 0 }
local CharacterCompsConfig =
{
    [cysoldierssortie_comp_name.HP] =
    {
        _addComp = function(character)
            local spawnHpPoint = character._character_entity._hpPoint.transform.position
            character:AddComponent(cysoldierssortie_comp_name.HP,
                {
                    character = character,
                    spawnHpPoint = spawnHpPoint,
                }
            )
        end,
    },
    [cysoldierssortie_comp_name.AI] = {
        _addComp = function(character)
            character:AddComponent(cysoldierssortie_comp_name.AI,
                { character = character, config = troopclash_config_fsm.UnitType_Config[character._unit_type] })
        end
    },
    [cysoldierssortie_comp_name.Attack] = {
        _addComp = function(character)
            character._weaponRoot = character._character_entity._releaseSkillPoint.transform
            character._weaponSpawnPoint = weaponSpawnPoint
            character:AddComponent(cysoldierssortie_comp_name.Attack,
                {
                    character = character
                })
        end
    },
    [cysoldierssortie_comp_name.Level] = {
        _addComp = function(character)
            if character._level then
                character:AddComponent(cysoldierssortie_comp_name.Level, {
                    level = character._level,
                    character = character
                })
            end
        end
    },
    [cysoldierssortie_comp_name.Move] = {
        _addComp = function(character)
            character:AddComponent(cysoldierssortie_comp_name.Move,
                { moveSpeed = character._moveSpeed, character = character })
        end
    },
    [cysoldierssortie_comp_name.FeedBack] = {
        _addComp = function(character)
            character:AddComponent(cysoldierssortie_comp_name.FeedBack, { character = character })
        end
    },
}

local UnitTypeCompsConfig = {
    [cysoldierssortie_unit_type.Soldier] =
    {
        cysoldierssortie_comp_name.AI,
        cysoldierssortie_comp_name.HP,
        cysoldierssortie_comp_name.Attack,
        cysoldierssortie_comp_name.Level,
        cysoldierssortie_comp_name.FeedBack
    },
    [cysoldierssortie_unit_type.Hero] =
    {
        cysoldierssortie_comp_name.AI,
        cysoldierssortie_comp_name.HP,
        cysoldierssortie_comp_name.Attack,
        cysoldierssortie_comp_name.Level,
        cysoldierssortie_comp_name.FeedBack
    },
    [cysoldierssortie_unit_type.NormalEnemy] =
    {
        cysoldierssortie_comp_name.AI,
        cysoldierssortie_comp_name.HP,
        cysoldierssortie_comp_name.Move,
        cysoldierssortie_comp_name.Attack,
        cysoldierssortie_comp_name.FeedBack
    },
    [cysoldierssortie_unit_type.BossEnemy] =
    {
        cysoldierssortie_comp_name.AI,
        cysoldierssortie_comp_name.HP,
        cysoldierssortie_comp_name.Move,
        cysoldierssortie_comp_name.Attack,
        cysoldierssortie_comp_name.FeedBack
    },
    [cysoldierssortie_unit_type.Drone] =
    {
        -- cysoldierssortie_comp_name.AI,
        -- cysoldierssortie_comp_name.Attack,
    }
}

return
{
    CharacterCompsConfig = CharacterCompsConfig,
    UnitTypeCompsConfig = UnitTypeCompsConfig,
}
