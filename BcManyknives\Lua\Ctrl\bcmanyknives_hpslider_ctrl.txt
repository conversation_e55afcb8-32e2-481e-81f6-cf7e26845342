local poolItemBase = TinyRush_CreateClass("bcmanyknives_hpslider_ctrl"):baseClass(require("tinyrush_gopoolitem"))

local itemNames = {
    fill_enemy = "fill_enemy",
    fill_Player = "fill_Player",
    Fill_Img = "Fill_Img",
    bg = "bg"
}

poolItemBase.dataSrc = nil
poolItemBase.fillImg = nil
poolItemBase.spritePlayer = nil
poolItemBase.spriteEnemy = nil
poolItemBase.hpBindUnReg = nil
poolItemBase.hpMaxBindUnReg = nil
poolItemBase.mainCamera = nil
poolItemBase.uiMgr = nil
poolItemBase.text = nil
poolItemBase.roleBase = nil

local fillMin = 0
local fillMax = 100
local horAxis = CS.UnityEngine.RectTransform.Axis.Horizontal

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    local tmp = self.gameObject:GetComponent(typeof(CS.GameLuaBehaviour_New))
    tmp:Awake()
    self.dataSrc = CshapToLuaValue_New(tmp)
    self.fillImg = self.dataSrc[itemNames.Fill_Img]
    self.spritePlayer = self.dataSrc[itemNames.fill_Player]
    self.spriteEnemy = self.dataSrc[itemNames.fill_enemy]
    self.bgRect = self.dataSrc[itemNames.bg]
    self.text = self.dataSrc["text"]
end

function poolItemBase:Init(roleBase, hpBind, hpMaxBind, camera, uiMgr)
    self.uiMgr = uiMgr
    self.mainCamera = camera
    self.roleBase = roleBase
    if self.roleBase.isPlayer then
        self.fillImg.sprite = self.spritePlayer
    else
        self.fillImg.sprite = self.spriteEnemy
    end
    self.hpBindUnReg = hpBind:register(function(value)
        self:HpListener()
    end)
    self.hpMaxBindUnReg = hpMaxBind:register(function(value)
        self:HpListener()
    end, true)
end

function poolItemBase:HpListener()
    local process = self.roleBase.hpValueBind.value / self.roleBase.hpMaxBind.value
    if process <= 0 then
        self.fillImg.rectTransform:SetSizeWithCurrentAnchors(horAxis, 0)
    else
        self.fillImg.rectTransform:SetSizeWithCurrentAnchors(horAxis, math.lerp(fillMin, fillMax, process))
    end
    self.text.text = tostring(math.ceil(self.roleBase.hpValueBind.value))
end

function poolItemBase:RefreshPos(pos)
    local anchorPos = self.mainCamera:WorldToViewportPoint(pos)
    anchorPos.x = self.uiMgr.realCanvaSize.x * (anchorPos.x - 0.5)
    anchorPos.y = self.uiMgr.realCanvaSize.y * (anchorPos.y - 0.5)
    self.bgRect.anchoredPosition = bc_CS_Vector2(anchorPos.x, anchorPos.y)
end

function poolItemBase:dispose()
    self:recycle()
    self.__base:dispose()
end

function poolItemBase:recycle()
    if self.hpBindUnReg ~= nil then
        self.hpBindUnReg:unRegister()
        self.hpBindUnReg = nil
    end
    if self.hpMaxBindUnReg ~= nil then
        self.hpMaxBindUnReg:unRegister()
        self.hpMaxBindUnReg = nil
    end
end

return poolItemBase
