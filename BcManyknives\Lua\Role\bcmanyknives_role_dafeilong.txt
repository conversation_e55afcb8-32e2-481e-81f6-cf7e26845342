local manyknives_role_dafeilong = TinyRush_CreateClass("bcmanyknives_role_dafeilong"):baseClass(require(
    "bcmanyknives_rolebase_item"))

-- 特效：眩晕
manyknives_role_dafeilong.fx_xuanyun = nil
-- 冲刺
manyknives_role_dafeilong.rushCenter = nil
-- 冲刺标记
manyknives_role_dafeilong.rushFlag = nil
manyknives_role_dafeilong.rushGuide = nil

local rushConfig = {
    -- 冲刺冷却
    CD_rush = 3,
    -- 冲刺完眩晕几秒
    dizziness = 3,
    -- 冲刺速度
    rush_MoveSpeed = 13,
    -- 冲刺多远
    rush_Limit = 15,
    -- 冲刺提前1.5秒定位方向
    rush_DirTimerOff = 0.8,
    -- 冲刺指引得高度
    rushGuide_height = 2.5
}

function manyknives_role_dafeilong:ctor(...)
    self.__base:ctor(...)
    self.fx_xuanyun = self.dataSrc["fx_xuanyun"]
    self.rushCenter = self.dataSrc["rushCenter"]
    self.fx_rushReady = self.dataSrc["fx_boss_chongci_xuli"]
end

function manyknives_role_dafeilong:Init(...)
    self.__base:Init(...)
    self.fx_xuanyun.gameObject:SetActive(false)
    self.rushCenter.gameObject:SetActive(false)
    -- 1：闲逛，2：准备冲，3：冲刺状态 ，4:眩晕状态
    self.moveFlag = 1
    self.rushTimer = 0
    self.rushTimerCD = rushConfig.CD_rush - rushConfig.rush_DirTimerOff
    self.moveDir = nil
    self.rushFlag = false
    self.fxTimer = 0
    self.fxTimerCD = rushConfig.CD_rush
    self.waitTimer = 0
    self.fx_rushReady.gameObject:SetActive(false)
end
function manyknives_role_dafeilong:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    self:RushMove(deltaTime)
end

-- 冲刺移动
function manyknives_role_dafeilong:RushMove(deltaTime)
    if self.deadFlag then
        return
    end
    -- 闲逛
    if self.moveFlag == 1 then
        self.collision.enabled = true
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        self.fixedlyFlag = false
        self:SetAICanMove(true)
        self:MoveFollow(deltaTime)
        self.rushTimer = self.rushTimer + deltaTime
        if self.rushTimer >= self.rushTimerCD then
            self.moveFlag = 2
        end
        self.fxTimer = self.fxTimer + deltaTime
        -- 准备冲
    elseif self.moveFlag == 2 and not (self.debuff_freeze_Flag or self.debuff_light_Flag) then
        self.collision.enabled = false
        self.fixedlyFlag = true
        self:SetAICanMove(false)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        self.fxTimer = self.fxTimer + deltaTime
        if self.fxTimer >= self.fxTimerCD then
            self:HideRushGuide()
            self.rushCenter.gameObject:SetActive(true)
            self.fx_rushReady.gameObject:SetActive(false)
            self.moveFlag = 3
            self.moveDis = 0
            self.rushFlag = true
            local deg = bc_CS_Vector2.Angle(bc_CS_Vector2(self.moveDir.x, self.moveDir.y), bc_CS_Vector2.right)
            if self.moveDir.y < 0 then
                deg = -deg
            end
            self.rushCenter.localRotation = bc_CS_Quaternion.Euler(0, 0, deg)
            self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.monster_dash)
            -- 提前一秒确定方向，降低难度
        elseif self.fxTimer >= self.fxTimerCD - rushConfig.rush_DirTimerOff and self.moveDir == nil then
            self.fx_rushReady:Simulate(0, true)
            self.fx_rushReady:Play()
            self.fx_rushReady.gameObject:SetActive(true)
            self.moveDir = (self.player.rigidbody.position - self.rigidbody.position).normalized
            self.rushGuide = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.rushGuide)
            self.rushGuide:Init(self.sceneMgr, ManyKnivesDefine.skillNames.rushGuide)
            local angle = bc_CS_Vector3.Angle(bc_CS_Vector3(self.moveDir.x, self.moveDir.y, 0), bc_CS_Vector3.right)
            if self.moveDir.y < 0 then
                angle = -angle
            end
            self.rushGuide:Play(1, bc_CS_Vector2(rushConfig.rush_Limit, rushConfig.rushGuide_height),
                self.bladeTran.position, bc_CS_Quaternion.Euler(0, 0, angle))
        end
        if self.rushGuide ~= nil then
            self.rushGuide:SetFill(1 - (self.fxTimerCD - self.fxTimer) / rushConfig.rush_DirTimerOff)
        end
        -- 眩晕
    elseif self.moveFlag == 4 then
        self.collision.enabled = false
        self.fixedlyFlag = true
        self:SetAICanMove(false)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.idle)
        self.waitTimer = self.waitTimer + deltaTime
        if self.waitTimer >= rushConfig.dizziness then
            self.fx_xuanyun.gameObject:SetActive(false)
            self.curMoveSp = self.roleData.speed
            self.moveSpBind:setValue(self.curMoveSp)
            self.moveFlag = 1
            self.rushCenter.gameObject:SetActive(false)
            self.fxTimer = 0
            self.rushTimer = 0
            self.moveDir = nil
            self.rushFlag = false
        end
    elseif not (self.debuff_freeze_Flag or self.debuff_light_Flag) then
        self.collision.enabled = false
        self.fixedlyFlag = true
        self:SetAICanMove(false)
        self.curMoveSp = rushConfig.rush_MoveSpeed
        self.moveSpBind:setValue(self.curMoveSp)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        self:SetDisplayFlip(self.moveDir.x < 0)
        local tmpVec2Pos = self.transform.position
        tmpVec2Pos = bc_CS_Vector2(tmpVec2Pos.x, tmpVec2Pos.y)
        tmpVec2Pos = self.sceneMgr.cameraCtrl:LimitPosInMap(tmpVec2Pos + self.moveDir *
                                                                (self.moveSpBind.value * deltaTime))
        self.transform.position = bc_CS_Vector3(tmpVec2Pos.x, tmpVec2Pos.y, 0)
        self.moveDis = self.moveDis + (self.moveDir * self.moveSpBind.value).magnitude * deltaTime
        -- 移动距离大于8m，眩晕2秒
        if self.moveDis >= rushConfig.rush_Limit then
            local safetyPos = self.sceneMgr:GetSafetyPosition(self.transform.position)
            safetyPos.z = 0
            self.transform.position = safetyPos
            self.waitTimer = 0
            self.moveFlag = 4
            self.fx_xuanyun:Simulate(0, true)
            self.fx_xuanyun:Play()
            self.fx_xuanyun.gameObject:SetActive(true)
            self.rushCenter.gameObject:SetActive(false)
        end
    end
end
function manyknives_role_dafeilong:HideRushGuide()
    if self.rushGuide ~= nil then
        self.rushGuide:PushInPool()
        self.rushGuide = nil
    end
end

function manyknives_role_dafeilong:deaded()
    self.fx_xuanyun.gameObject:SetActive(false)
    self.rushCenter.gameObject:SetActive(false)
    self.fx_rushReady.gameObject:SetActive(false)
    self:HideRushGuide()
    self.__base:deaded()
end

function manyknives_role_dafeilong:dispose()
    self:HideRushGuide()
    self.__base:dispose()
end

return manyknives_role_dafeilong
