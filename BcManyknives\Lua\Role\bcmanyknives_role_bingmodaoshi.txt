local manyknives_role_bingmodaoshi = TinyRush_CreateClass("bcmanyknives_role_bingmodaoshi"):baseClass(require(
    "bcmanyknives_rolebase_item"))
local shotPoolName = "iceShot"
local shotSpeed = 15
local shotDistance = 30
local shotLimit = 13
local runDisLimit = 8

-- 发射子弹的位置
manyknives_role_bingmodaoshi.shotPos = nil
manyknives_role_bingmodaoshi.changeActionFlag = nil

function manyknives_role_bingmodaoshi:ctor(...)
    self.__base:ctor(...)
    self.shotPos = self.dataSrc["shotPos"]
end

-- 出生点设置
function manyknives_role_bingmodaoshi:Init(...)
    self.__base:Init(...)
    self.atkSpBind:setValue(1)
    self.atkTimerCD = 2.3
    self.atkTimer = self.atkTimerCD
    self.idleTimer = 0
    self.idleTimerCD = 0.5
    -- 1:移动，2：攻击
    self.actionState = 1
    self.changeActionFlag = false
end

function manyknives_role_bingmodaoshi:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    if self.deadFlag then
        return
    end
    self.atkTimer = self.atkTimer + deltaTime
    -- 距离玩家15m时才发射子弹
    local disFormPlayer = bc_CS_Vector3.Distance(self.transform.position, self.player.transform.position)
    if self.actionState == 2 then
        self:SetAICanMove(false)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.attack)
        self.idleTimer = self.idleTimer + deltaTime
        if self.idleTimer >= self.idleTimerCD then
            self.actionState = 1
            self.idleTimer = 0
        end
    else
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        -- 距离玩家大于15m,往玩家方向走
        if disFormPlayer > shotLimit then
            self:SetAICanMove(true)
            self:MoveFollow(deltaTime)
            if not self.changeActionFlag then
                self.AIPath:SearchPath()
            end
            self.changeActionFlag = true
            -- 小于10m。逃跑
        elseif disFormPlayer < runDisLimit and not (self.debuff_freeze_Flag or self.debuff_light_Flag) then
            self:SetAICanMove(true)
            local dir = (self.transform.position - self.player.transform.position).normalized
            self.moveOffTimer = self.moveOffTimer + deltaTime
            if self.moveOffTimer >= ManyKnivesDefine.roleMoveOff.offTimeCD then
                self.moveOffTimer = 0
                self.moveOffRot = bc_CS_Quaternion.Euler(0, 0,
                    math.random(ManyKnivesDefine.roleMoveOff.offAngleMin, ManyKnivesDefine.roleMoveOff.offAngleMax) *
                        (math.random() < 0.5 and -1 or 1))
            end
            if self.moveOffRot ~= nil then
                dir = (self.moveOffRot * bc_CS_Vector3(dir.x, dir.y, 0)).normalized
            end
            local tarPos = self.transform.position + dir * (runDisLimit * 2)
            tarPos = self.sceneMgr:GetSafetyPosition(tarPos)
            tarPos.z = 0
            self.AIPath.destination = tarPos
            self:RefreshDisplayFlip()
            if self.changeActionFlag then
                self.AIPath:SearchPath()
                self.changeActionFlag = false
            end
        elseif disFormPlayer <= shotLimit and not self.debuff_freeze_Flag and self.atkTimer >= self.atkTimerCD then
            self:SetAICanMove(false)
            self.changeActionFlag = true
            -- 射程范围内
            self.actionState = 2
            self.atkTimer = 0
            local dir = (self.player.rigidbody.position - self.rigidbody.position)
            self:SetDisplayFlip(dir.x < 0)
            local shotDir = (self.player.center.position - self.shotPos.position).normalized
            shotDir.z = 0
            local startPos = self.shotPos.position
            local shotObj = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_bingzhangjineng)
            shotObj:GetColliderKey().name = ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                                                ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                                                ManyKnivesDefine.effectType.enemyBall
            shotObj:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_bingzhangjineng, self)
            shotObj:Play(1, startPos, shotDir, 20, 10, -90)
        end
    end
end

return manyknives_role_bingmodaoshi
