local manyknives_role_boss = TinyRush_CreateClass("bcmanyknives_role_rushboss"):baseClass(require(
    "bcmanyknives_role_boss"))
-- 冲刺
manyknives_role_boss.rushCenter = nil
-- 冲刺标记
manyknives_role_boss.rushFlag = nil
manyknives_role_boss.rushGuide = nil
manyknives_role_boss.fx_rushReady = nil
-- 冲刺配置
manyknives_role_boss.rushConfig = nil
-- 1：闲逛，2：准备冲，3：冲刺状态
manyknives_role_boss.rushFlagBind = nil
-- 1技能冷却中，2释放技能中
manyknives_role_boss.moveFlag = nil
manyknives_role_boss.furyFlagBind = nil
-- 冲刺伤害
manyknives_role_boss.rushBossDmg = nil

manyknives_role_boss.fx_Fury = nil
manyknives_role_boss.fx_Fury_Burst = nil

-- Boss技能计数相关配置
local skill_Config = {
    -- 冲刺指引得高度
    rushGuide_height = 3.6
}

function manyknives_role_boss:ctor(...)
    self.__base:ctor(...)
    self.rushCenter = self.dataSrc["rushCenter"]
    self.fx_rushReady = self.dataSrc["fx_boss_chongci_xuli"]
    self.fx_Fury = self.dataSrc["fx_boss_erjieduan"]
    self.fx_Fury_Burst = self.dataSrc["fx_boss_erjieduan_baofa"]
    self.rushFlagBind = require("tinyrush_bindable").new(0)
    self.furyFlagBind = require("tinyrush_bindable").new(false)
    self.rushFlagBind:register(function(value)
        self:MoveFlagListener(value)
    end)
    self.furyFlagBind:register(function(value)
        self:FuryListener(value)
    end)
    self.hpValueBind:register(function(value)
        self:HpListener()
    end)
end

function manyknives_role_boss:MoveFlagListener(value)
    if value == 0 then
        self.fixedlyFlag = true
        self.rushCenter.gameObject:SetActive(false)
        self.fx_rushReady.gameObject:SetActive(false)
        self.collision.enabled = true
        self:SetAICanMove(false)
        self.uncontrolled = false
        self.rushFlag = false
    elseif value == 1 then
        self.fixedlyFlag = false
        self.rushCenter.gameObject:SetActive(false)
        self.fx_rushReady.gameObject:SetActive(false)
        self.collision.enabled = true
        self:SetAICanMove(true)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        self.uncontrolled = false
        self.rushFlag = false
    elseif value == 2 then
        self.fixedlyFlag = true
        self.rushCenter.gameObject:SetActive(false)
        self.fx_rushReady.gameObject:SetActive(true)
        self.collision.enabled = false
        self:SetAICanMove(false)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.idle)
        self:KillBackUpTween()
        self.uncontrolled = true
        self.rushFlag = false
    else
        self.fixedlyFlag = true
        self:SetAICanMove(false)
        self.collision.enabled = false
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        self.rushCenter.gameObject:SetActive(true)
        self.fx_rushReady.gameObject:SetActive(false)
        self.uncontrolled = true
        self.rushFlag = true
    end
end

function manyknives_role_boss:Init(...)
    self.__base:Init(...)
    self.rushConfig = self.sceneMgr:GetRushConfigById(self.roleData.skillLevel)
    self.rushBossDmg = self.rushConfig.rush_Dmg
end

function manyknives_role_boss:Start()
    self.furyFlagBind:setValue(false)
    self.rushFlagBind:setValue(0, true)
    self.rushFlagBind:invokeEvent()
    self.__base:Start()
end

function manyknives_role_boss:Ready()
    self.__base:Ready()
    self.rushFlagBind:setValue(1)
    self.moveFlag = 1
    self.skillTimer = 0
    self.rushTimer = 0
    self.rushCount = 0
    self.moveDir = nil
    self:HpListener()
    self.sceneMgr.tutMgr:ShowBossGuide_Rush()
end

function manyknives_role_boss:HpListener()
    if self.actionFlag and not self.furyFlagBind.value and self.rushConfig.retinue_Enable then
        -- 转阶段
        if (self.hpValueBind.value / self.hpMaxBind.value) * 100 <= self.rushConfig.transLimit then
            self.furyFlagBind:setValue(true)
        end
    end
end
function manyknives_role_boss:FuryListener(value)
    if value then
        self.fx_Fury:Simulate(0, true)
        self.fx_Fury:Play()
        self.fx_Fury_Burst:Simulate(0, true)
        self.fx_Fury_Burst:Play()
    end
    self.fx_Fury.gameObject:SetActive(value)
    self.fx_Fury_Burst.gameObject:SetActive(value)
    if self.actionFlag and value then
        local roleName = ManyKnivesDefine.roleNames.axeboss2
        local roleLevel = self.rushConfig.retinueLevel
        local centerPos = self.transform.position
        -- 生成几个分身
        for i = 1, self.rushConfig.retinueNum, 1 do
            local tmpRole = self.sceneMgr:RolePoolPopOne(roleName)
            tmpRole.transform:SetParent(self.sceneMgr.levelRoot.transform)
            -- 生成在3米外
            local dir = bc_CS_Vector3(math.lerp(-1, 1, math.random()), math.lerp(-1, 1, math.random()), 0).normalized
            local newPos = self.sceneMgr:GetSafetyPosition(centerPos + dir * 3)
            newPos.z = 0
            tmpRole:Init(self.sceneMgr, self.sceneMgr.uiMgr, roleName, self.sceneMgr:GetRoleCofig(roleName, roleLevel),
                newPos)
            tmpRole:GetColliderKey().name = ManyKnivesDefine.triggerType.role .. ManyKnivesDefine.names.split ..
                                                ManyKnivesDefine.names.enemy
        end
        self.sceneMgr:PlayerRepulse(centerPos)
    end
end

function manyknives_role_boss:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    if self.deadFlag or not self.actionFlag then
        return
    end
    -- 技能冷却中
    if self.moveFlag == 1 then
        self:MoveFollow(deltaTime)
        if self.rushConfig.rush_Enable then
            self.skillTimer = self.skillTimer + deltaTime
            if self.skillTimer >= self.rushConfig.skillCD then
                self.rushFlagBind:setValue(2)
                self.rushTimer = 0
                self.moveFlag = 2
                self.rushCount = 0
            end
        end
    elseif not (self.debuff_freeze_Flag or self.debuff_light_Flag) then
        -- 冲刺间隔闲逛
        if self.rushFlagBind.value == 1 then
            self.rushTimer = self.rushTimer + deltaTime
            if self.rushTimer >= self.rushConfig.rush_interval then
                self.rushFlagBind:setValue(2)
                self.rushTimer = 0
            end
            -- 准备冲
        elseif self.rushFlagBind.value == 2 then
            self.rushTimer = self.rushTimer + deltaTime
            if self.rushGuide == nil then
                self.fx_rushReady:Simulate(0, true)
                self.fx_rushReady:Play()
                self.fx_rushReady.gameObject:SetActive(true)
                self.moveDir = (self.player.rigidbody.position - self.rigidbody.position).normalized
                self.rushGuide = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.rushGuide)
                self.rushGuide:Init(self.sceneMgr, ManyKnivesDefine.skillNames.rushGuide)
                local angle = bc_CS_Vector3.Angle(bc_CS_Vector3(self.moveDir.x, self.moveDir.y, 0), bc_CS_Vector3.right)
                if self.moveDir.y < 0 then
                    angle = -angle
                end
                self.rushGuide:Play(1, bc_CS_Vector2(self.rushConfig.rush_Distance, skill_Config.rushGuide_height),
                    self.bladeTran.position, bc_CS_Quaternion.Euler(0, 0, angle))
            end
            local attentCD = self.rushCount == 0 and self.rushConfig.rush_attent_First or self.rushConfig.rush_attent
            if self.rushTimer >= attentCD then
                self.rushFlagBind:setValue(3)
                self.moveDis = 0
                self:HideRushGuide()
                local deg = bc_CS_Vector2.Angle(bc_CS_Vector2(self.moveDir.x, self.moveDir.y), bc_CS_Vector2.right)
                if self.moveDir.y < 0 then
                    deg = -deg
                end
                self.rushCenter.localRotation = bc_CS_Quaternion.Euler(0, 0, deg)
                self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.boss_dash)
            end
            if self.rushGuide ~= nil then
                self.rushGuide:SetFill(self.rushTimer / attentCD)
            end
        else
            self.curMoveSp = self.rushConfig.rush_MoveSpeed
            self.moveSpBind:setValue(self.curMoveSp)
            self:SetDisplayFlip(self.moveDir.x < 0)
            local tmpVec2Pos = self.transform.position
            tmpVec2Pos = bc_CS_Vector2(tmpVec2Pos.x, tmpVec2Pos.y)
            tmpVec2Pos = self.sceneMgr.cameraCtrl:LimitPosInMap(tmpVec2Pos + self.moveDir *
                                                                    (self.moveSpBind.value * deltaTime))
            self.transform.position = bc_CS_Vector3(tmpVec2Pos.x, tmpVec2Pos.y, 0)
            self.moveDis = self.moveDis + (self.moveDir * self.moveSpBind.value).magnitude * deltaTime
            if self.moveDis >= self.rushConfig.rush_Distance then
                self.curMoveSp = self.roleData.speed
                self.moveSpBind:setValue(self.curMoveSp)
                self.rushCount = self.rushCount + 1
                if self.rushCount >= self.rushConfig.rushCount then
                    self.moveFlag = 1
                    self.skillTimer = 0
                    local safetyPos = self.sceneMgr:GetSafetyPosition(self.transform.position)
                    safetyPos.z = 0
                    self.transform.position = safetyPos
                end
                self.rushTimer = 0
                self.rushFlagBind:setValue(1)
            end
        end
    end
end

function manyknives_role_boss:HideRushGuide()
    if self.rushGuide ~= nil then
        self.rushGuide:PushInPool()
        self.rushGuide = nil
    end
end

function manyknives_role_boss:deaded()
    self.__base:deaded()
end

function manyknives_role_boss:recycle()
    self.rushCenter.gameObject:SetActive(false)
    self.fx_rushReady.gameObject:SetActive(false)
    self.fx_Fury.gameObject:SetActive(false)
    self.fx_Fury_Burst.gameObject:SetActive(false)
    self:HideRushGuide()
    self.__base:recycle()
end
function manyknives_role_boss:dispose()
    self.__base:dispose()
end

return manyknives_role_boss
