local ctrl = TinyRush_CreateClass("bcmanyknives_anim_ctrl")

ctrl.animator = nil
ctrl.curState = nil
-- 移动速度监听
ctrl.moveSpUnReg = nil
-- 攻击速度监听
ctrl.attackSpUnReg = nil
ctrl.moveSp = nil
ctrl.atkSp = nil
ctrl.freezeFlag = nil

function ctrl:ctor(...)
    local moveSpBind = nil
    local atkBind = nil
    self.animator, moveSpBind, atkBind = ...
    self.moveSpUnReg = moveSpBind:register(function(value)
        self:MoveSpeedListener(value)
    end, true)
    if atkBind ~= nil then
        self.attackSpUnReg = atkBind:register(function(value)
            self:AtkSpeedListener(value)
        end, true)
    end
end
function ctrl:AtkSpeedListener(value)
    self.atkSp = value
    self:SetAnimSpeed()
end
function ctrl:MoveSpeedListener(value)
    self.moveSp = value
    self:SetAnimSpeed()
end

function ctrl:SetAnimSpeed()
    local sp = 1
    if self.freezeFlag then
        sp = 0
    else
        if self.curState == ManyKnivesDefine.AnimatorState.attack then
            sp = self.atkSp
        elseif self.curState == ManyKnivesDefine.AnimatorState.walk then
            sp = self.moveSp / 3
        end
    end
    self.animator.speed = sp
end

function ctrl:Freeze(value)
    self.freezeFlag = value
    self:SetAnimSpeed()
end

function ctrl:Play(animState)
    if self.curState ~= animState then
        self.curState = animState
        self.animator:SetInteger(ManyKnivesDefine.AnimatorName, animState)
    end
    self:SetAnimSpeed()
end

function ctrl:dispose()
    if self.moveSpUnReg ~= nil then
        self.moveSpUnReg:unRegister()
        self.moveSpUnReg = nil
    end
    if self.attackSpUnReg ~= nil then
        self.attackSpUnReg:unRegister()
        self.attackSpUnReg = nil
    end
end

return ctrl
