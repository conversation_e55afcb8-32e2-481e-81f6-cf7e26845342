---@class kingshot_teamunit : fusion_gopoolitem
local unit = bc_Class("kingshot_teamunit", require("fusion_gopoolitem"))
---@type kingshot_scene_mgr
unit.sceneMgr = nil
---@type kingshot_team
unit.teamCtrl = nil
unit.DataSrc = nil
---@type kingshot_TeamConfig
unit.config = nil
---@type kingshot_enemyData
unit.teamData = nil
---@type number 记录队伍状态 0=集合状态，1=解散，2=成员死完
unit.ActionFlag = nil

unit.characterArray = nil
---@type number 队伍存活数量
unit.characterCount = nil

unit.tween_TeamRot = nil

unit.arrived = false
unit.runtimeDistance = 0

local wanderDurRandom = { 5, 8 }
local borderOff = 5


function unit:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
end

function unit:Init(sceneMgr, ctrl, data, config)
    self.ActionFlag = 0
    self.DataSrc.NavMeshAgent.enabled = true
    self.sceneMgr = sceneMgr
    self.teamCtrl = ctrl
    local actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    self.teamData = data
    self.config = config

    KingShot_Define.SetTransformPositionXYZ(self.transform, self.teamData.Pos.x, self.teamData.Pos.y,
            self.teamData.Pos.z)
    
    self.DataSrc.NavMeshAgent.speed = self.config.MoveSpeed
    self.gameObject:SetActive(true)

    self.characterArray = {}
    local pointArray = self:SpawnPointWithCount(self.config.UnitCount)
    local tmpIndex = 0
    for _, data in ipairs(self.config.UnitDatas) do
        for i = 1, data.Count, 1 do
            local character = actorMgr:CreateCharacter(data.UnitId, pointArray[tmpIndex], self.transform)
            character.troopClash_IgnoreBattleUpdate = true
            -- character:ResetCooldown()
            tmpIndex = tmpIndex + 1
            self.characterArray[tmpIndex] = character
            self.teamCtrl.TeamUnitWithCharacter[character] = self
        end
    end
    self.characterCount = tmpIndex
    --self:Wandering()
end

function unit:SpawnPointWithCount(count)
    local pointArray = {}
    local golden_angle = math.pi * (3 - math.sqrt(5)) -- 黄金角的弧度值，大约是2.39996
    local max_radius = self.config.Radius
    for i = 0, count - 1, 1 do
        -- 基础参数计算
        local base_radius = math.sqrt(i / count) * max_radius
        local base_theta = i * golden_angle
        -- 添加半径扰动 (±5%)
        local radius_jitter = 1 + (math.random() - 0.5) * 0.1
        local final_radius = math.min(base_radius * radius_jitter, max_radius)
        -- 添加角度扰动 (±15度)
        local angle_jitter = math.rad((math.random() - 0.5) * 30)
        local final_theta = base_theta + angle_jitter
        -- 坐标计算
        local x = final_radius * math.cos(final_theta)
        local z = final_radius * math.sin(final_theta)
        pointArray[i + 1] = { x = x, z = z }
    end
    return pointArray
end

---新的游荡任务
function unit:Wandering()
    local curPos = self.transform.position
    local moveLength = math.random(wanderDurRandom[1], wanderDurRandom[2]) * self.config.MoveSpeed
    local playerDir = self.sceneMgr.playerCtrl:GetCenterVec3() - curPos
    playerDir.y = 0
    local tmpRot = KingShot_Define.CS.Quaternion.LookRotation(playerDir.normalized) *
        KingShot_Define.CS.Quaternion.Euler(0, math.random(-70, 70), 0)
    local tarPos = curPos + (tmpRot * KingShot_Define.CacheVector3.Forward).normalized * moveLength
    self.DataSrc.NavMeshAgent:SetDestination(tarPos)
end

---沿着设定的path行走
function unit:MoveAlongPath(deltaTime)
    local path = self.sceneMgr:GetBakedPath()
    if not self.arrived and path then
        self.runtimeDistance = self.runtimeDistance + self.config.MoveSpeed * deltaTime
        local adjustedDistance = path.PathDistance * 0.999
        self.runtimeDistance = math.clamp(self.runtimeDistance,0,adjustedDistance)
        if self.runtimeDistance >= adjustedDistance then
            self.arrived = true
        end
        local newPos = path:GetPositionAtDistance(self.runtimeDistance)
        --local newRo = path.GetRotationAtDistance(self.runtimeDistance, useCustomUpVector ? customUpVector : path.GetUpVectorAtDistance(runtimeDistance))
        KingShot_Define.SetTransformPositionXYZ(self.transform, newPos.x, newPos.y, newPos.z)
    end
end

function unit:Update(deltaTime, viewCenterPos, pX, pZ)
    if self.ActionFlag ~= 0 then
        return
    end
    --怪物太远，重置位置
    local resetWander = false
    local curX, curY, curZ = KingShot_Define.GetTransformPositionXYZ(self.transform)
    --if math.abs(curX - viewCenterPos.x) - self.config.Radius - borderOff > self.sceneMgr.cameraCtrl.camBorderSizeHalf.x
    --    or math.abs(curZ - viewCenterPos.z) - self.config.Radius - borderOff > self.sceneMgr.cameraCtrl.camBorderSizeHalf.y then
    --    resetWander = true
    --    local newPos = self.sceneMgr:GetSpawnTeamPos()
    --    KingShot_Define.SetTransformPositionXYZ(self.transform, newPos.x, newPos.y, newPos.z)
    --end
    if resetWander or
        (self.DataSrc.NavMeshAgent.hasPath and not self.DataSrc.NavMeshAgent.pathPending
            and self.DataSrc.NavMeshAgent.remainingDistance <= self.DataSrc.NavMeshAgent.stoppingDistance) then
        --self:Wandering()
    end
    self:MoveAlongPath(deltaTime)
    for _, v in ipairs(self.characterArray) do
        v:UpdateAI()
        if v._character_entity and bc_IsNotNull(v._character_entity.modelGo) and not v._character_entity.modelGo.activeSelf then
            v._character_entity.modelGo:SetActive(true)
        end
    end
    local disFromPlayer = KingShot_Define.Func_GetDistance(pX, pZ, curX, curZ) -
        self.sceneMgr.playerCtrl.RadiusBind.value - self.config.Radius
    if disFromPlayer < self.config.SearchRange then
        self:Dissolution()
    end
end

--队伍解散
function unit:Dissolution()
    if self.ActionFlag == 0 then
        self.ActionFlag = 1
        self.DataSrc.NavMeshAgent.enabled = false
        for _, v in ipairs(self.characterArray) do
            v.troopClash_IgnoreBattleUpdate = false
        end
    end
end

---队伍成员死亡
---@return boolean 是否全部死亡
function unit:CharacterDie(character)
    self.characterCount = self.characterCount - 1
    return self.characterCount < 1
end

return unit
