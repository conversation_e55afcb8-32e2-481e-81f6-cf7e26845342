---@class fusion_taskscheduling_mgr : fusion_mgrbase 任务分帧执行
local mgr = bc_Class("fusion_taskscheduling_mgr", Fusion.MgrBase)
-- 任务队列
mgr.taskQueue = nil
mgr.taskCount = nil
mgr.updateFrameCount = nil

function mgr:__init()
    self.taskQueue = {}
    self.taskCount = 0
    self.updateFrameCount = 0
end

function mgr:__delete()
    self.taskQueue = nil
end

function mgr:OnUpdate(deltaTime)
    if self.taskCount < 1 then
        return
    end
    self:invokeTaskUpdate()
end

function mgr:invokeTaskUpdate()
    if self.taskCount > 0 then
        self.updateFrameCount = self.updateFrameCount + 1
        local tmpTask = self.taskQueue[1]
        if self.updateFrameCount >= tmpTask.frame then
            tmpTask.func()
            self.updateFrameCount = 0
            table.remove(self.taskQueue, 1)
            self.taskCount = self.taskCount - 1
        end
    end
end

--- 增加一个任务：每帧只执行一个任务。
---@param func fun()
---@param delay number 延迟几帧 默认1
function mgr:AddTask(func, delay)
    if delay == nil then
        delay = 1
    end
    self.taskCount = self.taskCount + 1
    self.taskQueue[self.taskCount] = {
        frame = delay,
        func = func
    }
end

return mgr
