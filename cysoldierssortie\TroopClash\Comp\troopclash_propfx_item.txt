---@class troopclash_propfx_item : fusion_gopoolitem
local fxItem = bc_Class("troopclash_propfx_item", require("fusion_gopoolitem"))
---@type troopclash_prop
fxItem.Ctrl = nil
fxItem.DataSrc = nil

function fxItem:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
end

function fxItem:Play(showCircle, showDrop)
    self.gameObject:SetActive(true)
    self.DataSrc.FxCircle.gameObject:SetActive(showCircle)
    self.DataSrc.FxDrop.gameObject:SetActive(showDrop)
end

return fxItem
