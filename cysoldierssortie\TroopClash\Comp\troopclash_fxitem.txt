---@class troopclash_fxitem : fusion_gopoolitem
local fxItem = bc_Class("troopclash_fxitem", require("fusion_gopoolitem"))
fxItem.Fx = nil
fxItem.tween = nil

local pathType = TroopClash_Define.TweenPathType.CatmullRom
local dropSpeed = 10
local dropEase = TroopClash_Define.TweenEase.Linear

function fxItem:__init(...)
    self:Ctor(...)
    self.Fx = self.gameObject:GetComponent(TroopClash_Define.TypeOf.ParticleSystem)
end

---开始播放特效
---@param reset boolean 是否重置特效
function fxItem:Play(reset, duration, delayCall)
    self.gameObject:SetActive(true)
    if reset then
        self.Fx:Simulate(0, true)
        self.Fx:Play()
    end
    if duration ~= nil and delayCall ~= nil then
        self.tween = TroopClash_Define.DOVirtual.DelayedCall(duration, delayCall)
    end
end

---播放抛物线特效
function fxItem:PlayParabola(oriPos, endPos, curvePath, curPathCount, moveCall, overDuration, completeCall)
    self.gameObject:SetActive(true)
    TroopClash_Define.SetTransformPositionXYZ(self.transform, oriPos.x, oriPos.y, oriPos.z)
    TroopClash_Define.SetTransformLocalScale(self.transform, 0.1, 0.1, 0.1)
    local newPath, moveLength = TroopClash_Define.Func_GetPathByCurve(oriPos, endPos, curvePath, curPathCount)
    local moveDur = moveLength / dropSpeed
    self.tween_Drop = TroopClash_Define.DOTween.Sequence()
    self.tween_Drop:Append(self.transform:DOPath(newPath, moveDur, pathType):SetEase(dropEase))
    self.tween_Drop:Insert(0, self.transform:DOScale(TroopClash_Define.CacheVector3.One, moveDur):SetEase(dropEase))
    self.tween_Drop:AppendCallback(moveCall)
    if overDuration ~= nil and completeCall ~= nil then
        self.tween_Drop:AppendInterval(overDuration)
        self.tween_Drop:OnComplete(completeCall)
    end
end

function fxItem:KillTween()
    if self.tween ~= nil then
        self.tween:Kill()
        self.tween = nil
    end
end

function fxItem:Recycle()
    self:KillTween()
end

function fxItem:__delete()
    self:KillTween()
    self:Dispose()
end

return fxItem
