local manyknives_scene_mgr = TinyRush_CreateClass("bcmanyknives_scene_mgr"):baseClass(TinyRush_Scope):interface(
    TinyRush_IInit, TinyRush_IUpdate, TinyRush_ILateUpdate, TinyRush_IFixedUpdate)
local goPool = require("tinyrush_gopool")

local itemNames = {
    Camera = "Camera",
    CameraParent = "CameraParent"
}

manyknives_scene_mgr.resMgr = nil
manyknives_scene_mgr.mono = nil
manyknives_scene_mgr.gameMgr = nil
manyknives_scene_mgr.audioMgr = nil
manyknives_scene_mgr.tutMgr = nil

manyknives_scene_mgr.AS_BGM = nil
manyknives_scene_mgr.AS_FX = nil
manyknives_scene_mgr.AS_FX_Loop_Craze = nil

-- 控制全局游戏暂停继续
manyknives_scene_mgr.pauseBind = nil

-- 放对象池的容器
manyknives_scene_mgr.bladePoolParent = nil
manyknives_scene_mgr.rolePoolParent = nil
manyknives_scene_mgr.propPoolParent = nil

-- 刀刃的对象池
manyknives_scene_mgr.bladePool = nil
-- 道具的对象池
manyknives_scene_mgr.propPool = nil
-- 角色的对象池
manyknives_scene_mgr.rolePool = nil
-- 特效对象池
manyknives_scene_mgr.effectPool = nil

manyknives_scene_mgr.dataSrc = nil
manyknives_scene_mgr.mainCamera = nil
manyknives_scene_mgr.gameMgr = nil
manyknives_scene_mgr.bgSR = nil
manyknives_scene_mgr.PathFinder = nil
manyknives_scene_mgr.playerPos = nil

manyknives_scene_mgr.rolePlayer = nil
manyknives_scene_mgr.readyFlag = nil

manyknives_scene_mgr.cameraCtrl = nil
-- 记录关卡生成波次
manyknives_scene_mgr.levelWaveBind = nil
manyknives_scene_mgr.levelWaveMax = nil
-- 关卡配置
manyknives_scene_mgr.levelConfigTable = nil
-- 全部角色配置
manyknives_scene_mgr.roleConfigTable = nil
-- 生成关卡内容计时器
manyknives_scene_mgr.spawnTimer = nil
-- 关卡敌人总数量
manyknives_scene_mgr.enemyNumMax = nil
-- 记录已经击杀数量
manyknives_scene_mgr.enemyNumBind = nil
-- 是否Boss关卡
manyknives_scene_mgr.bossFlag = nil
-- 记录当前boss类型
manyknives_scene_mgr.curBossType = nil
manyknives_scene_mgr.curBossLevel = nil
manyknives_scene_mgr.curBossRetinueNum = nil
-- 记录当前关卡有那些敌人，对象池只创建需要的敌人
manyknives_scene_mgr.curEnemyTypeList = nil
-- Boss生成时间
manyknives_scene_mgr.bossTimer = nil
-- Boss倒计时
manyknives_scene_mgr.bossTimerBind = nil
-- 演出状态下，所有角色无伤
manyknives_scene_mgr.NoInjury = nil
-- 游戏结束标记
manyknives_scene_mgr.overFlag = nil
-- 随时间生成刀刃
manyknives_scene_mgr.spawnBladeTimer = nil
manyknives_scene_mgr.bladeCount = nil
-- 记录所有自动掉落的刀刃
manyknives_scene_mgr.autoDropBladeList = nil
manyknives_scene_mgr.autoBladeConfig = nil
-- 自动生成的刀刃回收检测CD
local autoBladeCheckCD = 0.5
local spawnViewMaxOff = 5
local spawnViewMinOff = 1
local mapBound = 2
-- 每帧最多生成多少
local spawnCountPerFrame = 5
local vecZero = bc_CS_Vector3.zero
local vecOne = bc_CS_Vector3.one

local NNConstraint_Default = CS.Pathfinding.NNConstraint.Default

function manyknives_scene_mgr:rush_OnInit()
    self.resMgr = self.lifeScope:get("bcmanyknives_res_mgr")
    self.mono = self.lifeScope:get("tinyrush_mono")
    self.gameMgr = self.lifeScope:get("bcmanyknives_game_mgr")
    self.uiMgr = self.lifeScope:get("bcmanyknives_ui_mgr")
    self.audioMgr = self.lifeScope:get("bcmanyknives_audio_mgr")
    self.tutMgr = self.lifeScope:get("bcmanyknives_tutorial_mgr")
    self.enemyNumBind = require("tinyrush_bindable").new(0)
    self.levelWaveBind = require("tinyrush_bindable").new(0)
    self.bossTimerBind = require("tinyrush_bindable").new(0)
    self.pauseBind = require("tinyrush_bindable").new(false)
    self.pauseBind:register(function(value)
        self:PauseListener(value)
    end)
    self.readyFlag = false
end

function manyknives_scene_mgr:RefreshPause()
    self:PauseListener(self.pauseBind.value)
end
function manyknives_scene_mgr:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.cgTween ~= nil then
        self.cgTween.timeScale = timeScale
    end
end

function manyknives_scene_mgr:dispose()
    self:KillCGTween()
    -- 清空对象池
    if self.rolePool ~= nil then
        for _, v in pairs(self.rolePool) do
            v:clear()
        end
        self.rolePool = nil
    end
    if self.bladePool ~= nil then
        for _, v in pairs(self.bladePool) do
            v:clear()
        end
        self.bladePool = nil
    end
    if self.propPool ~= nil then
        for _, v in pairs(self.propPool) do
            v:clear()
        end
        self.propPool = nil
    end
    if self.effectPool ~= nil then
        for _, v in pairs(self.effectPool) do
            v:clear()
        end
        self.effectPool = nil
    end
    if self.cameraCtrl ~= nil then
        self.cameraCtrl:dispose()
        self.cameraCtrl = nil
    end
    self.__base:dispose()
end

function manyknives_scene_mgr:rush_OnUpdate(deltaTime)
    if self.pauseBind.value then
        return
    end
    if self.startFlag then
        for _, v in pairs(self.rolePairWithGO) do
            v:OnUpdate(deltaTime)
        end
        self:startSpawn(deltaTime)
        self:AutoSpawnBlade(deltaTime)
    elseif self.readyFlag and self.rolePlayer ~= nil then
        self.rolePlayer:OnUpdate(deltaTime)
    end
end

function manyknives_scene_mgr:rush_OnFixedUpdate(deltaTime)
    if self.pauseBind.value then
        return
    end
    if self.readyFlag and self.rolePlayer ~= nil then
        self.rolePlayer:OnFixedUpdate(deltaTime)
    end
end

function manyknives_scene_mgr:rush_OnLateUpdate(deltaTime)
    if self.pauseBind.value then
        return
    end
    if self.readyFlag then
        self.cameraCtrl:OnFixedUpdate(deltaTime)
    end
    if self.startFlag then
        self.cameraCtrl:OnUpdate(deltaTime)
    end
    if self.startFlag then
        for _, v in pairs(self.rolePairWithGO) do
            v:OnLateUpdate(deltaTime)
        end
    elseif self.readyFlag and self.rolePlayer ~= nil then
        self.rolePlayer:OnLateUpdate(deltaTime)
    end
end

function manyknives_scene_mgr:AddBlade()
    self.bladeCount = self.bladeCount + 1
end
function manyknives_scene_mgr:ReduceBlade(blade)
    self.bladeCount = self.bladeCount - 1
    if blade ~= nil and blade.autoSpawnFlag then
        TinyRush_Table_Remove(self.autoDropBladeList, blade)
    end
end
-- 随时间生成刀刃
function manyknives_scene_mgr:AutoSpawnBlade(deltaTime)
    -- 场上少于50个，每帧生成10个
    if self.bladeCount < self.autoBladeConfig.maxNum then
        local viewMin = self.cameraCtrl:ViewportToWorldPoint(vecZero)
        local viewMax = self.cameraCtrl:ViewportToWorldPoint(vecOne)
        local sideList = {}
        -- 左边可用
        if viewMin.x - spawnViewMinOff > self.cameraCtrl.mapLeftBound + mapBound then
            sideList[#sideList + 1] = 1
        end
        -- 下方可用
        if viewMin.y - spawnViewMinOff > self.cameraCtrl.mapBottomBound + mapBound then
            sideList[#sideList + 1] = 2
        end
        -- 右方可用
        if viewMax.x + spawnViewMinOff < self.cameraCtrl.mapRightBound - mapBound then
            sideList[#sideList + 1] = 3
        end
        -- 上方可用
        if viewMax.y + spawnViewMinOff < self.cameraCtrl.mapTopBound - mapBound then
            sideList[#sideList + 1] = 4
        end
        local num = math.min(spawnCountPerFrame, self.autoBladeConfig.maxNum - self.bladeCount)
        local abNum = #self.autoDropBladeList
        local sideNum = #sideList
        if sideNum < 1 then
            sideList = {1, 2, 3, 4}
            sideNum = 4
        end
        for i = 1, num, 1 do
            -- 随机出现在周围
            local spawnDir = sideList[math.random(1, sideNum)]
            local tmpPos = nil
            -- 上方
            if spawnDir == 4 then
                tmpPos = bc_CS_Vector3(
                    math.lerp(viewMin.x - spawnViewMaxOff, viewMax.x + spawnViewMaxOff, math.random()),
                    math.lerp(viewMax.y + spawnViewMinOff, viewMax.y + spawnViewMaxOff, math.random()), 0)
            elseif spawnDir == 2 then
                -- 下方
                tmpPos = bc_CS_Vector3(
                    math.lerp(viewMin.x - spawnViewMaxOff, viewMax.x + spawnViewMaxOff, math.random()),
                    math.lerp(viewMin.y - spawnViewMaxOff, viewMin.y - spawnViewMinOff, math.random()), 0)
            elseif spawnDir == 1 then
                -- 左方
                tmpPos = bc_CS_Vector3(
                    math.lerp(viewMin.x - spawnViewMaxOff, viewMin.x - spawnViewMinOff, math.random()),
                    math.lerp(viewMin.y - spawnViewMaxOff, viewMax.y + spawnViewMaxOff, math.random()), 0)
            else
                -- 右方
                tmpPos = bc_CS_Vector3(
                    math.lerp(viewMax.x + spawnViewMinOff, viewMax.x + spawnViewMaxOff, math.random()),
                    math.lerp(viewMin.y - spawnViewMaxOff, viewMax.y + spawnViewMaxOff, math.random()), 0)
            end
            tmpPos = self:GetSafetyPosition(tmpPos)
            tmpPos.z = 0
            local newBlade = self:BladePoolPopOne(1)
            newBlade:InitWithoutRole(self, 1, false)
            newBlade:Drop(tmpPos, false)
            self.autoDropBladeList[abNum + i] = newBlade
        end
    else
        -- 场上刀刃大于最大值，回收屏幕外的刀刃
        self.spawnBladeTimer = self.spawnBladeTimer + deltaTime
        local tLength = #self.autoDropBladeList
        if tLength > 0 and self.spawnBladeTimer >= autoBladeCheckCD then
            self.spawnBladeTimer = 0
            local viewMin = self.cameraCtrl:ViewportToWorldPoint(vecZero)
            local viewMax = self.cameraCtrl:ViewportToWorldPoint(vecOne)
            local idx = {}
            local tmpI = 0
            for i = 1, tLength, 1 do
                local tmpPos = self.autoDropBladeList[i].transform.position
                if tmpPos.x < viewMin.x - spawnViewMaxOff or tmpPos.y < viewMin.y - spawnViewMaxOff or tmpPos.x >
                    viewMax.x + spawnViewMaxOff or tmpPos.y > viewMax.y + spawnViewMaxOff then
                    tmpI = tmpI + 1
                    idx[tmpI] = i
                    -- 每帧最多回收10个
                    if tmpI >= 10 then
                        break
                    end
                end
            end
            if tmpI > 0 then
                local removeI = 0
                for _, v in pairs(idx) do
                    self.autoDropBladeList[v - removeI]:PushInPool()
                    table.remove(self.autoDropBladeList, v - removeI)
                    removeI = removeI + 1
                end
                self.bladeCount = self.bladeCount - tmpI
            end
        end
    end
end

function manyknives_scene_mgr:InitSpawnBlade()
    local viewMin = self.cameraCtrl:ViewportToWorldPoint(vecZero)
    local viewMax = self.cameraCtrl:ViewportToWorldPoint(vecOne)
    local abNum = #self.autoDropBladeList
    for i = 1, self.autoBladeConfig.initNum, 1 do
        -- 随机出现在周围
        local tmpPos = bc_CS_Vector3(math.lerp(viewMin.x, viewMax.x, math.random()),
            math.lerp(viewMin.y, viewMax.y, math.random()), 0)
        tmpPos = self:GetSafetyPosition(tmpPos)
        tmpPos.z = 0
        local newBlade = self:BladePoolPopOne(1)
        newBlade:InitWithoutRole(self, 1, false)
        newBlade:Drop(tmpPos, false)
        self.autoDropBladeList[abNum + i] = newBlade
    end
end

-- 按id生成关卡内容
function manyknives_scene_mgr:startSpawn(deltaTime)
    if self.overFlag then
        return
    end
    if self.levelWaveBind.value > self.levelWaveMax then
        return
    end
    local curWaveData = self.levelConfigTable[self.levelWaveBind.value]
    if curWaveData == nil then
        return
    end
    self.spawnTimer = self.spawnTimer + deltaTime
    self.bossTimerBind:setValue(self.bossTimerBind.value - deltaTime)
    if self.spawnTimer >= curWaveData.time then
        -- 生成道具
        if curWaveData.propNum ~= 0 then
            self:SpawnPropPrefab(curWaveData.propNum, curWaveData.propType)
        end
        -- 生成敌人
        if curWaveData.enemyNum ~= 0 then
            self:SpawnEnemyPrefab(curWaveData.enemyNum, curWaveData.enemyType, curWaveData.enemyLevel)
        end
        self.levelWaveBind:setValue(self.levelWaveBind.value + 1)
    end
end

function manyknives_scene_mgr:RoleNoInjury(value)
    self.NoInjury = value
    for _, v in pairs(self.rolePairWithGO) do
        v:NoInjury(value)
    end
end
-- 击退玩家
function manyknives_scene_mgr:PlayerRepulse(pos, disLimit)
    local dis = bc_CS_Vector3.Distance(pos, self.rolePlayer.transform.position)
    -- 玩家被击退至5米外
    if disLimit == nil then
        disLimit = 10
    end
    if dis < disLimit then
        local dir = (self.rolePlayer.transform.position - pos)
        dir.z = 0
        dir = dir.normalized
        local newPos = pos + (dir * disLimit)
        newPos = self:GetSafetyPosition(newPos)
        newPos.z = 0
        self.rolePlayer:PlayerRepulse(newPos)
    end
end
--- 生产敌人对象
function manyknives_scene_mgr:SpawnEnemyPrefab(num, type, level)
    local roleName = ManyKnivesDefine.roleTypeWithName[type]
    -- BOSS入场
    if type >= 7 and type <= 9 then
        self.uiMgr:bossCGPlay(function()
            local spawnPos = bc_CS_Vector3(0.5, 0.7, 0)
            spawnPos = self.cameraCtrl:ViewportToWorldPoint(spawnPos)
            spawnPos.z = 0
            self:SpawnEnemy(roleName, spawnPos, num, level)
        end)
    else
        self:SpawnEnemy(roleName, nil, num, level)
    end
end
function manyknives_scene_mgr:SpawnEnemy(roleName, spawnPos, num, level)
    for i = 1, num do
        local tmpGO = self:RolePoolPopOne(roleName)
        tmpGO.transform:SetParent(self.levelRoot.transform)
        tmpGO:Init(self, self.uiMgr, roleName, self:GetRoleCofig(roleName, level), self:getRoleSpawnPos(spawnPos))
        tmpGO:GetColliderKey().name = ManyKnivesDefine.triggerType.role .. ManyKnivesDefine.names.split ..
                                          ManyKnivesDefine.names.enemy
    end
end

function manyknives_scene_mgr:getRoleSpawnPos(spawnPos)
    if spawnPos == nil then
        local viewMin = self.cameraCtrl:ViewportToWorldPoint(vecZero)
        local viewMax = self.cameraCtrl:ViewportToWorldPoint(vecOne)
        local sideList = {}
        local sideNum = 0
        -- 左边可用
        if viewMin.x > self.cameraCtrl.mapLeftBound + mapBound then
            sideNum = sideNum + 1
            sideList[sideNum] = 1
            sideNum = sideNum + 1
            sideList[sideNum] = 1
            sideNum = sideNum + 1
            sideList[sideNum] = 1
            sideNum = sideNum + 1
            sideList[sideNum] = 1
        end
        -- 下方可用
        if viewMin.y > self.cameraCtrl.mapBottomBound + mapBound then
            sideNum = sideNum + 1
            sideList[sideNum] = 2
        end
        -- 右方可用
        if viewMax.x < self.cameraCtrl.mapRightBound - mapBound then
            sideNum = sideNum + 1
            sideList[sideNum] = 3
            sideNum = sideNum + 1
            sideList[sideNum] = 3
            sideNum = sideNum + 1
            sideList[sideNum] = 3
            sideNum = sideNum + 1
            sideList[sideNum] = 3
        end
        -- 上方可用
        if viewMax.y < self.cameraCtrl.mapTopBound - mapBound then
            sideNum = sideNum + 1
            sideList[sideNum] = 4
        end
        if sideNum < 1 then
            sideList = {1, 1, 1, 1, 2, 3, 3, 3, 3, 4}
            sideNum = #sideList
        end
        local spawnDir = sideList[math.random(1, sideNum)]
        -- 上方
        if spawnDir == 4 then
            spawnPos = bc_CS_Vector3(math.lerp(viewMin.x, viewMax.x, math.random()), viewMax.y + mapBound, 0)
        elseif spawnDir == 2 then
            -- 下方
            spawnPos = bc_CS_Vector3(math.lerp(viewMin.x, viewMax.x, math.random()), viewMin.y - mapBound, 0)
        elseif spawnDir == 1 then
            -- 左方
            spawnPos = bc_CS_Vector3(viewMin.x - mapBound, math.lerp(viewMin.y, viewMax.y, math.random()), 0)
        else
            -- 右方
            spawnPos = bc_CS_Vector3(viewMax.x + mapBound, math.lerp(viewMin.y, viewMax.y, math.random()), 0)
        end
    end
    spawnPos = self:GetSafetyPosition(spawnPos)
    spawnPos.z = 0
    return spawnPos
end

--- 生产道具对象
function manyknives_scene_mgr:SpawnPropPrefab(num, type, pos)
    local spawnPosFlag = pos == nil
    for i = 1, num do
        local tmpGO = self:PropPoolPopOne(type)
        tmpGO.transform:SetParent(self.levelRoot.transform)
        tmpGO:Init(self, type)
        tmpGO:GetColliderKey().name = ManyKnivesDefine.triggerType.prop .. ManyKnivesDefine.names.split ..
                                          ManyKnivesDefine.names.Prop
        local tmpPos = nil
        -- 随机出现在周围
        if spawnPosFlag then
            tmpPos = self:getPropSpawnPos()
        else
            tmpPos = pos
            tmpPos.x = tmpPos.x + math.lerp(-1, 1, math.random()) * 1.5
            tmpPos.y = tmpPos.y + math.lerp(-1, 1, math.random()) * 1.5
        end
        tmpGO.transform.position = tmpPos
    end
end
function manyknives_scene_mgr:SpawnOneProp(type, pos)
    local tmpGO = self:PropPoolPopOne(type)
    tmpGO.transform:SetParent(self.levelRoot.transform)
    tmpGO:Init(self, type)
    tmpGO:GetColliderKey().name = ManyKnivesDefine.triggerType.prop .. ManyKnivesDefine.names.split ..
                                      ManyKnivesDefine.names.Prop
    pos = self:GetSafetyPosition(pos)
    pos.z = 0
    tmpGO.transform.position = pos
    return tmpGO
end

function manyknives_scene_mgr:getPropSpawnPos()
    local boundMin = 0.05
    local boundMax = 0.3
    -- 1/4屏随机位置
    local rPos = bc_CS_Vector3(math.lerp(boundMin, boundMax, math.random()),
        math.lerp(boundMin, boundMax, math.random()), 0)
    local spawnDir = math.random()
    -- 左下角
    if spawnDir < 0.25 then
    elseif spawnDir < 0.5 then
        -- 左上角
        rPos.y = 1 - rPos.y
    elseif spawnDir < 0.75 then
        -- 右下角
        rPos.x = 1 - rPos.x
    else
        -- 右上角
        rPos.x = 1 - rPos.x
        rPos.y = 1 - rPos.y
    end
    rPos = self.cameraCtrl:ViewportToWorldPoint(rPos)
    rPos.z = 0
    rPos = self:GetSafetyPosition(rPos)
    rPos.z = 0
    return rPos
end
function manyknives_scene_mgr:GetSafetyPosition(pos)
    return self.PathFinder:GetNearest(pos, NNConstraint_Default).position
end

function manyknives_scene_mgr:Ready()
    self.fixedDeltaTime = CS.UnityEngine.Time.fixedDeltaTime
    self.mainScene = bc_CS_GameObject.Instantiate(self.resMgr.mainScenePrefab)
    self.mainScene.transform:SetParent(self.mono.globalMonoGO.transform)
    self.mainScene.transform.localPosition = bc_CS_Vector3.zero
    self.mainScene.transform.localScale = bc_CS_Vector3.one
    local tmp = self.mainScene:GetComponent(typeof(CS.GameLuaBehaviour_New))
    tmp:Awake()
    self.dataSrc = CshapToLuaValue_New(tmp)
    self.mainCamera = self.dataSrc[itemNames.Camera]
    self.cameraParent = self.dataSrc[itemNames.CameraParent]
    self.AS_BGM = self.dataSrc["AS_BGM"]
    self.AS_FX = self.dataSrc["AS_FX"]
    self.AS_FX_Loop_Craze = self.dataSrc["AS_FX_Loop_Craze"]

    self.levelRoot = bc_CS_GameObject.Instantiate(self.resMgr:GetLevelPrefab(self.gameMgr.level))
    self.levelRoot.transform:SetParent(self.mainScene.transform)
    self.levelRoot.transform.localPosition = bc_CS_Vector3.zero
    self.levelRoot.transform.localScale = bc_CS_Vector3.one

    local levelTmp = self.levelRoot:GetComponent(typeof(CS.GameLuaBehaviour_New))
    levelTmp:Awake()
    self.levelDataSrc = CshapToLuaValue_New(levelTmp)
    self.bgSR = self.levelDataSrc["Bg"]
    self.playerPos = self.levelDataSrc["playerPos"]
    self.PathFinder = self.levelDataSrc["PathFinder"]
    self.PathFinder.threadCount = CS.Pathfinding.ThreadCount.AutomaticHighLoad
    self.PathFinder.scanOnStartup = false
    self.PathFinder:Scan()      
    -- 初始化关卡配置
    self:InitLevelConfig()
    -- 初始化敌人配置
    self:InitRoleConfig()
    self:BladePoolReady()
    self:RolePoolReady()
    self:PropPoolReady()
    self:EffectPoolReady()
    self:ResetGame()
end

function manyknives_scene_mgr:InitRoleConfig()
    self.roleConfigTable = {}
    for _, v in pairs(ManyKnivesDefine.roleNames) do
        self.roleConfigTable[v] = {}
        for _, data in ipairs(self.resMgr.config_roles[v]) do
            local id = tonumber(data[1])
            self.roleConfigTable[v][id] = {
                level = id,
                roleName = v,
                hp = tonumber(data[2]),
                bladeType = tonumber(data[3]),
                bladeNum = tonumber(data[4]),
                speed = tonumber(data[5]),
                bodyDmg = tonumber(data[6]),
                bladeDmg = tonumber(data[7]),
                skillDmg = tonumber(data[8]),
                defense = tonumber(data[9]),
                skillType = tonumber(data[11]),
                callEnemyType = tonumber(data[12]),
                callTime = tonumber(data[13]),
                callNum = tonumber(data[14]),
                callLevel = tonumber(data[15]),
                skillLevel = tonumber(data[16])
            }
            self.roleConfigTable[v][id].dropConfig = nil
            if data[10] ~= nil then
                local splitStr = string.bc_split(data[10], ManyKnivesDefine.names.split)
                if #splitStr >= 3 then
                    self.roleConfigTable[v][id].dropConfig = {
                        num = tonumber(splitStr[1]),
                        typeMin = tonumber(splitStr[2]),
                        typeMax = tonumber(splitStr[3])
                    }
                end
            end
        end
    end
    if self.bossFlag then
        local roleName = ManyKnivesDefine.roleTypeWithName[self.curBossType]
        local tmpBossConfig = self:GetRoleCofig(roleName, self.curBossLevel)
        if tmpBossConfig.callEnemyType > 0 and tmpBossConfig.callNum > 0 then
            TinyRush_Table_Add(self.curEnemyTypeList, tmpBossConfig.callEnemyType)
        end
        if roleName == ManyKnivesDefine.roleNames.axeboss then
            local rushConfig = self:GetRushConfigById(tmpBossConfig.skillLevel)
            if rushConfig.retinueNum > 0 then
                -- 分身类型是11
                self.curBossRetinueNum = rushConfig.retinueNum
                TinyRush_Table_Add(self.curEnemyTypeList, 11)
            end
        end
    end
end

function manyknives_scene_mgr:GetRoleCofig(roleName, levelOrId)
    return self.roleConfigTable[roleName][levelOrId]
end

function manyknives_scene_mgr:InitLevelConfig()
    self.bossFlag = false
    self.levelConfigTable = {}
    self.curEnemyTypeList = {}
    self.enemyNumMax = 0
    for id, wave in ipairs(self.resMgr.config_level) do
        self.levelConfigTable[id] = {
            waveId = id,
            time = tonumber(wave[2]),
            propType = tonumber(wave[3]),
            propNum = tonumber(wave[4]),
            enemyType = tonumber(wave[5]),
            enemyNum = tonumber(wave[6]),
            enemyLevel = tonumber(wave[7])
        }
        if self.levelConfigTable[id].enemyType > 0 then
            local tmpEnemyType = self.levelConfigTable[id].enemyType
            self.enemyNumMax = self.enemyNumMax + self.levelConfigTable[id].enemyNum
            if not self.bossFlag and tmpEnemyType > 6 and tmpEnemyType < 10 then
                self.bossFlag = true
                self.bossTimer = self.levelConfigTable[id].time
                self.curBossType = tmpEnemyType
                self.curBossLevel = self.levelConfigTable[id].enemyLevel
            end
            TinyRush_Table_Add(self.curEnemyTypeList, tmpEnemyType)
        end
    end
    local levelBlade = self.resMgr.config_levelBlade[self.gameMgr.level]
    self.autoBladeConfig = {
        initNum = tonumber(levelBlade[2]),
        maxNum = tonumber(levelBlade[3])
    }
end

function manyknives_scene_mgr:GetRushConfigById(id)
    local config = self.resMgr.config_rushBoss[id]
    return {
        -- 冲刺技能是否开启
        rush_Enable = tonumber(config[2]) == 1,
        -- 冲刺技能伤害
        rush_Dmg = tonumber(config[3]),
        -- 技能冷却时间
        skillCD = tonumber(config[4]),
        -- 冲刺次数
        rushCount = tonumber(config[5]),
        -- 冲刺距离
        rush_Distance = tonumber(config[6]),
        -- 冲刺速度
        rush_MoveSpeed = tonumber(config[7]),
        -- 首次冲刺预警时长
        rush_attent_First = tonumber(config[8]),
        -- 冲刺间隔时间,
        rush_interval = tonumber(config[9]),
        -- 冲刺预警时间
        rush_attent = tonumber(config[10]),
        -- 二阶段分身是否开启
        retinue_Enable = tonumber(config[11]) == 1,
        -- 二阶段血量百分比
        transLimit = tonumber(config[12]),
        -- 二阶段几个分身
        retinueNum = tonumber(config[13]),
        -- 分身等级
        retinueLevel = tonumber(config[14])
    }
end
function manyknives_scene_mgr:GetFireConfigById(id)
    local config = self.resMgr.config_fireBoss[id]
    return {
        -- 扇形技能是否开启
        fan_Enable = tonumber(config[2]) == 1,
        fan_fireDmg = tonumber(config[3]),
        -- 扇形火球技能冷却时间
        fan_SkillCD = tonumber(config[4]),
        -- 扇形角度
        fan_Angle = tonumber(config[5]),
        -- 扇形火球数量,
        fan_FireNum = tonumber(config[6]),
        -- 火球距离
        fan_FireDis = tonumber(config[7]),
        -- 扇形预警时间
        fan_Attent = tonumber(config[8]),
        -- 砸地技能释放开启
        fall_Enable = tonumber(config[9]) == 1,
        fall_Dmg = tonumber(config[10]),
        -- 砸地冷却时间
        fall_SkillCD = tonumber(config[11]),
        -- 砸地预警时间
        fall_Attent = tonumber(config[12]),
        -- 砸地范围
        fall_Range = tonumber(config[13]),
        -- 每次技能后摇
        followCD = tonumber(config[14])
    }
end
function manyknives_scene_mgr:GetMiasmaConfigById(id)
    local config = self.resMgr.config_miasmaBoss[id]
    return {
        -- 路径毒气是否开启
        path_Enable = tonumber(config[2]) == 1,
        dmg = tonumber(config[3]),
        -- 路径毒气冷却时间
        path_CD = tonumber(config[4]),
        -- 路径存在时长
        path_ExitTime = tonumber(config[5]),
        -- 范围毒气是否开启,
        range_Enable = tonumber(config[6]) == 1,
        -- 范围毒气冷却时间
        range_CD = tonumber(config[7]),
        -- 选取范围(米)
        range_Range = tonumber(config[8]),
        -- 预警时间
        range_Attent = tonumber(config[9]),
        -- 毒气数量
        range_Num = tonumber(config[10]),
        -- 每个毒气范围(米)
        range_PerRange = tonumber(config[11]),
        -- 范围存在时长
        range_ExitTime = tonumber(config[12])
    }
end

function manyknives_scene_mgr:GetPlayerViewPos()
    return self.mainCamera:WorldToViewportPoint(self.rolePlayer.center.position)
end

function manyknives_scene_mgr:GetPropPool(type)
    if type == nil then
        return false
    end
    local curProp
    if type == 1 then
        curProp = self.propPool.defaultBlade
    elseif type == 2 then
        curProp = self.propPool.iceBlade
    elseif type == 3 then
        curProp = self.propPool.fireBlade
    elseif type == 4 then
        curProp = self.propPool.miasmaBlade
    elseif type == 5 then
        curProp = self.propPool.lightningBlade
    elseif type == 6 then
        curProp = self.propPool.craze
    elseif type == 7 then
        curProp = self.propPool.sword
    elseif type == 8 then
        curProp = self.propPool.fleetfoote
    elseif type == 9 then
        curProp = self.propPool.love
    elseif type == 10 then
        curProp = self.propPool.defaultBlade2
    end
    return curProp
end
function manyknives_scene_mgr:PropPoolPopOne(type)
    local obj = self:GetPropPool(type):popOne()
    self.propPairWithGO[obj:GetColliderKey()] = obj
    return obj
end
function manyknives_scene_mgr:PropPoolPushOne(type, item)
    self.propPairWithGO[item.__entity:GetColliderKey()] = nil
    return self:GetPropPool(type):pushOne(item)
end
function manyknives_scene_mgr:PropPoolReady()
    self.propPoolParent = bc_CS_GameObject("propPoolParent")
    self.propPoolParent:SetActive(false)
    self.propPoolParent.transform:SetParent(self.mainScene.transform)
    self.propPoolParent.transform.localPosition = bc_CS_Vector3.zero
    self.propPoolParent.transform.localScale = bc_CS_Vector3.one
    self.propPool = {}
    self.propPool.defaultBlade = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.defaultBladeProp), "bcmanyknives_propbase_item")
    self.propPool.defaultBlade2 = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.defaultBladeProp2), "bcmanyknives_propbase_item")
    self.propPool.iceBlade = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.iceBladeProp), "bcmanyknives_propbase_item")
    self.propPool.iceBlade:preload(ManyKnivesDefine.propPoolPreloadNum)
    self.propPool.fireBlade = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.fireBladeProp), "bcmanyknives_propbase_item")
    self.propPool.fireBlade:preload(ManyKnivesDefine.propPoolPreloadNum)
    self.propPool.miasmaBlade = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.miasmaBladeProp), "bcmanyknives_propbase_item")
    self.propPool.miasmaBlade:preload(ManyKnivesDefine.propPoolPreloadNum)
    self.propPool.lightningBlade = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.lightningBladeProp), "bcmanyknives_propbase_item")
    self.propPool.lightningBlade:preload(ManyKnivesDefine.propPoolPreloadNum)
    self.propPool.craze = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.crazeProp), "bcmanyknives_propbase_item")
    self.propPool.craze:preload(ManyKnivesDefine.propPoolPreloadNum)
    self.propPool.fleetfoote = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.fleetfootedProp), "bcmanyknives_propbase_item")
    self.propPool.fleetfoote:preload(ManyKnivesDefine.propPoolPreloadNum)
    self.propPool.love = goPool.new(self.propPoolParent, self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.loveProp),
        "bcmanyknives_propbase_item")
    self.propPool.love:preload(ManyKnivesDefine.propPoolPreloadNum)
    self.propPool.sword = goPool.new(self.propPoolParent,
        self.resMgr:GetPropPrefab(ManyKnivesDefine.propNames.swordProp), "bcmanyknives_propbase_item")
    self.propPool.sword:preload(ManyKnivesDefine.propPoolPreloadNum)
end

function manyknives_scene_mgr:EffectPoolReady()
    self.effectPool = {}
    self.effectPool.fx_bingzhangjineng = goPool.new(self.propPoolParent, self.resMgr:GetSkillPrefab(
        ManyKnivesDefine.skillNames.fx_bingzhangjineng), "bcmanyknives_effectbase_item")
    self.effectPool.fx_bingzhangjineng:preload(3)
    self.effectPool.fx_snow01 = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_snow01), "bcmanyknives_effectbase_item")
    self.effectPool.fx_snow01:preload(8)
    self.effectPool.fx_fire01 = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_fire01), "bcmanyknives_effectbase_item")
    self.effectPool.fx_fire01:preload(10)
    self.effectPool.fx_pindao = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_pindao), "bcmanyknives_effect_particle_item")
    self.effectPool.fx_pindao:preload(5)
    self.effectPool.fx_dimian = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_dimian), "bcmanyknives_effect_particle_item")
    self.effectPool.fx_dimian:preload(1)
    self.effectPool.fx_lightning01 = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_lightning01), "bcmanyknives_effectbase_item")
    self.effectPool.fx_lightning01:preload(3)
    self.effectPool.fx_miasma = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_miasma), "bcmanyknives_effectbase_item")
    self.effectPool.fx_miasma:preload(10)
    self.effectPool.fx_miasma_boss = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_miasma_boss), "bcmanyknives_effectbase_item")
    self.effectPool.fx_miasma_boss:preload(10)
    self.effectPool.fx_juese_shouji = goPool.new(self.propPoolParent, self.resMgr:GetSkillPrefab(
        ManyKnivesDefine.skillNames.fx_juese_shouji), "bcmanyknives_effect_particle_item")
    self.effectPool.fx_juese_shouji:preload(10)
    self.effectPool.fx_fire_boss = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fx_fire_boss), "bcmanyknives_effectbase_item")
    self.effectPool.fx_fire_boss:preload(10)
    self.effectPool.rushGuide = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.rushGuide), "bcmanyknives_spriter_render_item")
    self.effectPool.rushGuide:preload(3)
    self.effectPool.fanGuide = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.fanGuide), "bcmanyknives_spriter_render_item")
    self.effectPool.fanGuide:preload(1)
    self.effectPool.circleGuide = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.circleGuide), "bcmanyknives_spriter_render_item")
    self.effectPool.circleGuide:preload(1)
    self.effectPool.circleDmg = goPool.new(self.propPoolParent,
        self.resMgr:GetSkillPrefab(ManyKnivesDefine.skillNames.circleDmg), "bcmanyknives_effectbase_item")
    self.effectPool.circleDmg:preload(1)
    -- self.effectPool.fx_xiaoguaisiwang = goPool.new(self.propPoolParent, self.resMgr:GetSkillPrefab(
    --     ManyKnivesDefine.skillNames.fx_xiaoguaisiwang), "bcmanyknives_effect_particle_item")
    -- self.effectPool.fx_xiaoguaisiwang:preload(10)
end

function manyknives_scene_mgr:PopEffect(name)
    local obj = self.effectPool[name]:popOne()
    self.effectPairWithGO[obj:GetColliderKey()] = obj
    return obj
end
function manyknives_scene_mgr:PushEffect(name, item)
    self.effectPairWithGO[item.__entity:GetColliderKey()] = nil
    self.effectPool[name]:pushOne(item)
end

function manyknives_scene_mgr:GetBladePool(type)
    if type == nil then
        return false
    end
    local curBlade
    if type == 1 then
        curBlade = self.bladePool.default
    elseif type == 2 then
        curBlade = self.bladePool.snow
    elseif type == 3 then
        curBlade = self.bladePool.fire
    elseif type == 4 then
        curBlade = self.bladePool.miasma
    elseif type == 5 then
        curBlade = self.bladePool.lightning
    elseif type == 6 then
        curBlade = self.bladePool.ironblade
    elseif type == 7 then
        curBlade = self.bladePool.hugeAxe
    elseif type == 8 then
        curBlade = self.bladePool.hugeBlade
    end
    return curBlade
end
function manyknives_scene_mgr:BladePoolPopOne(type)
    local obj = self:GetBladePool(type):popOne()
    obj:GetColliderKey().name = ManyKnivesDefine.triggerType.blade .. ManyKnivesDefine.names.split ..
                                    ManyKnivesDefine.names.Blade
    self.bladePairWithGO[obj:GetColliderKey()] = obj
    return obj
end
function manyknives_scene_mgr:BladePoolPushOne(type, item)
    self.bladePairWithGO[item.__entity:GetColliderKey()] = nil
    return self:GetBladePool(type):pushOne(item)
end

function manyknives_scene_mgr:BladePoolReady()
    self.bladePoolParent = bc_CS_GameObject("bladePoolParent")
    self.bladePoolParent:SetActive(false)
    self.bladePoolParent.transform:SetParent(self.mainScene.transform)
    self.bladePoolParent.transform.localPosition = bc_CS_Vector3.zero
    self.bladePoolParent.transform.localScale = bc_CS_Vector3.one
    self.bladePool = {}
    -- 玩家的刀刃
    self.bladePool.default = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.defaultblade), "bcmanyknives_bladebase_item")
    self.bladePool.default:preload(ManyKnivesDefine.bladePoolPreloadNum + 50)
    self.bladePool.lightning = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.lightningblade), "bcmanyknives_bladebase_item")
    self.bladePool.lightning:preload(ManyKnivesDefine.bladePoolPreloadNum)
    self.bladePool.miasma = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.miasmablade), "bcmanyknives_bladebase_item")
    self.bladePool.miasma:preload(ManyKnivesDefine.bladePoolPreloadNum)
    self.bladePool.snow = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.snowblade), "bcmanyknives_bladebase_item")
    self.bladePool.snow:preload(ManyKnivesDefine.bladePoolPreloadNum)
    self.bladePool.fire = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.fireblade), "bcmanyknives_bladebase_item")
    self.bladePool.fire:preload(ManyKnivesDefine.bladePoolPreloadNum)
    self.bladePool.ironblade = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.ironblade), "bcmanyknives_bladebase_item")
    self.bladePool.ironblade:preload(ManyKnivesDefine.bladePoolPreloadNum)
    self.bladePool.hugeAxe = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.hugeAxe), "bcmanyknives_bladebase_item")
    self.bladePool.hugeAxe:preload(ManyKnivesDefine.bladePoolPreloadNum)
    self.bladePool.hugeBlade = goPool.new(self.bladePoolParent,
        self.resMgr:GetBladePrefab(ManyKnivesDefine.bladeNames.hugeblade), "bcmanyknives_bladebase_item")
    self.bladePool.hugeBlade:preload(ManyKnivesDefine.bladePoolPreloadNum)
end

function manyknives_scene_mgr:RolePoolPopOne(roleName)
    local obj = self.rolePool[roleName]:popOne()
    self.rolePairWithGO[obj:GetColliderKey()] = obj
    if self.gameMgr.level == 1 then
        if not self.tutMgr.lv1Flag and roleName == ManyKnivesDefine.roleNames.onion then
            self.tutMgr:SetParams(obj)
        end
    elseif self.gameMgr.level == 3 then
        if not self.tutMgr.lv3Flag and roleName == ManyKnivesDefine.roleNames.dafeilong then
            self.tutMgr:SetParams(obj)
        end
    elseif self.gameMgr.level == 9 then
        if not self.tutMgr.lv9Flag and roleName == ManyKnivesDefine.roleNames.bingmodaoshi then
            self.tutMgr:SetParams(obj)
        end
    end
    return obj
end

function manyknives_scene_mgr:KillCGTween()
    if self.cgTween ~= nil then
        self.cgTween:Kill()
        self.cgTween = nil
    end
end
function manyknives_scene_mgr:EnemyDie(role)
    local recycleFlag = true
    if not role.isPlayer then
        self.enemyNumBind:setValue(self.enemyNumBind.value + 1)
    end
    if not self.overFlag and
        (role.isPlayer or role.isBoss or (not self.bossFlag and self.enemyNumBind.value >= self.enemyNumMax)) then
        self.overFlag = true
        self.readyFlag = false
        if role.isPlayer then
            recycleFlag = false
        end
        if role.isPlayer then
            self.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.shibai)
        else
            self.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.shengli)
        end
        self:KillCGTween()
        self.cgTween = ManyKnivesDefine.DOTween.Sequence()
        self.cgTween:InsertCallback(0, function()
            ManyKnivesDefine.UnityTime.timeScale = 0.1
        end)
        self.cgTween:InsertCallback(0.2, function()
            ManyKnivesDefine.UnityTime.timeScale = 1
        end)
        self.cameraCtrl:FocusPos(true, role.transform, true)
        self.cameraCtrl:Shake_BossDie()
        self.cgTween:InsertCallback(1.1, function()
            if role.isPlayer then
                self.gameMgr:gameFail()
            else
                self.gameMgr:gameWin()
            end
        end)
        self:RefreshPause()
    end
    return recycleFlag
end

function manyknives_scene_mgr:RolePoolPushOne(roleName, item)
    self.rolePairWithGO[item.__entity:GetColliderKey()] = nil
    self.rolePool[roleName]:pushOne(item)
end

function manyknives_scene_mgr:RolePoolReady()
    self.rolePoolParent = bc_CS_GameObject("rolePoolParent")
    self.rolePoolParent:SetActive(false)
    self.rolePoolParent.transform:SetParent(self.mainScene.transform)
    self.rolePoolParent.transform.localPosition = bc_CS_Vector3.zero
    self.rolePoolParent.transform.localScale = bc_CS_Vector3.one
    self.rolePool = {}
    for _, v in pairs(self.curEnemyTypeList) do
        local enemyName = ManyKnivesDefine.roleTypeWithName[v]
        self.rolePool[enemyName] = goPool.new(self.rolePoolParent, self.resMgr:GetRolePrefab(enemyName),
            ManyKnivesDefine.roleTypeWithPoolFile[v])
        -- boss只需要预载一个
        if v > 6 and v < 10 then
            self.rolePool[enemyName]:preload(1)
        elseif v == 11 then
            self.rolePool[enemyName]:preload(self.curBossRetinueNum)
        else
            self.rolePool[enemyName]:preload(ManyKnivesDefine.enemyPoolPreloadNum)
        end
    end
end

function manyknives_scene_mgr:StartBattle()
    -- 开始出怪
    self.levelWaveMax = #self.levelConfigTable
    self.levelWaveBind:setValue(1)
    self.spawnTimer = 0
    self.rolePlayer:Start()
    self.startFlag = true
    self.overFlag = false
    self.tutMgr:GameStart()
end

function manyknives_scene_mgr:OverBattle()
    self.pauseBind:setValue(true)
    self.startFlag = false
    self.overFlag = true
    if self.rolePairWithGO ~= nil then
        for _, v in pairs(self.rolePairWithGO) do
            v:Stop()
        end
    end
end

--- 重置游戏状态
function manyknives_scene_mgr:ResetGame()
    self:KillCGTween()
    self.pauseBind:setValue(false)
    -- 清空对象池
    for _, v in pairs(self.rolePool) do
        v:clear()
    end
    for _, v in pairs(self.bladePool) do
        v:clear()
    end
    for _, v in pairs(self.propPool) do
        v:clear()
    end
    for _, v in pairs(self.effectPool) do
        v:clear()
    end
    self:OverBattle()
    self.rolePairWithGO = {}
    self.propPairWithGO = {}
    self.effectPairWithGO = {}
    self.bladePairWithGO = {}
    if self.rolePlayer == nil then
        -- 初始化主角
        self.rolePlayer = require("bcmanyknives_role_player").new(bc_CS_GameObject.Instantiate(
            self.resMgr:GetRolePrefab(ManyKnivesDefine.roleNames.player)))
        self.rolePlayer.transform:SetParent(self.levelRoot.transform)
        self.rolePlayer.transform.localScale = bc_CS_Vector3.one
        self.rolePlayer:GetColliderKey().name = ManyKnivesDefine.triggerType.role .. ManyKnivesDefine.names.split ..
                                                    ManyKnivesDefine.names.Player
    end
    if self.cameraCtrl == nil then
        -- 初始化相机
        self.cameraCtrl = require("bcmanyknives_camera_ctrl").new(self.lifeScope, self.mainCamera, self.cameraParent,
            self.bgSR)
        self.cameraCtrl:Init(self.rolePlayer)
    end
    local playerPos = self:GetSafetyPosition(self.playerPos.position)
    playerPos.z = 0
    self.rolePlayer:Init(self, self.uiMgr, ManyKnivesDefine.roleNames.player,
        self:GetRoleCofig(ManyKnivesDefine.roleNames.player, self.gameMgr.level), playerPos)
    self.rolePairWithGO[self.rolePlayer:GetColliderKey()] = self.rolePlayer
    self.cameraCtrl:Reset()
    if self.bossFlag then
        self.bossTimerBind:setValue(self.bossTimer)
    else
        self.enemyNumBind:setValue(0)
    end
    self.readyFlag = true
    self.NoInjury = false
    ManyKnivesDefine.UnityTime.timeScale = 1
    self.bladeCount = 0
    self.spawnBladeTimer = 0
    self.autoDropBladeList = {}
    self:InitSpawnBlade()
end

return manyknives_scene_mgr
