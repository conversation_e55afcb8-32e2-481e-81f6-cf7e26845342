---@class kingshot_mgr_level : fusion_mgrbase
---@field lifeScope kingshot_lifescope
local mgr = bc_Class("kingshot_mgr_level", Fusion.MgrBase)
---@type kingshot_scene_mgr
mgr.sceneMgr = nil
---@type fusion_mono
mgr.mono = nil
---@type kingshot_ui_mgr
mgr.uiMgr = nil
---@type kingshot_res_mgr
mgr.resMgr = nil
---@type kingshot_level
mgr.curLevel = nil
mgr.miniLvConfig = nil

local cysoldierssortie_comp_level_dynamic_challenge = require("cysoldierssortie_comp_level_dynamic_challenge")

function mgr:__init(...)
    self.miniLvConfig = ...
end

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.sceneMgr = self.lifeScope:GetMgr("kingshot_scene_mgr")
    self.mono = self.lifeScope:GetMgr("fusion_mono")
    self.uiMgr = self.lifeScope:GetMgr("kingshot_ui_mgr")
    self.resMgr = self.lifeScope:GetMgr("kingshot_res_mgr")
end

function mgr:Ready()
    self.curLevel = require("kingshot_level").New(self, self.miniLvConfig, self.uiMgr)
    self.curLevel:SetPlayerLua(self.sceneMgr.playerCtrl)
    KingShot_Define.minigame_mgr.SetLevelScr(self.curLevel)
    local total = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    total.curLevel = self.curLevel
end

function mgr:GameStart()
    cysoldierssortie_OpenMusicController(self.miniLvConfig.Bgm)
end

--敌人受伤，队伍解散
function mgr:EnemyBeHit(character)
    self.sceneMgr.teamCtrl:EnemyBeHit(character)
end

function mgr:GetAttackLimit(attack)
    return cysoldierssortie_comp_level_dynamic_challenge:GetAttackLimit(attack)
end

function mgr:GetHpLimit(attack)
    return cysoldierssortie_comp_level_dynamic_challenge:GetHpLimit(attack)
end

function mgr:SetLevelChallengeDebuff(debuff)
    return cysoldierssortie_comp_level_dynamic_challenge:SetLevelChallengeDebuff(debuff)
end

function mgr:SetCurLevelFailFlag()
    return cysoldierssortie_comp_level_dynamic_challenge:SetCurLevelFailFlag()
end

return mgr
