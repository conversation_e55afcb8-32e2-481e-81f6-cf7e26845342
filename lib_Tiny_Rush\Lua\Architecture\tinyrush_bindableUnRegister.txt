---@class tinyrush_bindableUnRegister : TRClass @数据监听的注销对象
local bindableUnRegister = TinyRush_CreateClass("tinyrush_bindableUnRegister")
bindableUnRegister.bindable = nil
bindableUnRegister.onValueChanged = nil

function bindableUnRegister:ctor(...)
    self.bindable, self.onValueChanged = ...
end

function bindableUnRegister:unRegister()
    if self.bindable ~= nil then
        self.bindable:unRegister(self.onValueChanged)
    end
    self.bindable = nil
    self.onValueChanged = nil
end

return bindableUnRegister
