local manyknives_game_mgr = TinyRush_CreateClass("bcmanyknives_game_mgr"):baseClass(require("tinyrush_game_mgr"))
    :interface(TinyRush_IInit)

manyknives_game_mgr.resMgr = nil
manyknives_game_mgr.mono = nil
manyknives_game_mgr.uiMgr = nil
manyknives_game_mgr.sceneMgr = nil
manyknives_game_mgr.audioMgr = nil
manyknives_game_mgr.tutMgr = nil
manyknives_game_mgr.langMgr = nil

manyknives_game_mgr.level = nil

function manyknives_game_mgr:rush_OnInit()
    self.resMgr = self.lifeScope:get("bcmanyknives_res_mgr")
    self.mono = self.lifeScope:get("tinyrush_mono")
    self.uiMgr = self.lifeScope:get("bcmanyknives_ui_mgr")
    self.sceneMgr = self.lifeScope:get("bcmanyknives_scene_mgr")
    self.audioMgr = self.lifeScope:get("bcmanyknives_audio_mgr")
    self.tutMgr = self.lifeScope:get("bcmanyknives_tutorial_mgr")
    self.langMgr = self.lifeScope:get("bcmanyknives_lang_mgr")
    self.level = self.entrance.level
end

function manyknives_game_mgr:dispose()
    self.__base:dispose()
end

--- 游戏逻辑：通关
function manyknives_game_mgr:gameWin()
    self.__base:gameWin()
    self.sceneMgr:OverBattle()
end
--- 游戏逻辑：失败
function manyknives_game_mgr:gameFail()
    self.__base:gameFail()
    self.sceneMgr:OverBattle()
    self.uiMgr:OverBattle()
    self.tutMgr:OverBattle()
    self.audioMgr:OverBattle()
end

function manyknives_game_mgr:GameReady()
    self.langMgr:Ready()
    self.sceneMgr:Ready()
    self.uiMgr:Ready()
    self.audioMgr:Ready()
    self.tutMgr:Ready()
end

function manyknives_game_mgr:StartBattle()
    self.sceneMgr:StartBattle()
    self.uiMgr:StartBattle()
    self.audioMgr:StartBattle()
end

function manyknives_game_mgr:GameReset()
    self.sceneMgr:ResetGame()
    self.uiMgr:ResetGame()
    self.audioMgr:ResetGame()
    self.tutMgr:ResetGame()
end
-- 用户主动点击退出，执行失败逻辑
function manyknives_game_mgr:GameFailByUser()
    self.sceneMgr:OverBattle()
    self.uiMgr:OverBattle()
    self.tutMgr:OverBattle()
end

return manyknives_game_mgr
