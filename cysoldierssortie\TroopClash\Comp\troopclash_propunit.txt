---@class troopclash_propunit : fusion_gopoolitem
local unit = bc_Class("troopclash_propunit", require("fusion_gopoolitem"))

---@type troopclash_scene_mgr
unit.sceneMgr = nil
---@type troopclash_prop
unit.Ctrl = nil
unit.DataSrc = nil
---@type troopclash_PropConfig
unit.config = nil
---@type troopclash_propitem
unit.item = nil
---@type troopclash_propfx_item
unit.fxItem = nil
---@type boolean 存活标记
unit.AliveFlag = nil

unit.tween_Drop = nil

local pathType = TroopClash_Define.TweenPathType.CatmullRom
local dropSpeed = 10
local ease = TroopClash_Define.TweenEase.Linear

function unit:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
    self.DataSrc.ColListener:RegisterTriggerEnter(function(collider)
        self:OnTriggerEnter(collider)
    end)
end

function unit:OnTriggerEnter(collider)
    if not self.AliveFlag then
        return
    end
    local go = collider.gameObject
    if go.layer == cysoldierssortie_LayerName.Player and go.tag == cysoldierssortie_TagName.Player then
        self.Ctrl:PickUpProp(self)
    end
end

function unit:EnableCollider(flag)
    self.DataSrc.Collider.enabled = flag
end

function unit:Init(sceneMgr, ctrl, config)
    self.AliveFlag = true
    self.sceneMgr = sceneMgr
    self.Ctrl = ctrl
    self.config = config
    self.gameObject:SetActive(true)
    self.item = self.Ctrl:PopOnePropItem()
    self.item.transform:SetParent(self.transform)
    TroopClash_Define.SetTransformLocalPositionAndLocalRotation(self.item.transform, 0, 0, 0, 0, 0, 0)
    self.item:Init(self.config)
end

function unit:ShowImmediately(pos)
    TroopClash_Define.SetTransformPositionXYZ(self.transform, pos.x, pos.y, pos.z)
    TroopClash_Define.SetTransformLocalScale(self.transform, 1, 1, 1)
    self:SpawnPropFX()
    self.fxItem:Play(true, true)
    self:EnableCollider(true)
end

function unit:SpawnPropFX()
    self.fxItem = self.Ctrl:PopOnePropFX(self.config.Level)
    self.fxItem.transform:SetParent(self.transform)
    TroopClash_Define.SetTransformLocalPositionAndLocalRotation(self.fxItem.transform, 0, 0, 0, 0, 0, 0)
end

---开始播放特效
---@param reset boolean 是否重置特效
function unit:DropPlay(oriPos, endPos)
    self:EnableCollider(false)
    self:SpawnPropFX()
    self.fxItem:Play(false, true)
    TroopClash_Define.SetTransformPositionXYZ(self.transform, oriPos.x, oriPos.y, oriPos.z)
    TroopClash_Define.SetTransformLocalScale(self.transform, 0.1, 0.1, 0.1)
    local newPath, moveLength = TroopClash_Define.Func_GetPathByCurve(oriPos, endPos, self.Ctrl.DropPath,
        self.Ctrl.DropPathCount)
    local moveDur = moveLength / dropSpeed
    self.tween_Drop = TroopClash_Define.DOTween.Sequence()
    self.tween_Drop:Append(self.transform:DOScale(TroopClash_Define.CacheVector3.One, moveDur):SetEase(ease))
    self.tween_Drop:Insert(0, self.transform:DOPath(newPath, moveDur, pathType):SetEase(ease))
    self.tween_Drop:OnComplete(function()
        self.fxItem:Play(true, true)
        self:EnableCollider(true)
        self.Ctrl:ShowBoomFX(endPos)
    end)
end

function unit:KillTween()
    if self.tween_Drop ~= nil then
        self.tween_Drop:Kill()
        self.tween_Drop = nil
    end
end

function unit:Recycle()
    self.AliveFlag = false
    self:KillTween()
    if self.item ~= nil then
        self.Ctrl:PushOnePropItem(self.item)
        self.item = nil
    end
    if self.fxItem ~= nil then
        self.Ctrl:PushOnePropFX(self.config.Level, self.fxItem)
        self.fxItem = nil
    end
end

function unit:__delete()
    self.AliveFlag = false
    self:KillTween()
    self:Dispose()
end

return unit
