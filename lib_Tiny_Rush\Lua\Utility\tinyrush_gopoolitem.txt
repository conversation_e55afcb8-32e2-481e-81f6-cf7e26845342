---@class tinyrush_gopoolitem : TRClass
local poolItem = TinyRush_CreateClass("tinyrush_gopoolitem")
poolItem.gameObject = nil
poolItem.transform = nil

function poolItem:ctor(go, ...)
    self.gameObject = go
    self.transform = self.gameObject.transform
end

function poolItem:recycle()

end

function poolItem:dispose()
    bc_CS_GameObject.Destroy(self.gameObject)
    self.gameObject = nil
    self.transform = nil
end
return poolItem
