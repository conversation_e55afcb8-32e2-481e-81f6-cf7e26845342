local cysoldierssortie_comp_character_entity = bc_Class("cysoldierssortie_comp_character_entity") --类名用小游戏名加后缀保证全局唯一
local gw_hero_mgr    = require "gw_hero_mgr"
local game_scheme 	        = require "game_scheme"
local Transform = CS.UnityEngine.Transform
local Vector3 = CS.UnityEngine.Vector3
local UnityEngine = CS.UnityEngine
local typeof = typeof
local Animator     = CS.UnityEngine.Animator
local Quaternion = CS.UnityEngine.Quaternion
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local math = math
local LayerMask           = CS.UnityEngine.LayerMask
local SkinnedMeshRenderer = CS.UnityEngine.SkinnedMeshRenderer
local MeshRenderer = CS.UnityEngine.MeshRenderer
local table = table
local AnimatorCullingMode = CS.UnityEngine.AnimatorCullingMode
local log = log
local tostring = tostring
local NavMeshAgent = CS.UnityEngine.AI.NavMeshAgent
local ObstacleAvoidanceType = CS.UnityEngine.AI.ObstacleAvoidanceType
local gw_sand_animator_helper = require "gw_sand_animator_helper"
local troopclash_animator = require "troopclash_animator"
local kingshot_animator = require "kingshot_animator"

local GCPerf = GCPerf
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local SetDestinationXYZ = ApiHelper.SetDestinationXYZ
local IsActiveNavAgent = ApiHelper.IsActiveNavAgent
local CSUpdateEvent = cysoldierssortie_EventName.CSUpdate
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_KillTimer = cysoldierssortie_KillTimer
local bc_IsNotNull = bc_IsNotNull
local bc_CS_Quaternion = bc_CS_Quaternion
local bc_CS_Vector3 = bc_CS_Vector3
local cysoldierssortie_unit_target_layer_str = cysoldierssortie_unit_target_layer_str
local xpcall = xpcall
local debug = debug
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local bc_Time = bc_Time
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local cysoldierssortie_CommonEffect = cysoldierssortie_CommonEffect
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local util = require "util"
local BoxCollider = CS.UnityEngine.BoxCollider
local SetLuaCompCache = SetLuaCompCache

local NewGpuAnimator = util.IsCSharpClass(CS.GPUAnimationBaker.Engine.ShaderIDs)

function cysoldierssortie_comp_character_entity.__init(self,parent)
    if not bc_IsNotNull(self.gameObject) then
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if poolMgr then
            poolMgr:CreateEntity(cysoldierssortie_PoolObjectName.Hero,parent,
                    function(character_go)
                        self.transform = poolMgr:GetTransform(character_go)
                        self.gameObject = character_go
                        self.transform.localRotation = Quaternion.identity
                        self._rfNumPos = poolMgr:GetRfNumPos(character_go)
                        self._collider = poolMgr:GetCollider(character_go)
                        self._releaseSkillPoint = poolMgr:GetReleaseSkillPoint(character_go)
                        self._hpPoint = poolMgr:GetHpPoint(character_go)
                        self._modelRoot = poolMgr:GetModelRootGo(character_go)
                        SetLuaCompCache(character_go,self)
                    end)
        end
    else
        self.gameObject:SetActive(true)
        ApiHelper.SetParent(self.transform,parent)
        self.transform.localRotation = Quaternion.identity
    end

    self.eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    self.eventMgr:RegisterEvt(self,CSUpdateEvent)
end

function cysoldierssortie_comp_character_entity:OnPopFormPool(parent)
    self.__init(self,parent)
end

function cysoldierssortie_comp_character_entity:OnPushIntoPool()
    self.__delete(self)
end

function  cysoldierssortie_comp_character_entity.__delete(self)
    if self._recycle_coroutine then
        cysoldierssortie_KillTimer(self._recycle_coroutine)
        self._recycle_coroutine = nil
    end
    if bc_IsNotNull(self.gameObject) then
        self.gameObject:SetActive(false)
    end
    self.eventMgr:UnRegisterEvt(self,CSUpdateEvent)
end
-- lua脚本正式开始

function cysoldierssortie_comp_character_entity:SetCharacter(character)
    self.character = character
    self._targetLayerMask =  LayerMask.GetMask(cysoldierssortie_unit_target_layer_str[self.character._unit_type])
    self:InitLayer()
    if bc_IsNotNull(self._releaseSkillPoint) then
        self._releaseSkillPoint.transform.localRotation = bc_CS_Quaternion.Euler(0,0,0)
    end
    self._pause_rotation = true
end

function cysoldierssortie_comp_character_entity:InitLayer()
    self._layer = cysoldierssortie_unit_layer[self.character._unit_type]
    self.gameObject.layer = self._layer
    if not self.character._player then
        self.gameObject.tag =  cysoldierssortie_TagName.Enemy_zombie
        self._cull = true
    else
        self.gameObject.tag = cysoldierssortie_TagName.Player
        self._cull = false
    end
end

local EffectParamCache = {}
function cysoldierssortie_comp_character_entity:PlayNewGetEffect()
    if self.character._unit_type == cysoldierssortie_unit_type.Soldier then
        local effect_path = cysoldierssortie_CommonEffect.UpgradeEffect
        local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
        EffectParamCache.auto_release = true
        EffectParamCache.delay_release = 1
        EffectParamCache.effect_path = effect_path
        EffectParamCache.callBack = function(go)
            go.transform.position = self.character.transform.position
        end
        EffectParamCache.maxWeightLimit = true
        effect_mgr:CreateEffect(EffectParamCache)
    end
end

function cysoldierssortie_comp_character_entity:CalBoundsSize(bounds)
    local tmp_size = bounds.size
    if tmp_size.x > tmp_size.z then
        if tmp_size.x > tmp_size.y then
            local temp = tmp_size.y
            tmp_size.y = tmp_size.x
            tmp_size.x = temp
        end
    else
        if tmp_size.z > tmp_size.y then
            local temp = tmp_size.y
            tmp_size.y = tmp_size.z
            tmp_size.z = temp
        end
    end

    return tmp_size
end

function cysoldierssortie_comp_character_entity:InitBoundsData(renderers)
        for i=0,renderers.Length-1 do
            if  i==0 and not renderers[i]:IsNull() then
                local bound_size = self:CalBoundsSize(renderers[i].bounds)
                local maxHeight = bound_size.y
                local hp_pos = self._hpPoint.transform.localPosition
                if self.character._unit_type == cysoldierssortie_unit_type.Hero then
                    self._hpPoint.transform.localPosition = {x=hp_pos.x,y=3,z=hp_pos.z}
                else
                    self._hpPoint.transform.localPosition = {x=hp_pos.x,y= maxHeight+0.5,z=hp_pos.z}
                end
                self._releaseSkillPoint.transform.localPosition = {x = 0,y = maxHeight/3, z = 0}
                if self._collider then
                    self._collider.size = bound_size
                    self._collider.center = {x=0, y = self._collider.size.y *0.5,z=0}
                end
            end
            renderers[i].shadowCastingMode =  UnityEngine.Rendering.ShadowCastingMode.Off
            if not NewGpuAnimator or not self._gpu_anim then
                renderers[i].gameObject.layer = self._layer
            end
        end
end

function cysoldierssortie_comp_character_entity:SetRenderPropertiesFloat(property,value)
    if self._animator and self._gpu_anim then
        self._animator:SetRenderPropertiesFloat(property,value)
    end
end

function cysoldierssortie_comp_character_entity:SetMaterialFloat(id,value)
    if self._animator  and self._gpu_anim then
        self._animator:SetMaterialFloat(id,value)
    end
end

function cysoldierssortie_comp_character_entity:SetRenderPropertiesColor(property,value)
    if self._animator and self._gpu_anim  then
        self._animator:SetRenderPropertiesColor(property,value)
    end
end

function cysoldierssortie_comp_character_entity:CreateModel()
    if self._modelRoot then
        self._modelRoot.transform.localRotation = bc_CS_Quaternion.Euler(0,0,0)
    end
    if self.character._isDrone then
        return
    end
    if self.character._isBuilding then
        return
    end
    local heroId = self.character._heroId
    local star = self.character._star
    local moudleId = gw_hero_mgr.ChangeHeroModel(heroId,star)
    local moduleCfg = game_scheme:Modul_0(moudleId)
    if not moduleCfg then
        log.Error("[Mini Game SoldierSsortie] UniId = "..tostring(self.character._unitID).." model path not found!!!!!!")
        return
    end
    local modelPath = moduleCfg.modelPath
    modelPath = modelPath:gsub("%.prefab","_simple.prefab")
    local pool_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local loadCallBack = function(gameObject)
        self.modelGo = gameObject
        
        self.modelGo.transform.localRotation = Quaternion.identity
        self.modelGo.transform.localPosition = Vector3.zero
        self.modelGo.transform.localScale = {x= self.character._scale,y =  self.character._scale, z = self.character._scale}
        
        self._animator = gameObject:GetComponent(typeof(Animator))
        if self._animator and not self._animator:IsNull() then
            if cysoldierssortie_TroopClash then
                self._animator = troopclash_animator.New(self.modelGo, self._animator, self.character)
            end
            if cysoldierssortie_KingShot then
                self._animator = kingshot_animator.New(self.modelGo, self._animator, self.character)
            end
            self._animator.cullingMode = AnimatorCullingMode.CullCompletely;
            self._gpu_anim = false
        else
            self._animator = gw_sand_animator_helper.new()
            self._animator:AddAnimator(self.modelGo)
            self._gpu_anim = true
        end

        if self._gpu_anim then
            if NewGpuAnimator then
                self._animator:SetLayer(self._layer)
            end
        end
        
        local skinMeshRenderers = self.modelGo:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
        if  skinMeshRenderers and  skinMeshRenderers.Length > 0 then
            self:InitBoundsData(skinMeshRenderers)
        else
             skinMeshRenderers = self.modelGo:GetComponentsInChildren(typeof(MeshRenderer))
            if skinMeshRenderers and  skinMeshRenderers.Length > 0 then
                self:InitBoundsData(skinMeshRenderers)
            end
        end
        self._modelRoot.layer = self._layer
        if not self.character._player then
            if self._cull or not self.character._view_actor then
                self.modelGo:SetActive(false)
            end
            --self._cull = true
        end
        self.character:InitAnimState()
        self.character:SpawnHp()
    end
    
    pool_mgr:CreateEntity(modelPath,self._modelRoot.transform,loadCallBack)
end

function cysoldierssortie_comp_character_entity:CreateNavMeshAgent(isBuffCreate)
    if not isBuffCreate then
        local minigame_buff_mgr= require "minigame_buff_mgr"
        if  minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.IgnoringTheTerrain,self.character) then
            return
        end
    end

    self._navMeshAgent = self.gameObject:GetComponent(typeof(NavMeshAgent))
    if  not self._navMeshAgent or self._navMeshAgent:IsNull() then 
        self._navMeshAgent = self.gameObject:AddComponent(typeof(NavMeshAgent))
    else
        self._navMeshAgent.enabled = true
    end
    self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
    self._navMeshAgent.acceleration = 1000
    self._navMeshAgent.stoppingDistance = 0--self.character._attackRange/2
    self._navMeshAgent.angularSpeed = 360
    self._crowed = false
end

function cysoldierssortie_comp_character_entity:SwitchObstacleAvoidState(Open)
    if  not self._navMeshAgent or self._navMeshAgent:IsNull() then
        return
    end
    
    if Open then
        self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
    else
        self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
    end
end

function cysoldierssortie_comp_character_entity:InitNavAgentData()
    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
        return
    end

    if not self._navMeshAgent.isOnNavMesh  or not self._navMeshAgent.isActiveAndEnabled then
        return
    end

    self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
    if self._collider then
        local minSize = self._collider.size.z
        if minSize > self._collider.size.x then
            minSize = self._collider.size.x
        end
        self._navMeshAgent.radius = minSize*0.5
        self._navMeshAgent.height = self._collider.size.y
    else
        self._navMeshAgent.radius = 0.5
    end
    self._crowed = true
end

function cysoldierssortie_comp_character_entity:RemoveNavMeshAgent()
    if  not self._navMeshAgent or self._navMeshAgent:IsNull() then
        return
    end
    self._navMeshAgent.enabled = false
end

function cysoldierssortie_comp_character_entity:UpdateNavAgentProp(speed)
    if not self._navMeshAgent or  self._navMeshAgent:IsNull() then
        return
    end

    if not self._navMeshAgent.isOnNavMesh  or not self._navMeshAgent.isActiveAndEnabled then
        return
    end
    self._navMeshAgent.speed = speed
end

function cysoldierssortie_comp_character_entity:IsActiveNavAgent()
    if not self._navMeshAgent then
        return false
    end
    return IsActiveNavAgent(self._navMeshAgent)
end

function cysoldierssortie_comp_character_entity:SetDestination(position)
    if not self._navMeshAgent or  self._navMeshAgent:IsNull() then
        return
    end
    if not self._navMeshAgent.isOnNavMesh  or not self._navMeshAgent.isActiveAndEnabled then
        return 
    end
    self._navMeshAgent:SetDestination(position)
end

function cysoldierssortie_comp_character_entity:SetDestinationXYZ(px, py, pz)
    if not self._navMeshAgent or  self._navMeshAgent:IsNull() then
        return
    end
    if not self._navMeshAgent.isOnNavMesh  or not self._navMeshAgent.isActiveAndEnabled then
        return 
    end
    SetDestinationXYZ(self._navMeshAgent, px, py, pz)
end

function cysoldierssortie_comp_character_entity:StopNav(stop)
    if not self._navMeshAgent or  self._navMeshAgent:IsNull() then
        return
    end

    if not self._navMeshAgent.isOnNavMesh  or not self._navMeshAgent.isActiveAndEnabled then
        return
    end
    self._navMeshAgent.isStopped = stop

    if self._crowed then
        if not stop  then
            self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
        else
            self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
        end
    end
end

function cysoldierssortie_comp_character_entity:Dead(anim)
    anim = anim==nil and true or anim

    if not anim then
        self:Release()
        return 
    end

    if self.character._player then
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        local res = xpcall(function()
            ApiHelper.SetParent(self.transform,poolMgr.transform)
        end,debug.traceback)

        if not res then
            self.transform:SetParent(poolMgr.transform)
        end
    end
    
    if self._animator  then
        self._animator:SetTrigger(cysoldierssortie_hero_anim_set.Dead)
    end

    if self._collider then
        self._collider.enabled = false
    end
    
    self._recycle_coroutine = cysoldierssortie_DelayCallOnce(2,function()
        if self._collider then
            self._collider.enabled = true
        end
        self:Release()
    end)
end

function cysoldierssortie_comp_character_entity:ReleaseModel()
    if self.modelGo then
        NeeGame.ReturnObject(self.modelGo)
    end
end

function cysoldierssortie_comp_character_entity:Release()
    self:ReleaseModel()
    
    if self._animator and self._gpu_anim then
        self._animator = nil
    end

    --NeeGame.ReturnObject(self.gameObject)
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if poolMgr then
        poolMgr:ReleaseObj(self)
    end
end

--生命周期函数
function cysoldierssortie_comp_character_entity:CSUpdate()
    if self.character.troopClash_IgnoreBattleUpdate then
        return
    end
    self.character:UpdateAI()
    self:DistanceCull()
    self:LookAtTarget()
    self:DisToPlayer()
    if not self._cull and self.character._view_actor then
        self:UpdateHp()
    end
end

function cysoldierssortie_comp_character_entity:UpdateHp()
    self.character:UpdateHp()
end

function cysoldierssortie_comp_character_entity:GetDistance1D(z1, z2)
    local dz = z1 - z2
    return math.abs(dz)
end

function cysoldierssortie_comp_character_entity:DisToPlayer()
    local playerZ = 0
    local currentPositionX,currentPositionY,currentPositionZ =  GetTransformPositionXYZ(self.transform)
    self._currentPositionX = currentPositionX
    if not self.character._player and self._isRunnerMode == nil then
        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        local curLevel = levelMgr.curLevel
        local isRunnerMode =  curLevel:IsRunnerMode()
        self._isRunnerMode = isRunnerMode
        if self._isRunnerMode then
            if not self._player then
                self._player = curLevel.playerLua
            end
        end
    end

    if not self.character._player and self._isRunnerMode then
        local x,y,z=  self._player:GetPositionXYZ()
        playerZ = z
    end
    self.character._disToPlayerZ = self:GetDistance1D(currentPositionZ,playerZ)
    local minigame_buff_mgr= require "minigame_buff_mgr"
    minigame_buff_mgr.CheckCondition(self.character,minigame_buff_mgr.ConditionType.Behind_Enemy,self.character._disToPlayerZ)
end

function cysoldierssortie_comp_character_entity:LookAtTarget()
    if not self.character._enemyRange then
        return
    end
    
    local targetGo = self.character:GetTargetGo()

    if self.character._freezeRotate then
        return
    end
    
    if bc_IsNotNull(targetGo) then
        self._pause_rotation = false
    end
    
    if self._pause_rotation then
        return 
    end

    if self.character._unit_type ~= cysoldierssortie_unit_type.Hero and self.character._unit_type~=cysoldierssortie_unit_type.Soldier then
        return 
    end
    
    if bc_IsNotNull(targetGo)  then
        self:SmoothRotateTo(targetGo.position)
    else
        self:AlignToForward()
    end
end

function cysoldierssortie_comp_character_entity:DistanceCull()
    local minigame_mgr = require "minigame_mgr"
    if not minigame_mgr.GetIsStartGame() then
        return
    end
    local cull_dis = 80
    local horizontal_cull_dis = 60
    local crowed_dis = 20
    
    if self.character then
        if self.character._player then
            return
        end
    else
        return
    end

    if not self._cam_mgr then
        self._cam_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    end
    local cam_to_ground_farthest_dis,cam_horizontal_max_dis  = self._cam_mgr:GetFarthestDistance()
    if cam_to_ground_farthest_dis then
        cull_dis = cam_to_ground_farthest_dis
    end
    if cam_horizontal_max_dis then
        horizontal_cull_dis = cam_horizontal_max_dis
    end
    
    if GCPerf then
        --local character_posX, character_posY, character_posZ = GetTransformPositionXYZ(self.transform)
        local disToPlayerZ =  self.character._disToPlayerZ or 100
        if self._cull then
            if disToPlayerZ <= cull_dis and math.abs(self._currentPositionX or 100)<=horizontal_cull_dis then
                if self.character._view_actor and bc_IsNotNull(self.modelGo) then
                    self.modelGo:SetActive(true)
                    local minigame_buff_mgr= require "minigame_buff_mgr"
                    minigame_buff_mgr.ShowBossTips(self.character)
                end
                self._cull = false
                self.character:CreateNavAgent()
            end
        end

        if not self._crowed and self._cull == false then
            if disToPlayerZ <= crowed_dis then
                self:InitNavAgentData()
            end
        end
    end
end

-- Aligns the character to face directly forward
function cysoldierssortie_comp_character_entity:AlignToForward()
    local lookDir = self.transform.forward
    local forwardRotation = Quaternion.LookRotation(lookDir)
    
    if bc_CS_Vector3.Angle(self._modelRoot.transform.forward,lookDir) <= 5 then
        self._modelRoot.transform.rotation = forwardRotation
        self._releaseSkillPoint.transform.rotation = forwardRotation
        self._pause_rotation = true
        return
    end
    
    --forwardRotation.y = 0 -- Only rotate on the horizontal plane
    self:ApplyRotation(forwardRotation)
end

-- Smoothly rotates the character to face a target position
function cysoldierssortie_comp_character_entity:SmoothRotateTo(targetPosition)
    local directionToTarget = targetPosition - self.transform.position
    directionToTarget.y = 0 -- Only rotate on the horizontal plane
    -- Create the desired rotation
    if directionToTarget.magnitude < 0.8 then
        return 
    end 
    
    local targetRotation = Quaternion.LookRotation(directionToTarget)

    if bc_CS_Vector3.Angle(self._modelRoot.transform.forward,directionToTarget) <= 5 then
        self._modelRoot.transform.rotation = targetRotation
        self._releaseSkillPoint.transform.rotation = targetRotation
        return
    end
    
    -- Smoothly rotate toward the target
    self:ApplyRotation(targetRotation)
end

-- Applies a rotation to the model root and skill release point
function cysoldierssortie_comp_character_entity:ApplyRotation(rotation)
    self._modelRoot.transform.rotation = Quaternion.Slerp(
            self._modelRoot.transform.rotation,
            rotation,
            bc_Time.deltaTime * 10
    )
    self._releaseSkillPoint.transform.rotation = self._modelRoot.transform.rotation
end

return cysoldierssortie_comp_character_entity