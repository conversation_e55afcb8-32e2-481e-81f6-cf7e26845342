---@class kingshot_mgr_cam : fusion_mgrbase
---@field lifeScope kingshot_lifescope
local mgr = bc_Class("kingshot_mgr_cam", Fusion.MgrBase)
---@type kingshot_scene_mgr
mgr.sceneMgr = nil
---@type kingshot_camera
mgr.cameraCtrl = nil

---@type boolean
mgr.readyFlag = nil

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.sceneMgr = self.lifeScope:GetMgr("kingshot_scene_mgr")
end

function mgr:Ready()
    self.cameraCtrl = self.sceneMgr.cameraCtrl
    self.readyFlag = false
end

function mgr:GameStart()
    self.readyFlag = true
end

function mgr:__delete()
    self.readyFlag = false
end

function mgr:WorldToScreenPoint(positionWS)
    return self.cameraCtrl:WorldToScreenPoint(positionWS)
end

function mgr:ScreenToWorldPoint(positionSS)
    return self.cameraCtrl:ScreenToWorldPoint(positionSS)
end

function mgr:GetFarthestDistance()
    return math.huge, math.huge
end

return mgr
