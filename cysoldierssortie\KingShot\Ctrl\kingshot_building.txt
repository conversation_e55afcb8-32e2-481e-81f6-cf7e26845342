---@class kingshot_building
local building = bc_Class("kingshot_building")
---@type kingshot_scene_mgr
building.sceneMgr = nil
---@type kingshot_res_mgr
building.resMgr = nil
---@type fusion_gopool
building.buildingPool = nil
---@type fusion_gopool
building.buildingItemPool = nil
---@type kingshot_buildingunit[]
building.BuildingUnitList = nil

function building:__init(...)
    self.sceneMgr, self.resMgr = ...
    local goPoolCls = require("fusion_gopool")
    self.buildingPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.BuildingUnit, "kingshot_buildingunit")
    self.buildingPool:Preload(3)
    self.buildingItemPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.BuildingItem, "kingshot_buildingitem")
    self.buildingItemPool:Preload(3)
    self.sceneMgr.TimeSecondBind:Register(function(timer)
        self:TimeSecondListener(timer)
    end)
end

function building:TimeSecondListener(timer)
    for i = #self.sceneMgr.BuildingDatasByPos, 1, -1 do
        local buildingData = self.sceneMgr.BuildingDatasByPos[i]
        if buildingData then
            self:SpawnBuilding(buildingData)
            table.remove(self.sceneMgr.BuildingDatasByPos, i)
        end
    end
end

---@param buildingData kingshot_buildingData
function building:SpawnBuilding(buildingData)
    local config = self.resMgr:GetBuildingConfigById(buildingData.BuildingID)
    ---@type kingshot_buildingunit
    local buildingUnit = self.buildingPool:PopOne()
    buildingUnit.transform:SetParent(self.sceneMgr.LevelRoot)
    buildingUnit:Init(self.sceneMgr, self, buildingData, config)
    self.BuildingUnitList[#self.BuildingUnitList + 1] = buildingUnit
end

function building:Reset()
    self.BuildingUnitList = {}
end

---@return kingshot_buildingitem
function building:PopOneBuildingItem()
    return self.buildingItemPool:PopOne()
end

---@param item kingshot_buildingitem
function building:PushOneBuildingItem(item)
    self.buildingItemPool:PushOne(item)
end

function building:Update(deltaTime)
    for _, v in ipairs(self.BuildingUnitList) do
        v:Update(deltaTime)
    end
end

function building:__delete()

    if self.buildingPool ~= nil then
        self.buildingPool:Delete()
        self.buildingPool = nil
    end
    if self.buildingItemPool ~= nil then
        self.buildingItemPool:Delete()
        self.buildingItemPool = nil
    end
end
return building
