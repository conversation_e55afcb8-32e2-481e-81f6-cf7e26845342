---@class troopclash_animator
local anim = bc_Class("troopclash_animator")
local AniName_Idle = "Stand"
local AniName_Attack = "Attack"
local AniName_Dead = "Dead"
local AniName_Run = "Run"

local bool_Attack = "Attack"
local trigger_Dead = "Dead"
local float_AtkSpeed = "AtkSpeed"
local bool_Run = "Run"

local AnimatorOverrideController = CS.UnityEngine.AnimatorOverrideController

local AnimState = {
    Idle = 0,
    Attack = 1,
    Dead = 2,
}

anim.Animator = nil
anim.Character = nil
---@type boolean 正在移动
anim.RunFlag = nil
---@type number 攻击动画时长
anim.atkClipLength = nil

function anim:__init(...)
    self.gameObject, self.Animator, self.Character = ...
    --- 从源Animator获取需要的动画
    local ac = self.Animator.runtimeAnimatorController
    local tAnimationClips = ac.animationClips
    local attackClip = nil
    local idleClip = nil
    local deadClip = nil
    local moveClip = nil
    local clipLength = tAnimationClips.Length
    for i = 0, clipLength - 1, 1 do
        local tmpClip = tAnimationClips[i]
        if tmpClip.name == AniName_Attack then
            attackClip = tmpClip
        elseif tmpClip.name == AniName_Idle then
            idleClip = tmpClip
        elseif tmpClip.name == AniName_Dead then
            deadClip = tmpClip
        elseif tmpClip.name == AniName_Run then
            moveClip = tmpClip
        end
    end
    -- 创建新的override覆盖动画
    ---@type troopclash_player
    local player = self.Character._player
    local oc = AnimatorOverrideController(player.sceneMgr.resMgr.HeroAnimator)
    oc:set_Item(AniName_Idle, idleClip)
    oc:set_Item(AniName_Attack, attackClip)
    oc:set_Item(AniName_Dead, deadClip)
    oc:set_Item(AniName_Run, moveClip)
    self.Animator.runtimeAnimatorController = oc
    self.atkClipLength = attackClip.length

    self:ResetAnim()
end

function anim:ResetAnim()
    self.Animator:SetBool(bool_Attack, false)
    self.Animator:ResetTrigger(trigger_Dead)
    self.Animator:SetBool(bool_Run, false)
    self.animatorState = AnimState.Idle
    self.RunFlag = false
    self.nextAnimatorState = nil
end

function anim:SetAnimatorState(state)
    self.nextAnimatorState = state
    self:OnDelaySetAnimatorState()
end

function anim:SetTrigger(state)
    local tmpState = nil
    if state == cysoldierssortie_hero_anim_set.Ability then
        tmpState = AnimState.Attack
    elseif state == cysoldierssortie_hero_anim_set.Stand or state == cysoldierssortie_hero_anim_set.ReSpawn or state == cysoldierssortie_hero_anim_set.Show then
        tmpState = AnimState.Idle
        self.RunFlag = false
    elseif state == cysoldierssortie_hero_anim_set.Dead then
        tmpState = AnimState.Dead
    elseif state == cysoldierssortie_hero_anim_set.Run then
        tmpState = AnimState.Idle
        self.RunFlag = true
    end
    self:SetAnimatorState(tmpState)
end

function anim:ResetTrigger(state)
    if state == cysoldierssortie_hero_anim_set.Ability then
        self:SetTrigger(cysoldierssortie_hero_anim_set.Stand)
    end
end

function anim:OnDelaySetAnimatorState()
    if not self.nextAnimatorState then
        return
    end
    if self.nextAnimatorState == AnimState.Idle then
        self.Animator:SetBool(bool_Run, self.RunFlag)
        self.Animator:SetBool(bool_Attack, false)
        self.Animator:ResetTrigger(trigger_Dead)
    elseif self.nextAnimatorState == AnimState.Attack then
        self.Animator:SetBool(bool_Run, self.RunFlag)
        self.Animator:ResetTrigger(trigger_Dead)
        self.Animator:SetBool(bool_Attack, true)
    elseif self.nextAnimatorState == AnimState.Dead then
        self.Animator:SetBool(bool_Run, false)
        self.Animator:SetBool(bool_Attack, false)
        self.Animator:SetTrigger(trigger_Dead)
    end
    self.animatorState = self.nextAnimatorState
    self.nextAnimatorState = nil
end

function anim:SetAnimatorSpeed(speed)
    -- bc_Logger.Error(speed)
    -- self.Animator.speed = speed or 1
end

function anim:SetAtkInterval(animSpeed, loopInterval)
    local atkSpeed = 1
    if loopInterval ~= nil then
        atkSpeed = self.atkClipLength / loopInterval
    elseif animSpeed ~= nil then
        atkSpeed = animSpeed
    end
    self.Animator:SetFloat(float_AtkSpeed, atkSpeed)
end

function anim:__delete()
end

return anim
