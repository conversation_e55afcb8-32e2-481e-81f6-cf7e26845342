local poolItemBase = TinyRush_CreateClass("bcmanyknives_bladebase_item"):baseClass(require("tinyrush_gopoolitem"))
local DOTween = ManyKnivesDefine.DOTween
local easeMove = ManyKnivesDefine.Ease.Linear
local easeRot = ManyKnivesDefine.Ease.Linear
local DOVirtual = ManyKnivesDefine.DOVirtual
local Vector3 = bc_CS_Vector3
local Vector2 = bc_CS_Vector2
local easeCollect1 = ManyKnivesDefine.Ease.Linear

-- 对应的角色
poolItemBase.roleBase = nil
-- 记录刀刃是否可用
poolItemBase.valid = nil
-- 记录刀刃是否可捡起来
poolItemBase.propFlag = nil
-- 刀刃的类型
poolItemBase.bladeType = nil
-- 刀刃的高度
poolItemBase.height = nil
-- 刀刃是否在飞行状态,
poolItemBase.flyFlag = nil
-- 刀刃父物体，只有RolePlayer有用
poolItemBase.parent = nil
-- 是否自动生成
poolItemBase.autoSpawnFlag = nil
poolItemBase.pauseUnRegister = nil

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    self.blade = self.transform:Find("blade")
    self.spriteRenderer = self.blade:GetComponent(typeof(CS.UnityEngine.SpriteRenderer))
    self.collider2D = self.blade:GetComponent(typeof(CS.UnityEngine.Collider2D))
    self.height = self.spriteRenderer.bounds.size.y * 0.5
end

function poolItemBase:GetColliderKey()
    return self.collider2D.gameObject
end

function poolItemBase:Init(sceneMgr, bladeType, roleBase)
    self.roleBase = roleBase
    self:InitWithoutRole(sceneMgr, bladeType, roleBase.isPlayer)
end
function poolItemBase:InitWithoutRole(sceneMgr, bladeType, isPlayer)
    self.sceneMgr = sceneMgr
    self.bladeType = bladeType
    self:GetColliderKey().layer = isPlayer and ManyKnivesDefine.layerID.triggerPlayer or
                                      ManyKnivesDefine.layerID.triggerEnemy
    self.collider2D.enabled = true
    self.blade.transform.localPosition = Vector3.zero
    self.spriteRenderer.sortingOrder = ManyKnivesDefine.sortOrder.blade_base
    self.valid = true
    self.propFlag = false
    self.flyFlag = false
    self.parent = nil
    self.autoSpawnFlag = false
    self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
        self:PauseListener(value)
    end)
end

function poolItemBase:RefreshPause()
    self:PauseListener(self.sceneMgr.pauseBind.value)
end

function poolItemBase:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.flyTween ~= nil then
        self.flyTween.timeScale = timeScale
    end
    if self.resetTween ~= nil then
        self.resetTween.timeScale = timeScale
    end
    if self.tweenScale ~= nil then
        self.tweenScale.timeScale = timeScale
    end
end

-- 直接扔地上
function poolItemBase:Drop(pos, autoHide)
    self.sceneMgr:AddBlade()
    self.spriteRenderer.sortingOrder = ManyKnivesDefine.sortOrder.blade_base
    self:GetColliderKey().layer = ManyKnivesDefine.layerID.triggerEnemy
    self.collider2D.enabled = true
    self.propFlag = true
    self.valid = false
    local tmpLocalPos = self.spriteRenderer.transform.localPosition
    tmpLocalPos.y = -self.height
    self.spriteRenderer.transform.localPosition = tmpLocalPos
    self.transform:SetParent(self.sceneMgr.levelRoot.transform)
    self.transform.localScale = bc_CS_Vector3.one
    self.transform.localRotation = bc_CS_Quaternion.Euler(0, 0, math.random(0, 360))
    self.transform.position = pos
    if autoHide then
        -- 5秒后消失
        self.flyTween = ManyKnivesDefine.DOVirtual.DelayedCall(5, function()
            self.sceneMgr:ReduceBlade()
            -- 回收
            self:PushInPool()
        end)
        self:RefreshPause()
    else
        self.autoSpawnFlag = true
    end
end

function poolItemBase:SetParentIndex(parent, scaleMulti)
    self.parent = parent
    self.transform:SetParent(parent.transform)
    self.transform.localScale = bc_CS_Vector3.one * scaleMulti
    self.transform.localPosition = bc_CS_Vector3.zero
    self.transform.localRotation = bc_CS_Quaternion.identity
end

function poolItemBase:PushInPool()
    self.valid = false
    self.propFlag = false
    self.collider2D.enabled = false
    self.sceneMgr:BladePoolPushOne(self.bladeType, self)
end

function poolItemBase:dispose()
    self:recycle()
    self.__base:dispose()
end

function poolItemBase:recycle()
    self:KillFlyTween()
    self:KillScaleTween()
    self:KillResetTween()
    if self.pauseUnRegister ~= nil then
        self.pauseUnRegister:unRegister()
        self.pauseUnRegister = nil
    end
end

function poolItemBase:KillFlyTween()
    if self.flyTween ~= nil then
        self.flyTween:Kill()
        self.flyTween = nil
    end
end
function poolItemBase:KillResetTween()
    if self.resetTween ~= nil then
        self.resetTween:Kill()
        self.resetTween = nil
    end
end
function poolItemBase:KillScaleTween()
    if self.tweenScale ~= nil then
        self.tweenScale:Kill()
        self.tweenScale = nil
    end
end

function poolItemBase:SetScale(scaleMulti)
    self:KillScaleTween()
    self.transform.localScale = bc_CS_Vector3.one * scaleMulti
end

local flyMoveSp = 35
function poolItemBase:FlyInRole(roleBase, parent, fromDeg, tarDeg, scaleMulti)
    self.roleBase = roleBase
    self.flyFlag = true
    self.parent = parent
    self.collider2D.enabled = false
    self:GetColliderKey().layer = self.roleBase.isPlayer and ManyKnivesDefine.layerID.triggerPlayer or
                                      ManyKnivesDefine.layerID.triggerEnemy
    self:KillFlyTween()
    self.flyTween = ManyKnivesDefine.DOTween.Sequence()
    local tmpLocalPos = self.spriteRenderer.transform.localPosition
    tmpLocalPos.y = 0
    self.spriteRenderer.transform.localPosition = tmpLocalPos
    tmpLocalPos = self.transform.localPosition
    tmpLocalPos = tmpLocalPos + tmpLocalPos.normalized * -self.height
    self.transform.localPosition = tmpLocalPos
    self.transform:SetParent(self.parent.transform)
    tmpLocalPos = self.transform.localPosition
    self.spriteRenderer.sortingOrder = ManyKnivesDefine.sortOrder.blade_fly
    local pathList = {}
    local dis = 0
    local range = scaleMulti * (ManyKnivesDefine.playerCollectRange + 1.5)
    local tmpI = 1
    local perDeg = math.min(10, math.abs(tarDeg - fromDeg))
    tarDeg = tarDeg - 360
    while fromDeg > tarDeg do
        pathList[tmpI] = bc_CS_Vector3(range * math.cos(math.rad(fromDeg)), range * math.sin(math.rad(fromDeg)), 0)
        pathList[tmpI] = self.parent.transform:InverseTransformPoint(
            self.roleBase.bladeTran:TransformPoint(pathList[tmpI]))
        if tmpI > 1 then
            dis = dis + bc_CS_Vector3.Distance(pathList[tmpI - 1], pathList[tmpI])
        end
        tmpI = tmpI + 1
        fromDeg = fromDeg - perDeg
    end
    pathList[tmpI] = bc_CS_Vector3.zero
    dis = dis + bc_CS_Vector3.Distance(pathList[1], tmpLocalPos)
    local lastDis = bc_CS_Vector3.Distance(pathList[tmpI - 1], pathList[tmpI])
    dis = dis + lastDis
    local dur = dis / flyMoveSp
    self.flyTween:Insert(0, self.transform:DOLocalPath(pathList, dur, ManyKnivesDefine.PathType.CatmullRom)
        :SetEase(easeCollect1))
    local recoverTime = lastDis / flyMoveSp
    self.flyTween:Insert(0, ManyKnivesDefine.DOVirtual.Float(0, 1, dur - recoverTime, function(value)
        local dir = self.roleBase.bladeTran.position - self.transform.position
        local angle = math.deg(math.atan2(dir.y, dir.x)) - 180
        self.transform.rotation = bc_CS_Quaternion.AngleAxis(angle, bc_CS_Vector3.forward)
    end))
    self.flyTween:Insert(dur - recoverTime,
        self.transform:DOLocalRotateQuaternion(bc_CS_Quaternion.identity, recoverTime))

    self.flyTween:OnComplete(function()
        -- 刀刃转换成当前类型
        if self.bladeType ~= self.roleBase.curBladeType then
            self.roleBase.bladeList[self.gameObject] = nil
            self.roleBase:AddBladeForce(self.parent)
            self:PushInPool()
        else
            self.spriteRenderer.sortingOrder = ManyKnivesDefine.sortOrder.blade_base
            self.collider2D.enabled = true
            self.propFlag = false
            self.flyFlag = false
            self.valid = true
        end
    end)
    -- 缩放单独一个Tween
    self.tweenScale = self.transform:DOScale(Vector3.one * scaleMulti, dur)
    self:RefreshPause()
end

local recycleDur = 0.2
local recycleScale = 0.1
local easeRecycle = ManyKnivesDefine.Ease.Linear
-- 缩小回收
function poolItemBase:Reduce()
    self:KillFlyTween()
    self:KillScaleTween()
    self:KillResetTween()
    self.parent:Clear()
    self.parent = nil
    -- end
    self:PushInPool()
    -- self.transform:SetParent(self.roleBase.bladeTran)
    -- -- 旧刀刃缩小到玩家身上再回收
    -- self.tweenScale = DOTween.Sequence()
    -- self.tweenScale:Insert(0, self.transform:DOLocalMove(Vector3.zero, recycleDur):SetEase(easeRecycle))
    -- self.tweenScale:Insert(0, self.transform:DOScale(Vector3.one * recycleScale, recycleDur):SetEase(easeRecycle))
    -- self.tweenScale:OnComplete(function()
    --     -- 动画播完，回收
    --     self.sceneMgr:BladePoolPushOne(self.bladeType, self)
    -- end)
end

function poolItemBase:Trigger(point)
    if not self.roleBase.invincible then
        return self:Dead(point)
    end
    return nil
end

local moveSpeedMin = 7.5
local moveSpeedMax = 8
local rotateSpeedMin = 1600
local rotateSpeedMax = 2000
local flyDurMin = 0.5
local fluDurMax = 0.6
local dirOffMin = -30
local dirOffMax = 30
function poolItemBase:Dead(point)
    self:KillFlyTween()
    self:KillScaleTween()
    self:KillResetTween()
    self.valid = false
    self.collider2D.enabled = false
    if self.parent ~= nil then
        self.parent:Clear()
        self.transform:SetParent(self.bladeTran)
        self.parent = nil
    end
    self.flyTween = ManyKnivesDefine.DOTween.Sequence()
    local tmpLocalPos = self.spriteRenderer.transform.localPosition
    tmpLocalPos.y = -self.height
    self.spriteRenderer.transform.localPosition = tmpLocalPos
    tmpLocalPos = self.transform.localPosition
    tmpLocalPos = tmpLocalPos + tmpLocalPos.normalized * self.height
    self.transform.localPosition = tmpLocalPos
    self.transform:SetParent(self.sceneMgr.levelRoot.transform)
    self.spriteRenderer.sortingOrder = ManyKnivesDefine.sortOrder.blade_fly
    local dir = (point - self.roleBase.rigidbody.position).normalized
    dir = Vector2.Perpendicular(Vector2(dir.x, dir.y)).normalized
    local flyDirOff = math.lerp(dirOffMin, dirOffMax, math.random())
    dir = (bc_CS_Quaternion.Euler(0, 0, flyDirOff) * Vector3(dir.x, dir.y, 0)).normalized
    local flyDur = math.lerp(flyDurMin, fluDurMax, math.random())
    local moveSpeed = math.lerp(moveSpeedMin, moveSpeedMax, math.random())
    local rotateSpeed = math.lerp(rotateSpeedMin, rotateSpeedMax, math.random())
    tmpLocalPos = self.transform.position
    local tarPos = self.sceneMgr:GetSafetyPosition(tmpLocalPos + dir * (moveSpeed * flyDur))
    self.flyTween:Insert(0, self.transform:DOMove(tarPos, flyDur, false):SetEase(easeMove))
    local startAngle = self.transform.eulerAngles.z
    self.flyTween:Insert(0, DOVirtual.Float(startAngle, flyDur * rotateSpeed + startAngle, flyDur, function(value)
        self.transform.localEulerAngles = Vector3(0, 0, value)
    end):SetEase(easeRot))
    self.flyTween:InsertCallback(flyDur, function()
        if not self.roleBase.isPlayer then
            self.spriteRenderer.sortingOrder = ManyKnivesDefine.sortOrder.blade_base
            self:GetColliderKey().layer = ManyKnivesDefine.layerID.triggerEnemy
            self.collider2D.enabled = true
            self.propFlag = true
            self.sceneMgr:AddBlade()
        else
            self:PushInPool()
        end
    end)
    if not self.roleBase.isPlayer then
        -- 5秒后消失
        self.flyTween:InsertCallback(flyDur + 5, function()
            self.sceneMgr:ReduceBlade()
            -- 回收
            self:PushInPool()
        end)
    end
    self:RefreshPause()
    return dir
end

return poolItemBase
