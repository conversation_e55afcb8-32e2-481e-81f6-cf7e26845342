---@class troopclash_lifescope : fusion_lifescope
local lifeScope = bc_Class("troopclash_lifescope", require("fusion_lifescope"))
TroopClash_Define = require("troopclash_define")
local ui_window_mgr = require "ui_window_mgr"
Fusion.InitLog(CS.UnityEngine.Application.isEditor, "TroopClash")

--- 构造函数
function lifeScope:__init(...)
    self:Ctor(...)
    --缓存引擎设置
    self:CacheAppSettings()
    self:CacheQualitySettings()
    self:CacheRenderSettings()
    self:CachePhysics3DSettings()
    self:SetGlobalValue()
    self:SetQuality()

    self:Build(function()
        ---@type troopclash_res_mgr
        self.resMgr = self:GetMgr("troopclash_res_mgr")
        self.resMgr:LoadAssets(function()
            self:GameReady()
        end)
    end)
end

--- 装载管理器
function lifeScope:Configure()
    self:RegisterMgr(require("fusion_mono").New())
    self:RegisterMgr(require("fusion_load_mgr").New(true, false))
    self:RegisterMgr(require("troopclash_res_mgr").New())
    self:RegisterMgr(require("troopclash_scene_mgr").New())
    self:RegisterMgr(require("fusion_taskscheduling_mgr").New())
    self:RegisterMgr(require("troopclash_mgr_level").New(self.miniLvConfig))
    self:RegisterMgr(require("troopclash_mgr_cam").New())
    self:RegisterMgr(require("troopclash_ui_mgr").New())
    self:RegisterMgr(require("fusion_lang_mgr").New())
end

--- 往 cysoldierssortie_mgrTable 中注册需要替换的mgr
function lifeScope:BuildMiddleware()
    cysoldierssortie_AddMgr(cysoldierssortie_MgrName.level, self:GetMgr("troopclash_mgr_level"))
    cysoldierssortie_AddMgr(cysoldierssortie_MgrName.cam, self:GetMgr("troopclash_mgr_cam"))
    cysoldierssortie_AddMgr(cysoldierssortie_MgrName.ui, self:GetMgr("troopclash_ui_mgr"))
end

--- 游戏必备资源准备好，可以启动游戏了
function lifeScope:GameReady()
    TroopClash_Define.minigame_mgr.SetLoadResState(6)
    self.SafeArea = self:GetSafeArea()
    ---@type troopclash_scene_mgr
    self.sceneMgr = self:GetMgr("troopclash_scene_mgr")
    ---@type fusion_taskscheduling_mgr
    self.taskMgr = self:GetMgr("fusion_taskscheduling_mgr")
    ---@type troopclash_mgr_level
    self.levelMgr = self:GetMgr("troopclash_mgr_level")
    ---@type troopclash_mgr_cam
    self.camMgr = self:GetMgr("troopclash_mgr_cam")
    ---@type troopclash_ui_mgr
    self.uiMgr = self:GetMgr("troopclash_ui_mgr")
    ---@type fusion_lang_mgr
    self.langMgr = self:GetMgr("fusion_lang_mgr")
    self.langMgr:ParseData(self.resMgr.langTA.text)
    self.sceneMgr:Ready()
    self.uiMgr:Ready()
    self.levelMgr:Ready()
    self.camMgr:Ready()
    --要等待NeeGame的Mgr初始化完毕
    TroopClash_Define.CS.NeeGame.RegisterOnAsyncInited(function()
        --延迟几帧再启动，给Mono的Start预留
        self.taskMgr:AddTask(function()
            self.sceneMgr:GameStart()
            self.uiMgr:GameStart()
            self.levelMgr:GameStart()
            self.camMgr:GameStart()
        end, 2)
        self.taskMgr:AddTask(function()
            TroopClash_Define.minigame_mgr.OpenUiMiniGameInfo()
            --关闭loading界面
            --告诉进度条当前关卡加载完毕了
            self.enter:Resume()
            self.enter:CanClickFail()
            ui_window_mgr:ShowModule("ui_mini_select_hero")
            TroopClash_Define.event.Trigger(TroopClash_Define.event.MINIGAME_SCENE_LOAD_SUCCESS)
        end, 2)
    end)
end

--- 析构函数
function lifeScope:__delete()
    self:Dispose()
    --恢复引擎设置
    self:RestoreAppSettings()
    self:RestoreQualitySettings()
    self:RestoreRenderSettings()
    self:RestorePhysics3DSettings()
end

---游戏退出后，enter会主动调用
function lifeScope:OnDestroy()
    self:Delete()
end

--设置全局变量，除了IgnoreLayerCollision外，其余的需要先记录再设置成想要的值
function lifeScope:SetGlobalValue()
    local Physics = TroopClash_Define.Physics
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Bullet, cysoldierssortie_LayerName.Bullet, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Player, cysoldierssortie_LayerName.Player, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Enemy, cysoldierssortie_LayerName.Enemy, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Bullet, cysoldierssortie_LayerName.Player, true)

    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Enemy, cysoldierssortie_LayerName.Prop, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Prop, cysoldierssortie_LayerName.Prop, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Bullet, cysoldierssortie_LayerName.Prop, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Prop, cysoldierssortie_LayerName.Player, false)

    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Default, cysoldierssortie_LayerName.Enemy, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Default, cysoldierssortie_LayerName.Default, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Default, cysoldierssortie_LayerName.Prop, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Default, cysoldierssortie_LayerName.Player, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.Default, cysoldierssortie_LayerName.Bullet, true)

    --12 = 队伍的碰撞体和场景边界
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.L12, cysoldierssortie_LayerName.Default, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.L12, cysoldierssortie_LayerName.L12, false)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.L12, cysoldierssortie_LayerName.Bullet, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.L12, cysoldierssortie_LayerName.Player, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.L12, cysoldierssortie_LayerName.Enemy, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.L12, cysoldierssortie_LayerName.Prop, true)
    Physics.IgnoreLayerCollision(cysoldierssortie_LayerName.L12, cysoldierssortie_LayerName.Boom, true)
end

function lifeScope:SetQuality()
    CS.UnityEngine.QualitySettings.shadowDistance = 160
    CS.UnityEngine.Time.fixedDeltaTime = 0.04
    TroopClash_Define.Physics.reuseCollisionCallbacks = true
end

return lifeScope
