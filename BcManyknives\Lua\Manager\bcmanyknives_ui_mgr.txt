local manyknives_ui_mgr = TinyRush_CreateClass("bcmanyknives_ui_mgr"):baseClass(TinyRush_Scope):interface(
    TinyRush_IInit, TinyRush_IUpdate)

manyknives_ui_mgr.resMgr = nil
manyknives_ui_mgr.mono = nil
manyknives_ui_mgr.gameMgr = nil
manyknives_ui_mgr.audioMgr = nil
manyknives_ui_mgr.tutMgr = nil
manyknives_ui_mgr.langMgr = nil

manyknives_ui_mgr.mainPanel = nil
manyknives_ui_mgr.dataSrc = nil

-- 当前关卡
manyknives_ui_mgr.lvTitleText = nil
-- 剩余多少敌人显示
manyknives_ui_mgr.roleNumText = nil
manyknives_ui_mgr.bossGuideObj = nil
manyknives_ui_mgr.bossCG = nil
manyknives_ui_mgr.bossText = nil

manyknives_ui_mgr.uiCtrl = nil
manyknives_ui_mgr.playerFollower = nil
manyknives_ui_mgr.playerArrow = nil

manyknives_ui_mgr.startFlag = nil
manyknives_ui_mgr.joystickUnReg = nil

manyknives_ui_mgr.hpSliderPool = nil
manyknives_ui_mgr.hpSliderCon = nil

manyknives_ui_mgr.dmgPool = nil
manyknives_ui_mgr.dmgCon = nil
manyknives_ui_mgr.realCanvaSize = nil
manyknives_ui_mgr.dmgPair = {}

function manyknives_ui_mgr:rush_OnInit()
    self.resMgr = self.lifeScope:get("bcmanyknives_res_mgr")
    self.mono = self.lifeScope:get("tinyrush_mono")
    self.gameMgr = self.lifeScope:get("bcmanyknives_game_mgr")
    self.sceneMgr = self.lifeScope:get("bcmanyknives_scene_mgr")
    self.audioMgr = self.lifeScope:get("bcmanyknives_audio_mgr")
    self.tutMgr = self.lifeScope:get("bcmanyknives_tutorial_mgr")
    self.langMgr = self.lifeScope:get("bcmanyknives_lang_mgr")
end

function manyknives_ui_mgr:dispose()
    self.startFlag = false
    if self.hpSliderPool ~= nil then
        self.hpSliderPool:dispose()
        self.hpSliderPool = nil
    end
    if self.dmgPool ~= nil then
        self.dmgPool:dispose()
        self.dmgPool = nil
    end
    if self.joystickUnReg then
        self.joystickUnReg:unRegister()
        self.joystickUnReg = nil
    end
    if self.bossTweener ~= nil then
        self.bossTweener:Kill()
        self.bossTweener = nil
    end
    if self.breathSequence ~= nil then
        self.breathSequence:Kill()
        self.breathSequence = nil
    end
    if self.uiCtrl ~= nil then
        self.uiCtrl:dispose()
        self.uiCtrl = nil
    end
    self.__base:dispose()
end

function manyknives_ui_mgr:rush_OnUpdate(deltaTime)
    if not self.startFlag then
        return
    end
    local anchorPos = self.sceneMgr:GetPlayerViewPos()
    anchorPos.x = self.realCanvaSize.x * (anchorPos.x - 0.5)
    anchorPos.y = self.realCanvaSize.y * (anchorPos.y - 0.5)
    self.playerFollower.anchoredPosition = bc_CS_Vector2(anchorPos.x, anchorPos.y)
    for _, v in pairs(self.dmgPair) do
        v:OnUpdate(deltaTime)
    end
end

function manyknives_ui_mgr:Ready()
    self.mainPanel = bc_CS_GameObject.Instantiate(self.resMgr.mainPanelPrefab)
    self.mainPanel.transform:SetParent(self.mono.globalMonoGO.transform)
    self.mainPanel.transform.localPosition = bc_CS_Vector3.zero
    self.mainPanel.transform.localScale = bc_CS_Vector3.one
    local tmp = self.mainPanel:GetComponent(typeof(CS.GameLuaBehaviour_New))
    tmp:Awake()
    self.dataSrc = CshapToLuaValue_New(tmp)
    self.uiCamera = self.dataSrc["uiCamera"]
    self.swordBg = self.dataSrc["swordBg"]
    self.swordFill = self.dataSrc["swordFill"]
    self.fleetfootBg = self.dataSrc["fleetfootBg"]
    self.fleetfootFill = self.dataSrc["fleetfootFill"]
    self.crazeBg = self.dataSrc["crazeBg"]
    self.crazeFill = self.dataSrc["crazeFill"]
    self.lvTitleText = self.dataSrc["lvTitleText"]
    self.startGuideText = self.dataSrc["startGuideText"]
    self.startGuideImg = self.dataSrc["startGuideImg"]
    self.hpSliderCon = self.dataSrc["hpSliderCon"]
    self.playerFollower = self.dataSrc["playerFollower"]
    self.playerArrow = self.dataSrc["playerArrow"]
    self.roleNumText = self.dataSrc["roleNumText"]
    self.startGuide = self.dataSrc["startGuide"]
    self.CDSlider = self.dataSrc["CDSlider"]
    self.bossGuideObj = self.dataSrc["bossGuide"]
    self.bossCG = self.dataSrc["bossCG"]
    self.bossText = self.dataSrc["txt_boss"]
    if not self.bossText.font.fallbackFontAssetTable:Contains(self.resMgr.fontAR) then
        self.bossText.font.fallbackFontAssetTable:Add(self.resMgr.fontAR)
    end
    self.bossText.text = self.langMgr:GetLangById(1059100012)
    self.tutMgr:SetLuaB(self.dataSrc["tutorialLuaB"])

    self.lvTitleText.text = string.format(self.langMgr:GetLangById(1059100025) .. ":%d", self.gameMgr.level)
    local tempRealSize = self.dataSrc["CanvasScaler"].referenceResolution
    local realScale = self.lifeScope.ScreenSize.x / self.lifeScope.ScreenSize.y
    local standardScale = tempRealSize.x / tempRealSize.y
    -- 更宽
    if realScale > standardScale then
        tempRealSize.x = tempRealSize.x * (realScale / standardScale)
    elseif realScale < standardScale then
        tempRealSize.y = tempRealSize.y * (standardScale / realScale)
    end
    self.realCanvaSize = bc_CS_Vector2(math.round(tempRealSize.x), math.round(tempRealSize.y))
    local topRect = self.dataSrc["topRect"]
    local topSize = topRect.sizeDelta
    topSize.y = 100 + self.lifeScope.TopRatio * self.realCanvaSize.y
    topRect.sizeDelta = topSize

    self.uiCtrl = require("bcmanyknives_uictrl").new(self.lifeScope, self.dataSrc["ctrlEventTrigger"],
        self.dataSrc["JoystickBg"], self.dataSrc["Joystick_head"], self.dataSrc["joyOriPos"])
    self.uiCtrl:Hide()
    -- 移动控制监听
    self.joystickUnReg = self.uiCtrl.joystickBindable:register(function(value)
        self:MoveListener(value)
    end)
    local hpSliderParent = bc_CS_GameObject("hpSliderPool")
    hpSliderParent:SetActive(false)
    hpSliderParent.transform:SetParent(self.mainPanel.transform)
    hpSliderParent.transform.localScale = bc_CS_Vector3.one
    hpSliderParent.transform.localPosition = bc_CS_Vector3.zero
    self.hpSliderPool = require("tinyrush_gopool").new(hpSliderParent, self.dataSrc["hpSlider_Prefab"],
        "bcmanyknives_hpslider_ctrl")
    self.hpSliderPool:preload(ManyKnivesDefine.hpSliderPreloadNum)
    self.dmgCon = self.dataSrc["dmgCon"]
    self.dmgPool = require("tinyrush_gopool").new(hpSliderParent, self.dataSrc["dmgPrefab"], "bcmanyknives_dmg_ctrl")
    self.dmgPool:preload(ManyKnivesDefine.dmgPreloadNum)

    if self.gameMgr.level == 1 then
        self.startGuideText.text = self.langMgr:GetLangById(105910001)
    else
        self.startGuideText.text = self.langMgr:GetLangById(105910002)
    end

    self.sceneMgr.enemyNumBind:register(function(value)
        self:roleNumListener(value)
    end, true)
    self.sceneMgr.bossTimerBind:register(function(value)
        self:bossTimerListener(value)
    end, true)
    self.sceneMgr.rolePlayer.crazeBind:register(function(value)
        self.crazeBg.gameObject:SetActive(value)
        if value then
            self.crazeBg:SetAsLastSibling()
        end
    end)
    self.sceneMgr.rolePlayer.bigSwordBind:register(function(value)
        self.swordBg.gameObject:SetActive(value)
        if value then
            self.swordBg:SetAsLastSibling()
        end
    end)
    self.sceneMgr.rolePlayer.fleetfootBind:register(function(value)
        self.fleetfootBg.gameObject:SetActive(value)
        if value then
            self.fleetfootBg:SetAsLastSibling()
        end
    end)
    self.sceneMgr.rolePlayer.bigSwordTimerBind:register(function(value)
        self:BigSwordTimer(value)
    end)
    self.sceneMgr.rolePlayer.crazeTimerBind:register(function(value)
        self:CrazeTimer(value)
    end)
    self.sceneMgr.rolePlayer.fleetfootTimerBind:register(function(value)
        self:FleefootTimer(value)
    end)
    self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
        self:PauseListener(value)
    end)
    self:ResetGame()
end

function manyknives_ui_mgr:RefreshPause()
    self:PauseListener(self.sceneMgr.pauseBind.value)
end

function manyknives_ui_mgr:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.bossCGTween ~= nil then
        self.bossCGTween.timeScale = timeScale
    end
    if self.breathSequence ~= nil then
        self.breathSequence.timeScale = timeScale
    end
end

function manyknives_ui_mgr:CrazeTimer(value)
    self.crazeFill.fillAmount = 1 - value / ManyKnivesDefine.playerEffect_Config.CD_craze
end
function manyknives_ui_mgr:BigSwordTimer(value)
    self.swordFill.fillAmount = 1 - value / ManyKnivesDefine.playerEffect_Config.CD_bigSword
end
function manyknives_ui_mgr:FleefootTimer(value)
    self.fleetfootFill.fillAmount = 1 - value / ManyKnivesDefine.playerEffect_Config.CD_fleetfoot
end

-- 当前击杀监听
function manyknives_ui_mgr:roleNumListener(value)
    if not self.sceneMgr.bossFlag then
        self.roleNumText.text = string.format(self.langMgr:GetLangById(1059100013) .. ": %d/%d", value,
            self.sceneMgr.enemyNumMax)
    end
end
function manyknives_ui_mgr:bossTimerListener(value)
    if self.sceneMgr.bossFlag then
        if value > 0 then
            self.roleNumText.text = string.format(self.langMgr:GetLangById(1059100015) ..
                                                      " <color=#F57563>%02d:%02d</color>", math.floor(value / 60),
                math.ceil(value % 60))
        else
            self.roleNumText.text = string.format("<color=#F57563>%s</color>", self.langMgr:GetLangById(1059100014))
        end
    end
end

function manyknives_ui_mgr:bossCGPlay(callback)
    self:killBossCG()
    self.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.boss_appears)
    self.bossGuideObj:SetActive(true)
    self.bossCG.alpha = 0
    self.bossCGTween = ManyKnivesDefine.DOTween.Sequence()
    self.bossCGTween:Append(ManyKnivesDefine.DOVirtual.Float(0, 1, 0.3, function(value)
        self.bossCG.alpha = value
    end))
    self.bossCGTween:AppendInterval(1)
    self.bossCGTween:Append(ManyKnivesDefine.DOVirtual.Float(1, 0, 0.3, function(value)
        self.bossCG.alpha = value
    end))
    self.bossCGTween:OnComplete(function()
        self.bossGuideObj:SetActive(false)
        callback()
    end)
    self.bossCGTween:SetLink(self.bossGuideObj)
    self:RefreshPause()
end
function manyknives_ui_mgr:killBossCG()
    if self.bossCGTween ~= nil then
        self.bossCGTween:Kill()
        self.bossCGTween = nil
    end
    self.bossGuideObj:SetActive(false)
end

function manyknives_ui_mgr:startBtnTween()
    self:killStartTween()
    self.startGuide:SetActive(true)
    self.startGuideImg.transform.localScale = bc_CS_Vector3.one
    self.breathSequence = self.startGuideImg.transform:DOScale(bc_CS_Vector3.one * 1.1, 0.5):SetEase(
        ManyKnivesDefine.Ease.InSine):SetLoops(-1, ManyKnivesDefine.LoopType.Yoyo)
    self:RefreshPause()
end
function manyknives_ui_mgr:killStartTween()
    if self.breathSequence ~= nil then
        self.breathSequence:Kill()
        self.breathSequence = nil
    end
    self.startGuide:SetActive(false)
end

function manyknives_ui_mgr:refreshCD_craze(time)
    self.craze:SetActive(true)
    self.crazeTime = self.crazeTime + time
end
function manyknives_ui_mgr:refreshCD_fleetfoot(time)
    self.fleetfoot:SetActive(true)
    self.fleetTime = self.fleetTime + time
end
function manyknives_ui_mgr:refreshCD_sword(time)
    self.sword:SetActive(true)
    self.swordTime = self.swordTime + time
end

function manyknives_ui_mgr:StartBattle()
    self:killStartTween()
    self.playerFollower.gameObject:SetActive(true)
    self.uiCtrl:Ready()
    self.CDSlider:SetActive(true)
    self.startFlag = true
end

-- 道具
function manyknives_ui_mgr:MoveListener(value)
    if not self.startFlag then
        self.gameMgr:StartBattle()
    end
    if value.x ~= 0 or value.y ~= 0 then
        local radius = 180
        self.playerArrow.anchoredPosition = radius * value
        local deg = bc_CS_Vector2.Angle(value, bc_CS_Vector2.right)
        if value.y < 0 then
            deg = -deg
        end
        self.playerArrow.localEulerAngles = bc_CS_Vector3(0, 0, deg - 90)
    end
end

--- 血条
function manyknives_ui_mgr:popHpSlider(roleBase, hpBind, hpMaxBind)
    local slider = self.hpSliderPool:popOne()
    slider:Init(roleBase, hpBind, hpMaxBind, self.sceneMgr.mainCamera, self)
    slider.transform:SetParent(self.hpSliderCon)
    slider.transform.localScale = bc_CS_Vector3.one
    return slider
end
function manyknives_ui_mgr:pushHpSlider(hpSlider)
    self.hpSliderPool:pushOne(hpSlider)
end

-- 伤害 type：1:普通伤害，2:真伤
function manyknives_ui_mgr:ShowDmgText(type, dmgValue, pos)
    local dmg = self.dmgPool:popOne()
    self.dmgPair[dmg.gameObject] = dmg
    dmg.transform:SetParent(self.dmgCon)
    dmg:Play(type, tostring(dmgValue), pos, self.sceneMgr.mainCamera, self)
end

function manyknives_ui_mgr:pushDmgText(item)
    self.dmgPair[item.gameObject] = nil
    self.dmgPool:pushOne(item)
end

function manyknives_ui_mgr:OverBattle()
    self.uiCtrl:Hide()
end

function manyknives_ui_mgr:ResetGame()
    self.sceneMgr.enemyNumBind:invokeEvent()
    self.CDSlider:SetActive(false)
    if self.hpSliderPool ~= nil then
        self.hpSliderPool:clear()
    end
    if self.dmgPool ~= nil then
        self.dmgPool:clear()
    end
    self.dmgPair = {}
    self.startFlag = false
    self.playerFollower.gameObject:SetActive(false)
    self.uiCtrl:Hide()
    self:startBtnTween()
    self:killBossCG()
    self.bossFlag = false
end

return manyknives_ui_mgr
