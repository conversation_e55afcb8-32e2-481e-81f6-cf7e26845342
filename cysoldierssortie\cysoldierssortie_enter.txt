require "cysoldierssortie_global_define"
require("cysoldierssortie_emmy_debug")
        :emmyDebug(cysoldierssortie_Common.riderPort,cysoldierssortie_Common.riderPath)
local battle_player = require("battle_player")
local state = {
    NONE = 0, -- 未开始
    LOADING = 1, -- 加载中
    PLAYING = 2, -- 正在游戏中
    PAUSE = 3, -- 暂停
    FINISH = 4 -- 结束
}

local contex = {}

local removeAllLuaCache = function()
    if not CS.UnityEngine.Application.isEditor then
        return
    end
    local Path = CS.System.IO.Path
    local luaFolder = CS.UnityEngine.Application.dataPath .. "/Lua/cysoldierssortie"
    local txtFiles = CS.System.IO.Directory.GetFiles(luaFolder, "*.txt", CS.System.IO.SearchOption.AllDirectories)
    local length = txtFiles.Length - 1
    local oldEndIndex = - #".txt" - 1
    for i = 0, length, 1 do
        local fileName = Path.GetFileName(txtFiles[i])
        fileName = string.sub(fileName, 1, oldEndIndex)
        package.loaded[fileName] = nil
    end
end

local CloseGameWindow = function(self)
    if self.eventTable then
        cysoldierssortie_MsgCenter:UnsubscribeEvents(self.eventTable)
        self.eventTable = nil
    end
    if cysoldierssortie_mgr then
        cysoldierssortie_mgr:DestroyMainGame()
    end
    -- 游戏退出 释放全局变量
    cysoldierssortie_mgr = nil
    cysoldierssortie_MsgCenter = nil
    --cysoldierssortie_Level_csvData = nil
    if bc_common_game_ctrl then
        bc_common_game_ctrl:Dispose()
        bc_common_game_ctrl = nil
    end

    removeAllLuaCache()
end

local gameData = {
    name = "cysoldierssortie",
    type = 1166,
    useCommonUI = true,
}


local SendMessager = function(even_name, data)
    if contex and contex.messager then
      -- contex.messager(even_name, data)
        local event = require "event"
        --print("SendMessagerSendMessager")
        event.Trigger("MINI_GAME_MESSAGE_"..even_name,data)
    end
end

local isCanClickFail=true
return {
    Open = function(self, level, loader, messager, ...)
        self.bgmVolume= 1  --bgm音量大小
        self.effectVolume=  1   --音效音量大小

        --local _bgmVolume,_effectVolume =  tiny_music_contorller.GetVolumeAndMuteMainGameVolume()
        --self.bgmVolume = _bgmVolume
        --self.effectVolume = _effectVolume
        
        require("cysoldierssortie_emmy_debug"):UpdateLoading(contex)
        -- _M = {}
        -- print("_M",_M.__index)
        -- setmetatable(_M, {__index = {}})
        -- print("_M__last",_M)
        contex.level = level
        contex.messager = messager
        contex.state = state.LOADING

        bc_common_game_ctrl = require "bc_common_game_ctrl"
        bc_common_game_ctrl.Init(level, loader, messager, ...)

        require "cysoldierssortie_global_define"

        --初始化消息管理器，注册事件
        --cysoldierssortie_MsgCenter = bc_EventMessager.New()
        --self.eventTable = {
        --    { code = cysoldierssortie_MsgCode.STAGE_FAIL, func = bc_BindCallback(self, self.Fail) },
        --    { code = cysoldierssortie_MsgCode.STAGE_PAUSE, func = bc_BindCallback(self, self.Pause) },
        --    { code = cysoldierssortie_MsgCode.STAGE_CONTINUE, func = bc_BindCallback(self, self.Resume) },
        --    { code = cysoldierssortie_MsgCode.STAGE_RESET, func = bc_BindCallback(self, self.Reset) },
        --    { code = cysoldierssortie_MsgCode.STAGE_SUCCESS, func = bc_BindCallback(self, self.FINISH) },
        --}
        --cysoldierssortie_MsgCenter:GetInstance():SubscribeEvents(self.eventTable)

        cysoldierssortie_mgr = require("cysoldierssortie_mgr_total").New(self)
        cysoldierssortie_mgr:LoadLevel(contex.level)
      --  contex.state = state.PLAYING
        
        if battle_player then
            battle_player.ShowSceneLight(false)
        end

        -- print("Open")
    end,

    SetState= function(_state)
        contex.state= _state
    end,
    --当前关卡状态
    State = function(self)
        return contex.state or state.NONE
    end,
    GameData = function()
        return gameData
    end,
    --重置当前关卡
    Reset = function(self)
        isCanClickFail=false
        -- if cysoldierssortie_mgr then
        --     cysoldierssortie_mgr.Restore(cysoldierssortie_mgr)
        -- end
        cysoldierssortie_mgr:ReloadLevel(contex.level)
        contex.state = state.PLAYING
        -- self.startTimerHandle = bc_TimerManager:GetInstance():SetTimerOut(bc_BindCallback(self, function()
        -- end), 0.05)
        if battle_player then
            battle_player.ShowSceneLight(false)
        end
    end,
    --暂停
    Pause = function(self)
        contex.state = state.PAUSE
    end,
    --继续
    Resume = function(self)
        contex.state = state.PLAYING
    end,
    --结束并触发失败
    Fail = function(self)
        print("返回大厅11")
        contex.state = state.PAUSE
        if not isCanClickFail then
            cysoldierssortie_log("不能点击失败: isCanReset=false")
            return
        end
        
        
        --contex.state = state.FINISH
        if cysoldierssortie_mgr then
            cysoldierssortie_mgr.canReload=true
        end
        -- CloseGameWindow(self)
        SendMessager("Finish", { false, 0 })

        --if battle_player then
        --    battle_player.ShowSceneLight(true)
        --end
        -- bc_MiniGameCtrl = nil
    end,
    CanClickFail=function(self)
        isCanClickFail=true
        cysoldierssortie_log("可以失败了: isCanReset=true")
    end,
    Close = function(self)
        print("返回大厅")
        contex.state = state.None
        if cysoldierssortie_mgr then
            cysoldierssortie_mgr.canReload=true
        end
        CloseGameWindow(self)
        SendMessager("Close", nil)
        cysoldierssortie_RemoveAllMgr()
        -- bc_MiniGameCtrl = nil
        CS.cysoldierssortie.MonoAgent.Instance:Destroy()
        ClearLuaCompCache()
        if battle_player then
            battle_player.ShowSceneLight(true)
        end

        local val = require "val"
        if val.IsTrue("sw_minigame_create_entity", 0) then
            DumpEntityRecord()
        end
    end,
    FINISH = function(self)
        contex.state = state.PAUSE
        --contex.state = state.FINISH
        SendMessager("Finish", { true, 0 })--LevelManager的问题
        --
        --if battle_player then
        --    battle_player.ShowSceneLight(true)
        --end
    end
}
