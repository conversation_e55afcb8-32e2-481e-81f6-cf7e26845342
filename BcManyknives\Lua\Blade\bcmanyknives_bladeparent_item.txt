local poolItemBase = TinyRush_CreateClass("bcmanyknives_bladeparent_item"):baseClass(require("tinyrush_gopoolitem"))
-- 对应的刀刃
poolItemBase.blade = nil
poolItemBase.index = nil
poolItemBase.sceneMgr = nil
poolItemBase.pauseUnRegister = nil

function poolItemBase:Init(sceneMgr, blade)
    self.sceneMgr = sceneMgr
    self.blade = blade
    if self.pauseUnRegister == nil then
        self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
            self:PauseListener(value)
        end)
    end
end
function poolItemBase:SetIndex(index)
    self.index = index
    self.gameObject.name = tostring(self.index)
end
function poolItemBase:IsEmpty()
    return self.blade == nil
end
function poolItemBase:Clear()
    self.blade = nil
end

function poolItemBase:ResetRotation(localPos, localRot)
    self:KillResetTween()
    self.resetTween = ManyKnivesDefine.DOTween.Sequence()
    local angle = bc_CS_Quaternion.Angle(self.transform.localRotation, localRot)
    local dur = bc_CS_Quaternion.Angle(self.transform.localRotation, localRot) / 260
    self.resetTween:Insert(0, self.transform:DOLocalMove(localPos, dur))
    self.resetTween:Insert(0, self.transform:DOLocalRotateQuaternion(localRot, dur))
    self:RefreshPause()
end

function poolItemBase:RefreshPause()
    self:PauseListener(self.sceneMgr.pauseBind.value)
end

function poolItemBase:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.resetTween ~= nil then
        self.resetTween.timeScale = timeScale
    end
end

function poolItemBase:KillResetTween()
    if self.resetTween ~= nil then
        self.resetTween:Kill()
        self.resetTween = nil
    end
end

function poolItemBase:recycle()
    self:KillResetTween()
    if self.pauseUnRegister ~= nil then
        self.pauseUnRegister:unRegister()
        self.pauseUnRegister = nil
    end
end
return poolItemBase
