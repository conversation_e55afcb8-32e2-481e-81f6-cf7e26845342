local poolItemBase = TinyRush_CreateClass("bcmanyknives_dmg_ctrl"):baseClass(require("tinyrush_gopoolitem"))

local itemNames = {
    dmgtext = "dmgText",
    bg = "bg"
}

poolItemBase.text = nil
poolItemBase.bgRect = nil
poolItemBase.tween = nil
poolItemBase.worldPos = nil
poolItemBase.mainCamera = nil
poolItemBase.uiMgr = nil

poolItemBase.pauseUnRegister = nil

local leftOff = -50
local rightOff = 50
local height = 40
local easeMove = ManyKnivesDefine.Ease.OutSine
local easeFade = ManyKnivesDefine.Ease.OutQuad
local easeScaleFakeIn = ManyKnivesDefine.Ease.OutQuad
local easeScaleFakeOut = ManyKnivesDefine.Ease.Linear
local colorRed = bc_CS_Color(1, 0, 0, 1)
local colorRedClear = bc_CS_Color(1, 0, 0, 0)
local colorWhite = bc_CS_Color(1, 1, 1, 1)
local colorWhiteClear = bc_CS_Color(1, 1, 1, 0)

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    local tmp = self.gameObject:GetComponent(typeof(CS.GameLuaBehaviour_New))
    tmp:Awake()
    local dataSrc = CshapToLuaValue_New(tmp)
    self.text = dataSrc[itemNames.dmgtext]
    self.bgRect = dataSrc[itemNames.bg]
end

function poolItemBase:OnUpdate(deltaTime)
    local anchorPos = self.mainCamera:WorldToViewportPoint(self.worldPos)
    anchorPos.x = self.uiMgr.realCanvaSize.x * (anchorPos.x - 0.5)
    anchorPos.y = self.uiMgr.realCanvaSize.y * (anchorPos.y - 0.5)
    self.bgRect.anchoredPosition = bc_CS_Vector2(anchorPos.x, anchorPos.y)
end

-- type：1:普通伤害，2:真伤
function poolItemBase:Play(type, str, pos, camera, uiMgr)
    self.mainCamera = camera
    self.uiMgr = uiMgr
    self.worldPos = pos
    self:KillTween()
    self.text.text = str
    local oriCol = type == 1 and colorRed or colorWhite
    local tarCol = type == 1 and colorRedClear or colorWhiteClear
    self.text.color = oriCol
    self.tween = ManyKnivesDefine.DOTween.Sequence()
    self:OnUpdate()
    self.transform.localScale = bc_CS_Vector3.one
    self.text.rectTransform.anchoredPosition = bc_CS_Vector2(math.lerp(leftOff, rightOff, math.random()), 0)
    self.text.transform.localScale = bc_CS_Vector3.one * 0.4
    -- 放大
    self.tween:Insert(0, self.text.transform:DOScale(bc_CS_Vector3.one, 0.35):SetEase(easeScaleFakeIn))
    -- 向上移动
    self.tween:Insert(0, self.text.transform:DOLocalMoveY(height, 0.8, false):SetEase(easeMove))
    -- 渐隐
    self.tween:Insert(0.5, ManyKnivesDefine.DOVirtual.Float(0, 1, 0.3, function(value)
        self.text.color = bc_CS_Color.Lerp(oriCol, tarCol, value)
    end):SetEase(easeFade))
    -- 缩放
    self.tween:Insert(0.5, self.text.transform:DOScale(bc_CS_Vector3.one * 0.5, 0.3):SetEase(easeScaleFakeOut))
    self.tween:OnComplete(function()
        uiMgr:pushDmgText(self)
    end)
    self.tween:SetLink(self.gameObject)
    self.pauseUnRegister = uiMgr.sceneMgr.pauseBind:register(function(value)
        self:PauseListener(value)
    end, true)
end

function poolItemBase:PauseListener(pause)
    if self.tween ~= nil then
        self.tween.timeScale = pause and 0 or 1
    end
end

function poolItemBase:KillTween()
    if self.tween ~= nil then
        self.tween:Kill()
        self.tween = nil
    end
end

function poolItemBase:recycle()
    self:KillTween()
    if self.pauseUnRegister ~= nil then
        self.pauseUnRegister:unRegister()
        self.pauseUnRegister = nil
    end
end

function poolItemBase:dispose()
    self:KillTween()
    self.__base:dispose()
end

return poolItemBase
