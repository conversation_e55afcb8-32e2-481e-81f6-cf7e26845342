---@class fusion_bindableUnRegister 数据监听的注销对象
local bindableUnRegister = bc_Class("fusion_bindableUnRegister")
---@type fusion_bindable
bindableUnRegister.bindable = nil
bindableUnRegister.onValueChanged = nil

function bindableUnRegister:__init(...)
    self.bindable, self.onValueChanged = ...
end

function bindableUnRegister:UnRegister()
    if self.bindable ~= nil then
        self.bindable:UnRegister(self.onValueChanged)
    end
    self.bindable = nil
    self.onValueChanged = nil
end

return bindableUnRegister
