local Physics2D = CS.UnityEngine.Physics2D
local UnityAxisHorizontal = CS.UnityEngine.RectTransform.Axis.Horizontal
local UnityAxisVertical = CS.UnityEngine.RectTransform.Axis.Vertical
ManyKnivesDefine = {
    UnityTime = CS.UnityEngine.Time,

    DOTween = CS.DG.Tweening.DOTween,
    Ease = CS.DG.Tweening.Ease,
    DOVirtual = CS.DG.Tweening.DOVirtual,
    LoopType = CS.DG.Tweening.LoopType,
    RotateMode = CS.DG.Tweening.RotateMode,
    PathMode = CS.DG.Tweening.PathMode,
    PathType = CS.DG.Tweening.PathType,

    -- 移动偏移
    roleMoveOff = {
        offTimeCD = 1,
        offAngleMin = 0,
        offAngleMax = 30
    },

    -- 玩家搜集刀刃的范围
    playerCollectRange = 3,

    roleDebuffConfig = {
        -- 毒气受伤频率
        hurtByMiasmaCD = 0.7,
        -- 毒气减速持续时间
        debuff_moveSp_CD = 0.5,
        -- 冷冻持续几秒
        debuff_freeze_CD = 2,
        -- 闪电禁锢持续几秒
        debuff_light_CD = 2,
        -- 碰到敌人身体扣血频率
        hurtByRoleCD = 0.9
    },

    -- 玩家技能伤害(百分比)
    playerEffect_Dmg = {
        -- 冰冻
        snow = 1.3,
        -- 火球
        fire = 3.8,
        -- 毒气
        miasma = 1.2,
        -- 闪电
        lightning = 4,
        -- 狂暴系数
        crazeMulti = 1.7
    },
    -- 刀刃伤害
    -- 1普通刀刃2冰冻3火球4瘴气5闪电
    bladeDmgWithType = {100, 180, 180, 180, 180},
    -- 玩家技能计数相关配置
    playerEffect_Config = {
        -- 冰冻
        CD_snow = 1.5,
        -- 冰球数量
        snow_Num = 6,
        -- 冰球飞行多少米
        snow_FlyLimit = 10,
        -- 冰球飞行速度
        snow_FlySp = 10,
        -- 火球CD
        CD_fire = 1,
        -- 每个火球间隔
        CD_fire2 = 0.1,
        -- 每轮发射数量
        fire_Num = 8,
        -- 毒气
        CD_miasma = 3,
        -- 玩家毒气存在时间
        miasma_Stay = 5,
        -- 闪电
        CD_lightning = 3.5,
        -- 闪电半径米
        lightning_raduis = 8.5,
        -- 闪电存在时间
        lightning_Dur = 1.8,
        -- 狂暴
        CD_craze = 5,
        -- 狂暴状态，刀刃旋转速度倍率
        craze_bladeSpeedMulti = 1.3,
        -- 大宝剑
        CD_bigSword = 5,
        bigSwordMulti = 1.5,
        -- 移速
        CD_fleetfoot = 10,
        moveSpMulti = 1.5,
        fx_craze_size = 0.125
    },

    -- 刀刃预加载几个
    bladePoolPreloadNum = 30,
    -- 小兵预加载几个，boss不预加载
    enemyPoolPreloadNum = 5,
    -- 道具预加载几个，boss不预加载
    propPoolPreloadNum = 5,
    -- 血条预加载几个
    hpSliderPreloadNum = 8,
    -- 伤害数字
    dmgPreloadNum = 15,

    -- 1刀刃顺时针/-1逆时针
    bladeSide = 1,
    -- 刀刃旋转速度
    bladeInitSpeed = 200,
    -- 敌人刀刃旋转速度
    bladeInitSpeed_Enemy = 200,
    -- 刀刃外扩半径
    bladeRadius = 1.1,
    -- 玩家发射技能时间
    attackTime = 2,
    -- 火球数量
    fireBallNum = 8,
    -- 刀刃道具新增刀刃数量
    newBladeNum = 5,
    -- 刀刃最大数量
    bladeMaxNum = 36,
    -- 小怪掉落的刀增加几个
    newBladeNumEnemy = 1,

    -- 层级配置
    sortOrder = {
        role_base = 50,
        role_display = 52,
        blade_display = 51,
        -- 角色底层的特效
        effect_base = -1,
        -- 角色上层的特效
        effect_fly = 100,
        -- 角色上层的特效2
        effect_fly2 = 200,
        -- 刀刃飞行时层级
        blade_fly = 100,
        blade_base = 0,
        prop_default = 0
    },

    -- 名字分隔符
    names = {
        split = "_",
        enemy = "Enemy",
        Player = "Player",
        Prop = "Prop",
        Effect = "Effect",
        Blade = "Blade"
    },

    triggerType = {
        role = 1,
        prop = 2,
        effect = 3,
        blade = 4
    },

    layerID = {
        ui = 12,
        -- 路障和主角
        collisionMap = 13,
        -- 敌人
        collisionEnemy = 14,
        -- 角色触发器
        triggerPlayer = 15,
        triggerEnemy = 16
    },
    -- 1普通刀刃2冰冻技能3火球技能4瘴气技能5闪电技能6狂暴7大宝剑8飞毛腿9爱心10小怪掉的刀
    propType = {
        blade_default = 1,
        blade_snow = 2,
        blade_fire = 3,
        blade_miasma = 4,
        blade_lightning = 5,
        craze = 6,
        bigSword = 7,
        moveSp = 8,
        heart = 9,
        blade_default2 = 10
    },
    -- 2冰冻技能3火球技能4瘴气技能5闪电技能。6敌人法师的光球，7Boss范围伤害,8boss范围毒气
    effectType = {
        snow = 2,
        fire = 3,
        miasma = 4,
        lightning = 5,
        enemyBall = 6,
        bossCircle = 7
    },

    MK_Hit = {
        playerMoveDis = 1,
        playerMoveDur = 0.15,
        enemyMoveDis = 1,
        enemyMoveDur = 0.15,
        fillColDur1 = 0.1,
        fillColDur2 = 0.15
    },

    roleDir = {
        forward = bc_CS_Vector3(1, 1, 1),
        back = bc_CS_Vector3(-1, 1, 1)
    },
    AnimatorName = "StateIndex",
    AnimatorState = {
        -- 走路
        walk = 0,
        -- 死亡
        die = 1,
        -- 攻击
        attack = 2,
        -- 入场
        entrance = 3,
        idle = 4
    },

    allAudioPath = "casualgame/bcmanyknives/prefab/allaudios.prefab",
    -- 音效列表
    AudioClips = {
        shengli = "shengli",
        return_blood = "return_blood",
        pick_knife = "pick_knife",
        monster_death = "monster_death",
        monster_death2 = "monster_death2",
        monster_dash = "monster_dash",
        lightning = "lightning",
        knife_fight = "knife_fight",
        diski_firlat = "diski_firlat",
        character_fireball = "character_fireball",
        boss_fireball = "boss_fireball",
        boss_fall = "boss_fall",
        boss_dash = "boss_dash",
        boss_appears = "boss_appears",
        bingdong = "bingdong",
        accelerate = "accelerate",
        hit = "hit",
        violent = "violent",
        Invincible = "Invincible",
        shibai = "shibai",
        boss_die = "boss_die",
        boss_die2 = "boss_die2",
        pick_props = "pick_props",
        BGM2 = "BGM2"
    },

    -- 配置表
    allDatasPath = "casualgame/bcmanyknives/prefab/alldatas.prefab",
    levelDataFormat = "level%d",
    -- 文案
    langPath = "casualgame/bcmanyknives/tablecsv/lang.csv",

    -- 技能预制
    allSkillPath = "casualgame/bcmanyknives/prefab/skill/allskill.prefab",
    skillNames = {
        role_smoke = "role_smoke",
        fx_xuanyun = "fx_xuanyun",
        fx_snow02 = "fx_snow02",
        fx_snow01 = "fx_snow01",
        fx_pindao = "fx_pindao",
        fx_miasma = "fx_miasma",
        fx_lightning01 = "fx_lightning01",
        fx_huoqudaoju = "fx_huoqudaoju",
        fx_huixue = "fx_huixue",
        fx_fire01 = "fx_fire01",
        fx_dimian = "fx_dimian",
        fx_chongci = "fx_chongci",
        fx_bingzhangjineng = "fx_bingzhangjineng",
        fx_bingzhang = "fx_bingzhang",
        fx_miasma_boss = "fx_miasma_boss",
        fx_juese_shouji = "fx_juese_shouji",
        fx_fire_boss = "fx_fire_boss",
        rushGuide = "rushGuide",
        fx_xiaoguaisiwang = "fx_xiaoguaisiwang",
        fanGuide = "fanGuide",
        circleGuide = "circleGuide",
        circleDmg = "circleDmg"
    },
    -- 刀刃的预制
    allBladePath = "casualgame/bcmanyknives/prefab/blade/allblade.prefab",
    bladeNames = {
        snowblade = "snowblade",
        miasmablade = "miasmablade",
        lightningblade = "lightningblade",
        fireblade = "fireblade",
        defaultblade = "defaultblade",
        ironblade = "ironblade",
        hugeAxe = "hugeAxe",
        hugeblade = "hugeblade"
    },
    -- 各种角色预制
    allRolePath = "casualgame/bcmanyknives/prefab/role/allrole.prefab",
    roleNames = {
        player = "player",
        onion = "onion",
        knight = "knight",
        gadiator = "gadiator",
        gadiator2 = "gadiator2",
        bingmodaoshi = "bingmodaoshi",
        dafeilong = "dafeilong",
        ironboss = "ironboss",
        axeboss2 = "axeboss2",
        axeboss = "axeboss",
        bladeboss = "bladeboss"
    },

    -- 各种道具预制
    allPropPath = "casualgame/bcmanyknives/prefab/prop/allprop.prefab",
    propNames = {
        defaultBladeProp = "defaultBladeProp",
        defaultBladeProp2 = "defaultBladeProp2",
        iceBladeProp = "iceBladeProp",
        fireBladeProp = "fireBladeProp",
        miasmaBladeProp = "miasmaBladeProp",
        lightningBladeProp = "lightningBladeProp",
        crazeProp = "crazeProp",
        fleetfootedProp = "fleetfootedProp",
        loveProp = "loveProp",
        swordProp = "swordProp"
    },

    allLevelPath = "casualgame/bcmanyknives/prefab/level/alllevel_%d.prefab",
    levelNameFormat = "level%d",

    mainScenePath = "casualgame/bcmanyknives/prefab/mainscene.prefab",
    mainPanelPath = "casualgame/bcmanyknives/prefab/mainpanel.prefab",

    fontARPath = "casualgame/bcmanyknives/art/font2.all",

    Func_Raycast = function(layerMask, from, to, rayLength, draw, color)
        if draw == nil then
            draw = false
        end
        if draw then
            if color == nil then
                color = CS.UnityEngine.Color.white
            end
            CS.UnityEngine.Debug.DrawRay(from, to * rayLength, color)
        end
        local hitInfos = Physics2D.RaycastAll(from, to, rayLength, layerMask)
        local hitLength = hitInfos.Length
        if hitLength > 0 then
            return hitInfos[0]
        end
        return nil
    end,
    --- 忽略两个碰撞体
    Func_IgnoreCollision = function(col1, col2)
        Physics2D.IgnoreCollision(col1, col2)
    end,
    Func_OverlapCircle = function(pos, radius, layerMark)
        return Physics2D.OverlapCircle(bc_CS_Vector2(pos.x, pos.y), radius, layerMark)
    end,
    Func_BezierCurveWithThreePoints = function(v1, v2, v3, vCount)
        local pointList = {}
        local dis = 0
        local tmpIndex = 0
        vCount = vCount and vCount or 5
        for i = 0, vCount, 1 do
            local ratio = i / vCount
            pointList[tmpIndex] = bc_CS_Vector3.Lerp(bc_CS_Vector3.Lerp(v1, v2, ratio),
                bc_CS_Vector3.Lerp(v2, v3, ratio), ratio)
            if tmpIndex > 0 then
                dis = dis + bc_CS_Vector3.Distance(pointList[tmpIndex - 1], pointList[tmpIndex])
            end
            tmpIndex = tmpIndex + 1
        end
        return pointList, dis
    end,
    Func_LineCircleIntersectionFactor = function(circleCenter, linePoint1, linePoint2, radius)
        local normalizedDirection = nil
        local tmpVec = (linePoint2 - linePoint1)
        local segmentLength = tmpVec.magnitude
        if segmentLength > 0.00001 then
            normalizedDirection = tmpVec / segmentLength
        else
            normalizedDirection = bc_CS_Vector2.zero
        end
        local dirToStart = linePoint1 - circleCenter
        local dot = bc_CS_Vector2.Dot(dirToStart, normalizedDirection)
        local discriminant = dot * dot - (dirToStart.sqrMagnitude - radius * radius)
        if discriminant < 0 then
            discriminant = 0
        end
        local t = -dot + math.sqrt(discriminant)
        return segmentLength > 0.00001 and (t / segmentLength) or 1
    end,
    --- 设置布局，限定宽/高
    --- @param text table UI.Text 
    --- @param axis number @指定轴：0水平，1垂直
    ---@param maxValue  number 指定轴最大值
    Func_SetTextLayoutWithFixed = function(text, axis, maxValue)
        local curValue = nil
        local unityAxis = nil
        if axis == 0 then
            curValue = text.preferredWidth
            unityAxis = UnityAxisHorizontal
        else
            curValue = text.preferredHeight
            unityAxis = UnityAxisVertical
        end
        if curValue > maxValue then
            curValue = maxValue
        end
        local nextAxis = nil
        text.rectTransform:SetSizeWithCurrentAnchors(axis, curValue)
        if axis == 0 then
            nextAxis = UnityAxisVertical
            curValue = text.preferredHeight
        else
            nextAxis = UnityAxisHorizontal
            curValue = text.preferredWidth
        end
        text.rectTransform:SetSizeWithCurrentAnchors(nextAxis, curValue)
    end
}

-- 1普通刀刃2冰冻技能3火球技能4瘴气技能5闪电技能6狂暴7大宝剑8飞毛腿9爱心,道具数量,
-- 敌人类型1洋葱头2骑士3角斗士4大乌龟5冰魔导士6宝箱怪7ironBoss8axeBoss9bladeBoss 10角斗士2,11axeboss2,
ManyKnivesDefine.roleTypeWithName = {ManyKnivesDefine.roleNames.onion, ManyKnivesDefine.roleNames.knight,
                                     ManyKnivesDefine.roleNames.gadiator, ManyKnivesDefine.roleNames.dafeilong,
                                     ManyKnivesDefine.roleNames.bingmodaoshi, nil, ManyKnivesDefine.roleNames.ironboss,
                                     ManyKnivesDefine.roleNames.axeboss, ManyKnivesDefine.roleNames.bladeboss,
                                     ManyKnivesDefine.roleNames.gadiator2, ManyKnivesDefine.roleNames.axeboss2}
-- 角色类型对应对象池
ManyKnivesDefine.roleTypeWithPoolFile = {"bcmanyknives_role_onion", "bcmanyknives_role_knight",
                                         "bcmanyknives_role_gadiator", "bcmanyknives_role_dafeilong",
                                         "bcmanyknives_role_bingmodaoshi", nil, "bcmanyknives_role_fireboss",
                                         "bcmanyknives_role_rushboss", "bcmanyknives_role_miasmaboss",
                                         "bcmanyknives_role_gadiator", "bcmanyknives_role_rush_retinue"}

ManyKnivesDefine.MapLayerMask = 2 ^ ManyKnivesDefine.layerID.collisionMap
ManyKnivesDefine.Physics2D_Raycast = CS.UnityEngine.Physics2D.Raycast
ManyKnivesDefine.OverlapCircle = CS.UnityEngine.Physics2D.OverlapCircle
