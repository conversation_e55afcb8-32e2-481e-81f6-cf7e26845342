local manyknives_role_boss = TinyRush_CreateClass("bcmanyknives_role_boss"):baseClass(require(
    "bcmanyknives_rolebase_item"))
manyknives_role_boss.actionFlag = nil
-- Boss技能计数相关配置
local skill_Config = {
    -- 火球
    CD_fire = 0.8,
    CD_fire2 = 0.05,
    -- 毒气
    CD_miasma = 2,
    -- 毒气存在时长
    miasma_Stay = 10,
    -- 冲刺
    CD_rush = 3,
    -- 冲刺速度
    rush_MoveSpeed = 10,
    -- 冲刺多远
    rush_Limit = 15,
    -- 冲刺提前1.5秒定位方向
    rush_DirTimerOff = 1.5,
    -- 冲刺指引得高度
    rushGuide_height = 3.6
}
-- 入场演出
manyknives_role_boss.cgTween = nil
manyknives_role_boss.initPos = nil

function manyknives_role_boss:ctor(...)
    self.__base:ctor(...)
end

function manyknives_role_boss:Init(...)
    self.__base:Init(...)
    _, _, _, _, self.initPos = ...
    self.isBoss = true
    self.s_interval = self.roleData.callTime
    self.callNum = self.roleData.callNum
    self.callEnemyType = self.roleData.callEnemyType
    self.callLevel = self.roleData.callLevel
    local bladeType = nil
    if self.roleData.roleName == ManyKnivesDefine.roleNames.axeboss then
        bladeType = 7
    elseif self.roleData.roleName == ManyKnivesDefine.roleNames.ironboss then
        bladeType = 6
    else
        bladeType = 8
    end
    self:InitBlade(bladeType, self.roleData.bladeNum)
    self.spawnTimer = self.s_interval
    -- 行动标记
    self.actionFlag = false
    self.fxTimer = 0
    self.fxTimer2 = 0
    self.fxTimerCD = nil
    -- 技能类型1冲刺2火球3毒瘴
    if self.roleData.skillType == 1 then
        self.fxTimerCD = skill_Config.CD_rush
    elseif self.roleData.skillType == 2 then
        self.fxTimerCD = skill_Config.CD_fire
        self.fxTimerCD2 = skill_Config.CD_fire2
    elseif self.roleData.skillType == 3 then
        self.fxTimerCD = skill_Config.CD_miasma
    end
    self.bladeFxFlag2 = 0
end

function manyknives_role_boss:PauseListener(pause)
    self.__base:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.cgTween ~= nil then
        self.cgTween.timeScale = timeScale
    end
end

function manyknives_role_boss:Start()
    self.__base:Start()
    self:EntrancePlay(self.initPos)
end

function manyknives_role_boss:Ready()
    self.fixedlyFlag = false
    self.sceneMgr:RoleNoInjury(false)
    self.actionFlag = true
end

function manyknives_role_boss:EntrancePlay(initPos)
    self:KillCGTween()
    self.sceneMgr:RoleNoInjury(true)
    self.fixedlyFlag = true
    self:SetAICanMove(false)
    self.actionFlag = false
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.entrance)
    self.cgTween = ManyKnivesDefine.DOTween.Sequence()
    self.cgTween:InsertCallback(0.15, function()
        ManyKnivesDefine.UnityTime.timeScale = 0.15
    end)
    self.cgTween:InsertCallback(0.24, function()
        ManyKnivesDefine.UnityTime.timeScale = 1
        self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.boss_fall)
    end)
    self.cgTween:InsertCallback(0.3, function()
        self.sceneMgr.cameraCtrl:Shake_BossEntrance()
        local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_dimian)
        fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_dimian)
        fx:Play(1, initPos, 2)
        self.sceneMgr:PlayerRepulse(initPos)
    end)
    self.cgTween:InsertCallback(1.2, function()
        self.__entity:Ready()
    end)
    self.cgTween:SetAutoKill(true)
    self:RefreshPause()
end

function manyknives_role_boss:KillCGTween()
    if self.cgTween ~= nil then
        self.cgTween:Kill()
        self.cgTween = nil
    end
end

function manyknives_role_boss:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    if self.deadFlag or not self.actionFlag then
        return
    end
    -- 召唤小怪
    self.spawnTimer = self.spawnTimer + deltaTime
    if self.spawnTimer >= self.s_interval then
        self.spawnTimer = 0
        self.sceneMgr:SpawnEnemyPrefab(self.callNum, self.callEnemyType, self.callLevel)
    end
end

function manyknives_role_boss:deaded()
    self.__entity:recycle()
    self.__base:deaded()
end

function manyknives_role_boss:recycle()
    self:KillCGTween()
    self.__base:recycle()
end
function manyknives_role_boss:dispose()
    self.__entity:recycle()
    self.__base:dispose()
end

return manyknives_role_boss
