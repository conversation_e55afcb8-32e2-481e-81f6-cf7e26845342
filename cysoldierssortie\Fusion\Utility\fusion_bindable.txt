---@class fusion_bindable 数据监听对象，数据改变时会触发回调。【数据类型不限】
local bindable = bc_Class("fusion_bindable")
local bindableUnRegister = require("fusion_bindableUnRegister")
bindable.value = nil
bindable.callback = nil

function bindable:__init(...)
    self.value = ...
    self.callback = {}
end

function bindable:__delete()
    self.value = nil
    self.callback = nil
end

--- 强制执行一次回调
function bindable:InvokeEvent()
    for _, v in ipairs(self.callback) do
        v(self.value)
    end
end

--- 改变值,并执行回调
---@param v any
---@param withoutEvent boolean 是否忽略回调
function bindable:SetValue(v, withoutEvent)
    if withoutEvent == nil then
        withoutEvent = false
    end
    if self.value ~= v then
        self.value = v
        if not withoutEvent then
            for _, v in ipairs(self.callback) do
                v(self.value)
            end
        end
    end
end

--- 绑定事件
---@param onValueChanged fun() 改变值后回调
---@param withInitValue boolean 可选择是否立刻执行一次回调
---@return fusion_bindableUnRegister
function bindable:Register(onValueChanged, withInitValue)
    self.callback[#self.callback + 1] = onValueChanged
    if withInitValue == nil then
        withInitValue = false
    end
    if withInitValue then
        onValueChanged(self.value)
    end
    return bindableUnRegister.New(self, onValueChanged)
end

--- 移除事件
---@param func fun()
function bindable:UnRegister(func)
    table.remove_value(self.callback, func)
end

return bindable
