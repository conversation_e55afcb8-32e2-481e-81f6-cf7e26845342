local cysoldierssortie_comp_character = bc_Class("cysoldierssortie_comp_character") --类名用小游戏名加后缀保证全局唯一
local Vector3 = CS.UnityEngine.Vector3
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local Quaternion = CS.UnityEngine.Quaternion
local require = require
local cysoldierssortie_config_character_comps = require(cysoldierssortie_TroopClash and "troopclash_config_character_comps" or (cysoldierssortie_KingShot and "kingshot_config_character_comps") or "cysoldierssortie_config_character_comps")
local cysoldierssortie_config_feedback = require("cysoldierssortie_config_feedback")
local math = math
local GCPerf = GCPerf
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local KillTimer = cysoldierssortie_KillTimer
local bc_IsNotNull = bc_IsNotNull
local SetTransformPositionByTransform = ApiHelper.SetTransformPositionByTransform
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_character_state = cysoldierssortie_character_state
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local cysoldierssortie_comp_name = cysoldierssortie_comp_name
local LookAtTargetSystem = CS.cysoldierssortie.LookAtTargetSystem
local LookAtTargetSystemInstance
local minigame_buff_mgr= require "minigame_buff_mgr"
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local bc_Time = bc_Time
local cysoldierssortie_common_unit_prop = cysoldierssortie_common_unit_prop
local log = log
local cysoldierssortie_CharacterOtherEffect = cysoldierssortie_CharacterOtherEffect
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp

function cysoldierssortie_comp_character.__init(self)
    local res = xpcall(function()
        LookAtTargetSystemInstance = LookAtTargetSystem.Instance
    end,debug.traceback)
end


function cysoldierssortie_comp_character:CreateData(data)
    --self._damageCoefficient = data.DamageCoefficient
    self._initAttack=data.Attack
    self._attack = data.Attack

    if data.UnitType and (data.UnitType == cysoldierssortie_unit_type.Soldier or data.UnitType == cysoldierssortie_unit_type.Soldier2) and data.AtkIncreaseRatioByLevel then
        self._attack = self._attack +  self._attack * (data.AtkIncreaseRatioByLevel/100)
    end
    self._attackRange = data.AttackRange
    self._hp = data.HP
    self._heroId = data.HeroID
    self._star = data.Star or 0
    self._curHp = self._hp
    self._character_entity = self._character_entity
    self._position = Vector3.zero
    self.transform = self.transform
    self._characterState = self._characterState or cysoldierssortie_character_state.Idle
    self._player = data.player
    self._targetObjs = self._targetObjs
    self._weaponRoot = self._weaponRoot
    self._weaponSpawnPoint = self._weaponSpawnPoint
    self._unit_type = data.UnitType
    self._moveSpeed = data.MoveSpeed
    self._enemyRange = data.EnemyRange
    self.comps = self.comps or {}
    self._localPos = data.LocalPos
    self._unitID = data.UnitID
    self._bloodOffset = data.BloodOffset
    self._level = data.Level or 1
    if self._unit_type == cysoldierssortie_unit_type.Drone then--神兽
        self._isDrone=true
    else
        self._isDrone=false
    end

    if self._unit_type == cysoldierssortie_unit_type.Building then--建筑
        self._isBuilding=true
    else
        self._isBuilding=false
    end

    self._targetGo = nil
    if data.Scale then
        self._scale = data.Scale > 0 and data.Scale or 1
    end

    --即使是英雄，通过道具获得也算在士兵集合中
    self._lst_group_type = data.LstGroupType
    self._hitDeceleration = data.HitDeceleration
    
    --当前实体是否显示
    self._view_actor = true
    
    --唯一sid 先随机写
    self._sid = data.Sid or  math.random(1000, 100000)
    
    self._weapons = data.Weapons
    
    self._disToPlayerZ = 1000
    
    self._curAnimState = nil
    
    self._skillReleaseTrans = nil
    
    --冻结旋转,某些情况下 比如释放某种技能此时角色的旋转不允许转向
    self._freezeRotate = false
end

function cysoldierssortie_comp_character:SetFreezeRotate(freeze)
    self._freezeRotate = freeze
end

function cysoldierssortie_comp_character:GetStarLvAddCoefficient(weaponData)
    if not self._weaponAddCoeByStarLvs then
        self._weaponAddCoeByStarLvs = {}
    end
    
    if  self._weaponAddCoeByStarLvs[weaponData._skillID] then
        return self._weaponAddCoeByStarLvs[weaponData._skillID]
    end
    local coe  = nil
    if weaponData._starRatingIncrease then
        if self.heroData then
            local starLv =   math.floor(self.heroData.numProp.starLv / 5)
            if starLv then
                local array = weaponData._starRatingIncrease.data
                if array and array[starLv] and #array >= starLv then
                    coe = array[starLv] / 10000
                    if coe then
                        self._weaponAddCoeByStarLvs[weaponData._skillID] = coe
                    end 
                end
            end
        end
    end
    return coe
end

function cysoldierssortie_comp_character:CreateEntity(parent,playEffect)
    --local character_go = NeeGame.PoolObject(cysoldierssortie_PoolObjectName.Hero, parent)
   -- character_go.transform.localRotation = Quaternion.identity
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if not poolMgr then
        return
    end
    self._character_entity = poolMgr:AcquireObj("cysoldierssortie_comp_character_entity",parent)
    self._character_entity:SetCharacter(self)
    self._skillReleaseTrans = self._character_entity._releaseSkillPoint.transform
    self._character_entity.transform.localPosition = self._localPos
    self.transform = self._character_entity.transform
    if playEffect then
        self._character_entity:PlayNewGetEffect()
    end
    
    --添加组件
    local unit_type = self._unit_type
    local comps =  cysoldierssortie_config_character_comps.UnitTypeCompsConfig[unit_type]
    if comps then
        for i=1,#comps do
            cysoldierssortie_config_character_comps.CharacterCompsConfig[comps[i]]._addComp(self)
        end
    end
end

function cysoldierssortie_comp_character:SetPosAndParent(pos,parent)
    self._localPos = pos
    if not self._character_entity then
        return
    end

    self._character_entity.transform:SetParent(parent)
    self._character_entity.transform.localPosition = pos
end

function cysoldierssortie_comp_character:GetPosition()
    if self._character_entity then
        return self._character_entity.transform.position
    end
end

function cysoldierssortie_comp_character:LevelUp(newLevel,isPlayEffect)
    local levelComp = self.comps[cysoldierssortie_comp_name.Level]
    if levelComp then
        levelComp:LevelUp(newLevel,isPlayEffect)
    end
end

function cysoldierssortie_comp_character:GetFsmState()
    local fsmComp = self.comps[cysoldierssortie_comp_name.AI]
    if fsmComp then
       return fsmComp._currentState
    end
    return cysoldierssortie_character_state.Idle
end

function cysoldierssortie_comp_character:ReplaceEntity(newLevel)
    if self._level >= 5 then
        return
    end
    
    self:RemoveComponent(cysoldierssortie_comp_name.Attack)

    if self._character_entity then
        --self._character_entity:ReleaseModel()
    end

    if not newLevel then
        self._level = self._level+1
    else
        self._level = newLevel
    end

    if self._unit_type == cysoldierssortie_unit_type.Hero or self._isDrone then

        local newUnitID =self._player:GetUnitIDByHeroID(self._heroId,self._level)
        if newUnitID then
            self._unitID=newUnitID
        end

    else
        local level_mgr =  cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        if level_mgr then
            local curLevel = level_mgr.curLevel
            if curLevel then
                self._unitID = curLevel:GetUseSoldier(self._level)
            end
        end
    end


    if self._player then
        local actor_instance_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
        local newData = actor_instance_mgr:CreateCharacterData(self._unitID,nil,nil,nil,nil,self._player)
        newData.LocalPos = self._localPos
        self:CreateData(newData)
        self._player:CreateAttackRange(self._unitID,self._attackRange)
    end
    
    if self._character_entity then
        --self._character_entity:CreateModel()
    end

    local attack_comp = self:AddComponent(cysoldierssortie_comp_name.Attack, { character = self })
    
    if self:GetFsmState()==cysoldierssortie_character_state.Attack then
        local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
        if attackMgr:EnableProxy(self._unitID,self._unit_type) then
            if attackMgr then
                attackMgr:RegisterAttackCompProxy(attack_comp)
            end
        end
    end
    self:ResetCooldown()
end

function cysoldierssortie_comp_character:SetViewActor(state)
    self._view_actor = state
    if state then
        local autoDeadTime = 40
        if self._unit_type == cysoldierssortie_unit_type.NormalEnemy then
            --self._autoDelayDeadTimer =  cysoldierssortie_DelayCallOnce(autoDeadTime,function()
            --    self:Dead()
            --end)
        end
        local entity = self._character_entity
        if entity then
            if not entity._cull then
                if bc_IsNotNull(entity.modelGo) then
                    entity.modelGo:SetActive(true)
                end
            end
        end
    end
end

function cysoldierssortie_comp_character:AddMaxHp(addNum)
    --log.Error("Before hp:"..self._curHp.." MaxHp:"..self._hp)
    self._curHp = self._curHp + addNum
    self._hp = self._hp + addNum
    --log.Error("After hp:"..self._curHp.." MaxHp:"..self._hp)
end

function cysoldierssortie_comp_character:BossHpDynamicAddOnce()
    if not (self._unit_type == cysoldierssortie_unit_type.BossEnemy) then
        return
    end

    if not self._hpDynamic then
        local levelMgr  = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        local curLevel = levelMgr.curLevel
        local player = curLevel.playerLua
        if player then
            local allSoldierDps =  player:GetAllSoldierDps()
            self:AddMaxHp(allSoldierDps)
        end
        self._hpDynamic = true
    end
end

local feedBackData = {}
local feedBackUse = nil
function cysoldierssortie_comp_character:SkillUltraCamShakeFeedBack()
    local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
    if feedComp then
        if not feedBackUse or feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.SKILL_ULTRA then
            feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.SKILL_ULTRA]
            feedComp:CreateData(feedBackData)
        end
        feedComp:StartFeedBack()
    end
end

function cysoldierssortie_comp_character:SoldierBeHitFeedBack()
    local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
    if feedComp then
        if not feedBackUse or feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.SOLDIER_BEHIT then
            feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.SOLDIER_BEHIT]
            feedComp:CreateData(feedBackData)
        end
        feedComp:StartFeedBack()
    end
end

function cysoldierssortie_comp_character:CreateSkillCountDownUI()
    local hpComp = self.comps[cysoldierssortie_comp_name.HP]
    if hpComp then 
        local hpRoot = hpComp._hpSliderParent
        if not bc_IsNotNull(hpRoot) then
            return false
        end
        local skillCountDownRoot = hpRoot:Find("Avatar")
        if skillCountDownRoot then
            skillCountDownRoot:SetActive(true)
            return skillCountDownRoot
        end
    end
    return false
end

function cysoldierssortie_comp_character:CreateSkillUltraShow()
    local attackComp = self.comps[cysoldierssortie_comp_name.Attack]
    if attackComp then
        attackComp:CreateSkillUltraShow()
    end
end

--受惊行为
function cysoldierssortie_comp_character:SetScared()
    local moveComp = self.comps[cysoldierssortie_comp_name.Move]
    if moveComp then
        moveComp:SetScared()
    end
end

function cysoldierssortie_comp_character:BeHit(attack,isCritical,takeDamagePointX,takeDamagePointY,takeDamagePointZ,skillView)
    if self._isDrone then
        return
    end
    attack=minigame_buff_mgr.GetSkillBuffAttribute(minigame_buff_mgr.BuffType.ReduceHarm,self,attack)
    local newAttack=attack
    local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    if  not self._player then
        newAttack =  levelMgr:GetAttackLimit(attack)
    else
        newAttack=  levelMgr:GetHpLimit(attack)
    end

    -- 敌人受伤
    if cysoldierssortie_TroopClash and not self._player then
        levelMgr:EnemyBeHit(self)
    end

    -- 敌人受伤
    if cysoldierssortie_KingShot and not self._player then
        levelMgr:EnemyBeHit(self)
    end

    self:BossHpDynamicAddOnce()
    self._curHp = self._curHp - newAttack
    local minigame_mgr = require "minigame_mgr"
    minigame_mgr.AddHeroHitHurt(self,attack)

    if self._curHp <= 0 and  self._characterState ~= cysoldierssortie_character_state.Die then
        self._curHp = 0
        self:Dead()
    else
        minigame_buff_mgr.CheckCondition(self,minigame_buff_mgr.ConditionType.BeHit)
        local buff = minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.Scared,self)
        if buff then
            self:SetScared()    
        end
        if self._autoDelayDeadTimer then
            KillTimer(self._autoDelayDeadTimer)
        end
        
        --受击减速
        local moveComp =  self.comps[cysoldierssortie_comp_name.Move]
        if moveComp then
            moveComp:ChangeMoveSpeed(cysoldierssortie_common_unit_prop.DecelerationTime,cysoldierssortie_common_unit_prop.DecelerationPercent)
        end
        
        --反馈系统
        if not self._player then
            local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
            if feedComp then
                if not feedBackUse or feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT then
                    feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT]
                    feedComp:CreateData(feedBackData)
                end
                feedComp:StartFeedBack()
            end
        else
            self:SoldierBeHitFeedBack()
        end
    end
    
    if not self.comps[cysoldierssortie_comp_name.HP] then
        return
    end
    if not self._view_actor then
        return 
    end
    
    local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
    if hp_comp then
        hp_comp:SpawnHp()
        hp_comp:SpawnHpNumAniText(attack,isCritical,takeDamagePointX,takeDamagePointY,takeDamagePointZ,1,skillView)
    end
end

function cysoldierssortie_comp_character:SpawnHp()
    if self._player then
        local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
        if hp_comp then
            hp_comp:SpawnHp()
        end
    end
end

local fakeHpAnimMaxNum = 2
local spawnInterval = 0.0333
function cysoldierssortie_comp_character:FakeSpawnHpAnim(sumDamage,attack)
    if not self._fakeSpawnTimer or bc_Time.time> self._fakeSpawnTimer then
        self._fakeSpawnTimer = spawnInterval + bc_Time.time
        local hpComp = self.comps[cysoldierssortie_comp_name.HP]
        if hpComp then
            local spawnCount = math.floor(sumDamage/attack)
            spawnCount = math.min(fakeHpAnimMaxNum,spawnCount)
            local x,y,z = GetTransformPositionXYZ(self._skillReleaseTrans)
            for i=1,spawnCount do
                hpComp:SpawnHpNumAniText(attack,false,x,y,z,2)
            end
        end
    end
end

function cysoldierssortie_comp_character:GetAnimator()
    if not self._character_entity then
        return nil
    end
    if not self._character_entity._animator then
        return nil
    end
    return self._character_entity._animator
end

function cysoldierssortie_comp_character:RefreshAttackInterval()
    local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
    local releaseSkillTime,animSpeed,loopInterval = attackMgr:GetReleaseSkillTime(self._unitID,self._heroId)
    if not loopInterval then
        return false
    end
    
    if not self._animator then
        self._animator = self:GetAnimator()
    end
    self._animator:SetAttackLoopInterval(true,0)
    cysoldierssortie_DelayCallOnce(bc_Time.deltaTime,function()
        self._animator:SetAttackLoopInterval(true,loopInterval)
    end)
    return true
end

function cysoldierssortie_comp_character:PlayAnim(state)
    if self._curAnimState == state then
        return
    end
    if not self._animator then
        self._animator = self:GetAnimator()
    end

    if state == cysoldierssortie_hero_anim_set.Ability then
        local refresh = self:RefreshAttackInterval()
        if refresh then
            self._curAnimState = state
            return
        end
    end
    if self._animator then
        self._animator:SetTrigger(state)
        self._curAnimState = state
    end
end

local showAnimCD = 10
function cysoldierssortie_comp_character:PlayShowAnimClip()
    if self._unit_type == cysoldierssortie_unit_type.BossEnemy then
        if minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.UnableToMove,self) then
            if not self._animShowCD or self._animShowCD <= bc_Time.time then
                self._animShowCD = bc_Time.time + showAnimCD
                self:PlayAnim(cysoldierssortie_hero_anim_set.Show) 
            end
        end
    end
end

function cysoldierssortie_comp_character:ResetAnim()
    --动画逻辑
    if not self._animator then
        self._animator = self:GetAnimator()
    end
    
    if self._animator then
        self._animator:ResetTrigger(cysoldierssortie_hero_anim_set.Ability)
        self._curAnimState = cysoldierssortie_hero_anim_set.Stand
    end
end

function cysoldierssortie_comp_character:CreateNavAgent()
    local moveComp =  self.comps[cysoldierssortie_comp_name.Move]
    if moveComp then
        moveComp:CreateNavAgent()
    end
end

function cysoldierssortie_comp_character:Patrol()
    local moveComp =  self.comps[cysoldierssortie_comp_name.Move]
    if moveComp then
        moveComp:Patrol()
    end
end

function cysoldierssortie_comp_character:InitAnimState()
    local fsmState = self:GetFsmState()
    if fsmState == cysoldierssortie_character_state.Move then
        self:PlayAnim(cysoldierssortie_hero_anim_set.Run)
    elseif fsmState == cysoldierssortie_character_state.Attack then
        if self._character_entity._gpu_anim then
            local animator = self._character_entity._animator
            if animator then
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                local releaseSkillTime,animSpeed,loopInterval =  attackMgr:GetReleaseSkillTime(self._unitID,self._heroId)
                if animSpeed then
                    animator:SetAnimatorSpeed(animSpeed)
                end
                if loopInterval then
                    animator:SetAttackLoopInterval(true,loopInterval)
                end
            end
        end
        self:PlayAnim(cysoldierssortie_hero_anim_set.Ability)
    end
end

function cysoldierssortie_comp_character:Dead(anim,recyclePos)
    recyclePos = recyclePos == nil and true or recyclePos

    if self._autoDelayDeadTimer then
        KillTimer(self._autoDelayDeadTimer)
    end
    self._characterState = cysoldierssortie_character_state.Die


    local unit_type = self._unit_type
    local comps =  cysoldierssortie_config_character_comps.UnitTypeCompsConfig[unit_type]
    if comps then
        for i=1,#comps do
            self:RemoveComponent(comps[i])
        end
    end

    if self._player then
        self._player:OnHeroDead(self)
        if recyclePos then
            self._player:RecycleSoldierPos(self._localPos,self.transform.parent)
        end
    else
        local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
        if attackMgr then
            attackMgr:RemoveDamageBatch(self)
        end
        self:RemoveComponent(cysoldierssortie_comp_name.EarlyWarning)
        self:PlayBombBlood()
        local level_mgr =  cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        if level_mgr then
            local curLevel = level_mgr.curLevel
            if curLevel then
                curLevel.playerLua:RemoveRangeEnemy(self._character_entity.gameObject)
                curLevel:RaduceEnemyCount(1,self)
            end
        end
        
        local actor_instance_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
        if actor_instance_mgr then
            actor_instance_mgr:DequeueActor(self)
        end
    end

    self._character_entity:Dead(anim)
    if self._player then
        local res = xpcall(function()
            LookAtTargetSystemInstance:UnregisterSoldier(self.transform)
        end, debug.traceback)
    else
        local res = xpcall(function()
            LookAtTargetSystemInstance:UnregisterEnemey(self.transform)
        end, debug.traceback)
    end
    self.isDelKey=true
    minigame_buff_mgr.DelAllCharacterBuff(self)
end

--无限远索敌
function cysoldierssortie_comp_character:UnLimitRangeLookAtTarget()
    if self._player then
        local res = xpcall(function()
            LookAtTargetSystemInstance:UnregisterSoldier(self.transform)
            LookAtTargetSystemInstance:RegisterSoldier(self.transform,1000)
        end, debug.traceback)
    end
end

function cysoldierssortie_comp_character:GetTargetGo()
    local res = false
    if self._player then
        res = xpcall(function()
            self._targetGo = LookAtTargetSystemInstance:GetSoldierTarget(self.transform)
        end,debug.traceback)
    else
        res = xpcall(function()
            self._targetGo = LookAtTargetSystemInstance:GetEnemyTarget(self.transform)
        end,debug.traceback)
    end
    return self._targetGo
end

local EffectParamCache = {}
function cysoldierssortie_comp_character:PlayBombBlood()
    local effect_path = cysoldierssortie_CharacterOtherEffect.EnemyBombBloodEffect
    EffectParamCache.auto_release = true
    EffectParamCache.delay_release = 2.5
    EffectParamCache.effect_path = effect_path
    EffectParamCache.callBack = function(go)
        if bc_IsNotNull(self.transform) then
            SetTransformPositionByTransform(go.transform,self.transform)
        end
    end
    EffectParamCache.maxWeightLimit = true
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
    effect_mgr:CreateEffect(EffectParamCache)
end

function cysoldierssortie_comp_character:UpdateHp()
    if not self.comps[cysoldierssortie_comp_name.HP] then
        return
    end
    if self._curHp<=0 then
        return
    end
    if not self._player and self._curHp >= self._hp then
        return
    end

    if not self._character_entity then
        return
    end
    self._hpPoint = self._hpPoint or self._character_entity._hpPoint
    local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
    if hp_comp then
        if GCPerf then
            local pX, pY, pZ = GetTransformPositionXYZ(self._hpPoint.transform)
            hp_comp:UpdateXYZ(pX, pY, pZ,self._curHp,self._hp,self._curHp)
        else
            hp_comp:Update(self._hpPoint.transform.position,self._curHp,self._hp,self._curHp)
        end
    end
end

function cysoldierssortie_comp_character:UpdateAI()
    if not self.comps[cysoldierssortie_comp_name.AI] then
        return
    end

    local ai_comp = self.comps[cysoldierssortie_comp_name.AI]
    if ai_comp then
        ai_comp:Update()
    end
end

function cysoldierssortie_comp_character:Fire()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    attack_comp:Fire()
end

function cysoldierssortie_comp_character:GetAttackComp()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return nil
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    return attack_comp
end

function cysoldierssortie_comp_character:StopFire()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    attack_comp:StopFire()
end

function cysoldierssortie_comp_character:Move(position)
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:Move(position)
end

function cysoldierssortie_comp_character:MoveXYZ(px, py, pz)
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:ExeMove(px, py, pz)
end

function cysoldierssortie_comp_character:UpdateEarlyWaning()
    if not self.comps[cysoldierssortie_comp_name.EarlyWarning] then
        return
    end
    local early_waning = self.comps[cysoldierssortie_comp_name.EarlyWarning]
    early_waning:UpdateWaningView()
end

function cysoldierssortie_comp_character:StopMove()
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:StopMove()
end

function cysoldierssortie_comp_character:StartMove()
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:StartMove()
end

function cysoldierssortie_comp_character:AttackCountdown()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return 0
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    return attack_comp:Countdown()
end

--添加一个组件
function cysoldierssortie_comp_character:AddComponent(compName,data)
    if self.comps[compName] then
        return
    end
    local comp =  require(cysoldierssortie_comp_register[compName])
    local comp_instance = comp.New(data)
    self.comps[compName] = comp_instance
    return self.comps[compName]
end

function cysoldierssortie_comp_character:RemoveComponent(compName)
    if self.comps[compName] then
        local comp_instance =  self.comps[compName]
        comp_instance.Delete(comp_instance)
        self.comps[compName] = nil
    end
end

function cysoldierssortie_comp_character:ResetCooldown()
    if not self.comps[cysoldierssortie_comp_name.AI] then
        return 0
    end
    local fsm_comp = self.comps[cysoldierssortie_comp_name.AI]
    fsm_comp:ChangeState(cysoldierssortie_character_state.Idle)
end

return cysoldierssortie_comp_character