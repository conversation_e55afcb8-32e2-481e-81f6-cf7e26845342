local manyknives_role_gadiator = TinyRush_CreateClass("bcmanyknives_role_gadiator"):baseClass(require(
    "bcmanyknives_rolebase_item"))

-- 出生点设置
function manyknives_role_gadiator:Init(...)
    self.__base:Init(...)
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
end
function manyknives_role_gadiator:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    self:MoveFollow(deltaTime)
end

return manyknives_role_gadiator
