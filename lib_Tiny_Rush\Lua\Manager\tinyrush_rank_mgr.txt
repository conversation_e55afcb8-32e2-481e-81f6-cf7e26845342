---@class tinyrush_rank_mgr : <PERSON><PERSON><PERSON>_Scope,<PERSON>Rush_IInit,TinyRush_IUpdate
local rankMgr = TinyRush_CreateClass("tinyrush_rank_mgr"):baseClass(TinyRush_Scope):interface(TinyRush_IInit,
    TinyRush_IUpdate)
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local puzzleMgr = require("puzzlegame_mgr")
local dkjson = require("dkjson")
local localDefaultID = -1
local localDefaultName = "Me"
local fakeRankReqTime = 1
-- 请求服务器冷却时间
local serverReqTimeCD = 0.5

rankMgr.entrance = nil
rankMgr.gameData = nil
rankMgr.level = nil
rankMgr.saveKey = nil
--- 本地数据，只包含本关的数据
rankMgr.localDataCache = nil
--- 服务器数据，只包含本关的数据
rankMgr.serverDataCache = nil
--- 排序类型: 降序是0，其它都是升序，接收第一次输入的值，不更新
rankMgr.sortType = nil
--- 最大显示数量,不传默认20，大于100默认100，接收多个不同的值，可更新
rankMgr.maxNum = nil
--- 是否开启假排名，不传默认false
rankMgr.fakeRankFlag = nil
rankMgr.fakeRankMin = nil
rankMgr.fakeRankMax = nil
rankMgr.fakeRequest = nil
rankMgr.fakeRequestTimer = nil
--- 服务器请求标志位，防止多次请求服务器数据
rankMgr.serverReqFlag = nil
rankMgr.serverReqTimer = nil

--- 构造：传入 entrance, sortType, maxNum
---@param entrance table 小游戏入口tinyrush_entrance
---@param sortType number 排序类型: 降序是0，其它都是升序，接收第一次输入的值，不更新
---@param maxNum number 最大显示数量,不传默认20，大于100默认100，接收多个不同的值，可更新
---@param fakeRankFlag boolean 是否开启假排名，不传默认false。【只用作测试用，不能作为功能使用】
---@param fakeRankMin number 假排名分数最小值：初始化随机用
---@param fakeRankMax number 假排名分数最大值：初始化随机用
function rankMgr:ctor(...)
    self.entrance, self.sortType, self.maxNum, self.fakeRankFlag, self.fakeRankMin, self.fakeRankMax = ...
    self.sortType = type(self.sortType) == "number" and self.sortType or 0
    self.maxNum = type(self.maxNum) == "number" and math.min(self.maxNum, 100) or 20
    self.fakeRankFlag = type(self.fakeRankFlag) == "boolean" and self.fakeRankFlag or false
    self.fakeRankMin = type(self.fakeRankMin) == "number" and self.fakeRankMin or 0
    self.fakeRankMax = type(self.fakeRankMax) == "number" and self.fakeRankMax or self.maxNum
    self.level = self.entrance.level
    self.gameData = self.entrance:GameData()
    self.saveKey = string.format("%s_%d_Rank_Key", self.gameData.name, self.level)
    self.__base:ctor()
end

function rankMgr:rush_OnInit()
    self.localDataCache = nil
    self.serverDataCache = nil
    self.fakeRequestTimer = 0
    self.fakeRequest = nil
    self.serverReqFlag = true
    self:LoadLocalData()
    if self.fakeRankFlag then
        self.serverDataCache = {}
        for i = 1, self.maxNum, 1 do
            self.serverDataCache[i] = self:NewDataTable(i, math.floor(math.random(self.fakeRankMin, self.fakeRankMax)),
                "Faker_" .. tostring(i))
        end
        self:Quicksort(self.serverDataCache, 1, self.maxNum)
    end
end

function rankMgr:rush_OnUpdate(deltaTime)
    -- 假数据 模拟异步回调
    if self.fakeRankFlag then
        if self.fakeRequest ~= nil then
            self.fakeRequestTimer = self.fakeRequestTimer + deltaTime
            if self.fakeRequestTimer >= fakeRankReqTime then
                TinyRush_Log(self.__fullName .. "【假】排名数据获取结果：")
                if TinyRush_ShowLog and self.serverDataCache ~= nil then
                    for _, v in pairs(self.serverDataCache) do
                        TinyRush_Log("id:" .. v[1] .. " score:" .. v[2] .. " name:" .. v[3])
                    end
                end
                if self.fakeRequest.callback ~= nil then
                    self.fakeRequest.callback(self:GetServerData())
                end
                self.fakeRequest = nil
            end
        end
    else
        if not self.serverReqFlag then
            self.serverReqTimer = self.serverReqTimer + deltaTime
            if self.serverReqTimer >= serverReqTimeCD then
                self.serverReqFlag = true
            end
        end
    end
end

--- 主动请求服务器数据: 异步。可以在游戏开始前调用
---@param num number 获取前几个数据
---@param callback function
function rankMgr:LoadServerData(num, callback)
    if self.fakeRankFlag then
        local tmpLocalData = self:GetLocalData()
        if tmpLocalData ~= nil then
            self:InsertToArray(self.sortType, self.serverDataCache, tmpLocalData)
        end
        self.fakeRequestTimer = 0
        self.fakeRequest = {}
        self.fakeRequest.callback = callback
    else
        if self.serverReqFlag and puzzleMgr ~= nil and puzzleMgr.Get_Tiny_Rank_REQ ~= nil then
            self.serverReqFlag = false
            self.serverReqTimer = 0
            puzzleMgr.Get_Tiny_Rank_REQ(self.gameData.type, self.level, 0, math.max(0, num - 1), function(msg)
                TinyRush_Log(self.__fullName .. "排名数据获取结果：" .. tostring(msg))
                if TinyRush_ShowLog and msg.rankList ~= nil then
                    for _, v in pairs(msg.rankList) do
                        TinyRush_Log("id:" .. v[1] .. " score:" .. v[2] .. " name:" .. v[3])
                    end
                end
                self.serverDataCache = msg.rankList
                if callback ~= nil then
                    callback(self:GetServerData())
                end
                -- 如果收到上一条消息，重置冷却
                self.serverReqFlag = true
            end)
        end
    end
end

--- 上报当前得分
---@param score number 得分 UInt64
function rankMgr:SendServerData(score)
    score = math.floor(math.max(0, score))
    if self.fakeRankFlag then
        self:InsertToArray(self.sortType, self.serverDataCache,
            self:NewDataTable(localDefaultID, score, localDefaultName))
        TinyRush_Log(self.__fullName .. "【假】排名数据发送结果：成功")
    else
        if puzzleMgr ~= nil and puzzleMgr.Send_Tiny_Rank_Score_REQ ~= nil then
            puzzleMgr.Send_Tiny_Rank_Score_REQ(self.gameData.type, self.level, score, self.sortType, self.maxNum,
                function(msg)
                    TinyRush_Log(self.__fullName .. "排名数据发送结果：" .. tostring(msg))
                end)
        end
    end
end

--- 所有数据都是这种格式
function rankMgr:ParseDataTable(table)
    return self:NewDataTable(table[0], table[1], table[2])
end
function rankMgr:NewDataTable(id, score, name)
    return {id, score, name}
end

--- 加载本地数据
function rankMgr:LoadLocalData()
    if self.localDataCache == nil and PlayerPrefs.HasKey(self.saveKey) then
        local data = PlayerPrefs.GetString(self.saveKey)
        if data ~= nil then
            local ok = pcall(function()
                self.localDataCache = dkjson.decode(data)
            end)
        end
    end
end
--- 保存本地数据
function rankMgr:SaveLocalData(score)
    local newScore = self.localDataCache
    if newScore ~= nil then
        if self.sortType == 0 then
            newScore = math.max(score, newScore)
        else
            newScore = math.min(score, newScore)
        end
    else
        newScore = score
    end
    if self.localDataCache ~= newScore then
        self.localDataCache = newScore
        local data = dkjson.encode(self.localDataCache)
        PlayerPrefs.SetString(self.saveKey, data)
    end
end

--- 获取本地数据
function rankMgr:GetLocalData()
    if self.localDataCache ~= nil then
        return self:NewDataTable(localDefaultID, self.localDataCache, localDefaultName)
    else
        return nil
    end
end

--- 获取服务器数据，排行榜列表
function rankMgr:GetServerData()
    return self.serverDataCache
end

--- 获取服务器数据
---@param index number 索引
function rankMgr:GetServerDataByIndex(index)
    index = index or 1
    if self.serverDataCache ~= nil and index <= #self.serverDataCache then
        return self:ParseDataTable(self.serverDataCache[index])
    else
        return nil
    end
end

function rankMgr:InsertToArray(sortType, arr, value)
    local arrLen = #arr + 1
    -- 增加数组长度
    arr[arrLen] = value
    -- 从数组的末尾开始向前遍历，找到value应插入的位置
    local i = arrLen - 1
    while i >= 1 and ((sortType == 0 and arr[i][2] < value[2]) or (sortType ~= 0 and arr[i][2] > value[2])) do
        -- 将元素向后移动一位
        arr[i + 1] = arr[i]
        i = i - 1
    end
    -- 将新元素插入正确位置
    arr[i + 1] = value
    -- 如果数组长度已经达到最大限制，删除多余的数据
    if arrLen > self.maxNum then
        table.remove(arr, arrLen)
    end
end

local function partition(arr, low, high, sortType)
    local pivot = arr[high]
    local i = low - 1
    for j = low, high - 1 do
        if (sortType == 0 and arr[j][2] > pivot[2]) or (sortType ~= 0 and arr[j][2] < pivot[2]) then
            i = i + 1
            arr[i], arr[j] = arr[j], arr[i]
        end
    end
    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    return i + 1
end

function rankMgr:Quicksort(arr, low, high)
    if low < high then
        local pivot = partition(arr, low, high, self.sortType)
        self:Quicksort(arr, low, pivot - 1)
        self:Quicksort(arr, pivot + 1, high)
    end
end

return rankMgr
