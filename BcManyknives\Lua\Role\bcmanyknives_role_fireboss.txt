local manyknives_role_boss = TinyRush_CreateClass("bcmanyknives_role_fireboss"):baseClass(require(
    "bcmanyknives_role_boss"))

-- 0:进场,1:自由态，2:扇形火球,3:砸地,4:技能后摇
manyknives_role_boss.moveFlagBind = nil
manyknives_role_boss.fanTimer = nil
manyknives_role_boss.circleTimer = nil
manyknives_role_boss.attentTimer = nil
manyknives_role_boss.waitTimer = nil
manyknives_role_boss.fireConfig = nil
-- 扇形区域
manyknives_role_boss.fanGuide = nil
manyknives_role_boss.fanDir = nil
manyknives_role_boss.fireBossDmg = nil
-- 圆形区域
manyknives_role_boss.circleGuide = nil
manyknives_role_boss.circlePos = nil
manyknives_role_boss.circleFlag = nil
manyknives_role_boss.circleBossDmg = nil

function manyknives_role_boss:ctor(...)
    self.__base:ctor(...)
    self.fx_rushReady = self.dataSrc["fx_boss_chongci_xuli"]
    self.moveFlagBind = require("tinyrush_bindable").new(0)
    self.moveFlagBind:register(function(value)
        self:MoveFlagListener(value)
    end)
end

function manyknives_role_boss:MoveFlagListener(value)
    if value == 0 then
        self.fixedlyFlag = true
        self.collision.enabled = false
        self:SetAICanMove(false)
        self:KillBackUpTween()
        self.uncontrolled = true
        self.fx_rushReady.gameObject:SetActive(false)
    elseif value == 1 or value == 4 then
        self.fixedlyFlag = false
        self.collision.enabled = true
        self:SetAICanMove(true)
        self.uncontrolled = false
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
        self.fx_rushReady.gameObject:SetActive(false)
    elseif value == 2 then
        self.fixedlyFlag = true
        self:KillBackUpTween()
        self.uncontrolled = true
        self.collision.enabled = false
        self:SetAICanMove(false)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.idle)
        self.fx_rushReady:Simulate(0, true)
        self.fx_rushReady:Play()
        self.fx_rushReady.gameObject:SetActive(true)
    else
        self.fixedlyFlag = true
        self:KillBackUpTween()
        self.uncontrolled = true
        self.collision.enabled = false
        self:SetAICanMove(false)
        self.animCtrl:Play(ManyKnivesDefine.AnimatorState.idle)
        self.fx_rushReady:Simulate(0, true)
        self.fx_rushReady:Play()
        self.fx_rushReady.gameObject:SetActive(true)
    end
end

function manyknives_role_boss:Init(...)
    self.__base:Init(...)
    self.fireConfig = self.sceneMgr:GetFireConfigById(self.roleData.skillLevel)
    self.fireBossDmg = self.fireConfig.fan_fireDmg
    self.circleBossDmg = self.fireConfig.fall_Dmg
    self.moveFlagBind:setValue(0, true)
    self.moveFlagBind:invokeEvent()
end

function manyknives_role_boss:Start()
    self.__base:Start()
end

function manyknives_role_boss:Ready()
    self.__base:Ready()
    self.moveFlagBind:setValue(1)
    self.waitTimer = 0
    self.fanTimer = 0
    self.circleTimer = 0
    self.sceneMgr.tutMgr:ShowBossGuide_Fire()
end
function manyknives_role_boss:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    if self.deadFlag or not self.actionFlag then
        return
    end
    if self.moveFlagBind.value == 1 then
        self:MoveFollow(deltaTime)
        self.fanTimer = self.fanTimer + deltaTime
        self.circleTimer = self.circleTimer + deltaTime
        if self.fireConfig.fan_Enable and self.fanTimer >= self.fireConfig.fan_SkillCD then
            self.moveFlagBind:setValue(2)
            self.attentTimer = 0
        elseif self.fireConfig.fall_Enable and self.circleTimer >= self.fireConfig.fall_SkillCD then
            self.moveFlagBind:setValue(3)
            self.attentTimer = 0
            self.circleFlag = false
        end
    elseif self.moveFlagBind.value == 4 then
        self:MoveFollow(deltaTime)
        self.waitTimer = self.waitTimer + deltaTime
        if self.waitTimer >= self.fireConfig.followCD then
            self.moveFlagBind:setValue(1)
        end
    elseif not (self.debuff_freeze_Flag or self.debuff_light_Flag) then
        self.attentTimer = self.attentTimer + deltaTime
        if self.moveFlagBind.value == 2 then
            if self.fanGuide == nil then
                self.fanDir = (self.player.rigidbody.position - self.rigidbody.position).normalized
                self.fanGuide = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fanGuide)
                self.fanGuide:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fanGuide)
                local angle = bc_CS_Vector3.Angle(bc_CS_Vector3(self.fanDir.x, self.fanDir.y, 0), bc_CS_Vector3.right)
                if self.fanDir.y < 0 then
                    angle = -angle
                end
                angle = angle - self.fireConfig.fan_Angle * 0.5
                self.fanGuide:Play(2, self.fireConfig.fan_Angle,
                    bc_CS_Vector2(self.fireConfig.fan_FireDis * 2, self.fireConfig.fan_FireDis * 2),
                    self.bladeTran.position, bc_CS_Quaternion.Euler(0, 0, angle))
            end
            -- 发射火球
            if self.attentTimer >= self.fireConfig.fan_Attent then
                self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.boss_fireball)
                -- 每个火球角度
                local perDeg = self.fireConfig.fan_Angle / (self.fireConfig.fan_FireNum - 1)
                local startAngle = self.fanGuide.transform.eulerAngles.z
                local startPos = self.bladeTran.position
                for i = 1, self.fireConfig.fan_FireNum, 1 do
                    local offDeg = (i - 1) * perDeg + startAngle
                    local fireBall = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_fire_boss)
                    fireBall:GetColliderKey().name =
                        ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.effectType.fire
                    fireBall:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_fire_boss, self)
                    local x = math.cos(math.rad(offDeg))
                    local y = math.sin(math.rad(offDeg))
                    local dir = bc_CS_Vector3(x, y, 0).normalized
                    fireBall:Play(1, startPos + dir, dir, self.fireConfig.fan_FireDis, 10, -90)
                end
                self.moveFlagBind:setValue(4)
                self.waitTimer = 0
                self.fanTimer = 0
                self:HideFanGuide()
            else
                if self.fanGuide ~= nil then
                    self.fanGuide:SetFill(self.attentTimer / self.fireConfig.fan_Attent)
                end
            end
        elseif self.moveFlagBind.value == 3 then
            if not self.circleFlag and self.circleGuide == nil then
                self.circlePos = self.sceneMgr:GetSafetyPosition(self.player.transform.position)
                self.circlePos.z = 0
                self.circleGuide = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.circleGuide)
                self.circleGuide:Init(self.sceneMgr, ManyKnivesDefine.skillNames.circleGuide)
                self.circleGuide:Play(3, bc_CS_Vector2(self.fireConfig.fall_Range, self.fireConfig.fall_Range),
                    self.circlePos, bc_CS_Quaternion.identity)
            end
            if not self.circleFlag and self.attentTimer >= self.fireConfig.fall_Attent then
                self.circleFlag = true
                self:KillCGTween()
                self.rigidbody.simulated = false
                self.bladeRigbody.simulated = false
                self.invincible = true
                self.transform.position = self.circlePos
                self.animCtrl:Play(ManyKnivesDefine.AnimatorState.entrance)
                self.cgTween = ManyKnivesDefine.DOTween.Sequence()
                self.cgTween:InsertCallback(0.3, function()
                    self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.boss_fall)
                    self.sceneMgr.cameraCtrl:Shake_BossEntrance()
                    local dmgObj = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.circleDmg)
                    dmgObj:GetColliderKey().name =
                        ManyKnivesDefine.triggerType.effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.names.Effect .. ManyKnivesDefine.names.split ..
                            ManyKnivesDefine.effectType.bossCircle
                    dmgObj:Init(self.sceneMgr, ManyKnivesDefine.skillNames.circleDmg, self)
                    dmgObj:Play(3, self.circlePos, 0.1, self.fireConfig.fall_Range)
                    local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_dimian)
                    fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_dimian)
                    fx:Play(1, self.circlePos, 2, self.fireConfig.fall_Range * 0.12)
                    self.rigidbody.simulated = true
                    self.bladeRigbody.simulated = true
                    self.invincible = false
                    self:HideCircleGuide()
                end)
                self.cgTween:InsertCallback(1, function()
                    self.moveFlagBind:setValue(4)
                    self.waitTimer = 0
                    self.circleTimer = 0
                end)
            end
            if self.circleGuide ~= nil then
                self.circleGuide:SetFill(math.min(1, self.attentTimer / self.fireConfig.fall_Attent))
            end
        end
    end
end

function manyknives_role_boss:HideFanGuide()
    if self.fanGuide ~= nil then
        self.fanGuide:PushInPool()
        self.fanGuide = nil
    end
end
function manyknives_role_boss:HideCircleGuide()
    if self.circleGuide ~= nil then
        self.circleGuide:PushInPool()
        self.circleGuide = nil
    end
end

function manyknives_role_boss:deaded()
    self.__base:deaded()
end

function manyknives_role_boss:recycle()
    self:HideFanGuide()
    self:HideCircleGuide()
    self.fx_rushReady.gameObject:SetActive(false)
    self.__base:recycle()
end
function manyknives_role_boss:dispose()
    self.__base:dispose()
end

return manyknives_role_boss
