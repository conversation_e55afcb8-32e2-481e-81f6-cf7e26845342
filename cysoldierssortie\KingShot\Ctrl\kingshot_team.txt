---@class kingshot_team
local team = bc_Class("kingshot_team")
---@type kingshot_scene_mgr
team.sceneMgr = nil
---@type kingshot_res_mgr
team.resMgr = nil

---@type fusion_gopool
team.teamPool = nil
---@type kingshot_teamunit[] 记录存活的所有队伍
team.TeamUnitList = nil
---@type kingshot_teamunit[]
team.TeamUnitWithCharacter = nil

function team:__init(...)
    self.sceneMgr, self.resMgr = ...
    self.teamPool = require("fusion_gopool").New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.TeamUnit,
        "kingshot_teamunit")
    self.teamPool:Preload(5)
    self.sceneMgr.TimeSecondBind:Register(function(timer)
        self:TimeSecondListener(timer)
    end)
    self.LookAtTargetSystemInstance = KingShot_Define.LookAtTargetSystem.Instance
end

function team:Reset()
    self.TeamUnitList = {}
    self.TeamUnitWithCharacter = {}
end

function team:TimeSecondListener(timer)
    for i = #self.sceneMgr.TeamDatasByTime, 1, -1 do
        local teamData = self.sceneMgr.TeamDatasByTime[i]
        if teamData.Delay <= timer then
            self:SpawnTeam(teamData)
            table.remove(self.sceneMgr.TeamDatasByTime, i)
        end
    end
end

function team:SpawnTeamByPosCheck()
    for i = 1, #self.sceneMgr.TeamDatasByPos, 1 do
        local teamData = self.sceneMgr.TeamDatasByPos[i]
        if self.sceneMgr.cameraCtrl:CheckInView(teamData.Pos) then
            self:SpawnTeam(teamData)
            table.remove(self.sceneMgr.TeamDatasByPos, i)
            i = i - 1
        end
    end
end

function team:SpawnTeam(teamData)
    local teamConfig = self.resMgr:GetTeamConfigById(teamData.TeamID)
    ---@type kingshot_teamunit
    local teamUnit = self.teamPool:PopOne()
    teamUnit.transform:SetParent(self.sceneMgr.LevelRoot)
    teamUnit:Init(self.sceneMgr, self, teamData, teamConfig)
    self.TeamUnitList[#self.TeamUnitList + 1] = teamUnit
end

function team:Update(deltaTime)
    local pX, _, pZ = self.sceneMgr.playerCtrl:GetCenterXYZ()
    self:SpawnTeamByPosCheck()
    for _, v in ipairs(self.TeamUnitList) do
        v:Update(deltaTime, self.sceneMgr.cameraCtrl.ViewCenterWorldPos, pX, pZ)
    end
end

function team:EnemyBeHit(character)
    local teamUnit = self.TeamUnitWithCharacter[character]
    teamUnit:Dissolution()
end

function team:EnemyDie(character)
    character.transform:SetParent(self.sceneMgr.LevelRoot)
    local teamUnit = self.TeamUnitWithCharacter[character]
    if teamUnit ~= nil then
        self.TeamUnitWithCharacter[character] = nil
        local overFlag = teamUnit:CharacterDie(character)
        if overFlag then
            table.remove_value(self.TeamUnitList, teamUnit)
            self.teamPool:PushOne(teamUnit)
        end
    end
end

return team
