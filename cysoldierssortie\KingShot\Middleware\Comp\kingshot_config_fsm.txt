local minigame_mgr = KingShot_Define.minigame_mgr
local minigame_buff_mgr = KingShot_Define.minigame_buff_mgr
local GetTransformPositionXYZ = KingShot_Define.GetTransformPositionXYZ
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local cysoldierssortie_dotween_extern = require("cysoldierssortie_dotween_extern")

local HeroFSMConfig =
{
    _states = {
        [cysoldierssortie_character_state.Idle] = {
            onEnter = function(self, character)
            end,
            onUpdate = function(self, character)
                if not character or not character._character_entity then
                    return
                end
                if character._player.MotionFlag then
                    character:PlayAnim(cysoldierssortie_hero_anim_set.Run)
                else
                    character:PlayAnim(cysoldierssortie_hero_anim_set.Stand)
                end
            end,
            onExit = function(self, character)
            end
        },
        [cysoldierssortie_character_state.Attack] = {
            onEnter = function(self, character)
                if not character or not character._character_entity then
                    return
                end
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                local releaseSkillTime, animSpeed, loopInterval = attackMgr:GetReleaseSkillTime(
                    character._unitID, character._heroId)
                ---@type kingshot_animator
                local animator = character._character_entity._animator
                if character._character_entity._gpu_anim then
                    if animator then
                        if animSpeed then
                            animator:SetAnimatorSpeed(animSpeed)
                        end
                        if loopInterval then
                            animator:SetAttackLoopInterval(true, loopInterval)
                        end
                    end
                else
                    animator:SetAtkInterval(animSpeed, loopInterval)
                end
                --循环播放攻击动画
                character:PlayAnim(cysoldierssortie_hero_anim_set.Ability)

                --攻击代理
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if not attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end
                local attackComp = character:GetAttackComp()
                if not attackComp then
                    return
                end
                if attackMgr then
                    attackMgr:RegisterAttackCompProxy(attackComp)
                end
            end,
            onUpdate = function(self, character)
                if not character then
                    return
                end
                --非GPU动画可以试试控制跑步动画
                if character._character_entity and not character._character_entity._gpu_anim then
                    if character._player.MotionFlag then
                        character:PlayAnim(cysoldierssortie_hero_anim_set.Run)
                    else
                        character:PlayAnim(cysoldierssortie_hero_anim_set.Stand)
                    end
                end
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if attackMgr and attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end
                character:Fire()
            end,
            onExit = function(self, character)
                if not character then
                    return
                end
                character:ResetAnim()

                if not character._character_entity then
                    return
                end
                if character._character_entity._gpu_anim then
                    local animator = character._character_entity._animator
                    if animator then
                        animator:SetAnimatorSpeed(1)
                    end
                end

                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if not attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end
                local attackComp = character:GetAttackComp()
                if not attackComp then
                    return
                end
                if attackMgr then
                    attackMgr:UnregisterAttackCompProxy(attackComp)
                end
            end,
        },
    },
    _transitions = {
        {
            from = cysoldierssortie_character_state.Idle,
            to = cysoldierssortie_character_state.Attack,
            condition = function(self, character)
                return character._targetGo ~= nil
            end
        },
        {
            from = cysoldierssortie_character_state.Attack,
            to = cysoldierssortie_character_state.Idle,
            condition = function(self, character)
                return character._targetGo == nil
            end
        },
    }
}

local lookAtTime = 0.25
local aoiAngle = 25
local EnemyFSMConfig =
{
    _states = {
        [cysoldierssortie_character_state.Idle] = {
            onEnter = function(self, character)
                character:PlayAnim(cysoldierssortie_hero_anim_set.Stand)
            end,
            onUpdate = function(self, character)
                if not character or not character._character_entity then
                    return
                end
                character:PlayAnim(cysoldierssortie_hero_anim_set.Run)
            end,
            onExit = function(self, character)
            end
        },
        [cysoldierssortie_character_state.Move] = {
            onEnter = function(self, character)
                character:StartMove()
                character:PlayAnim(cysoldierssortie_hero_anim_set.Run)
            end,
            onUpdate = function(self, character)
                if not character then
                    return
                end
                if bc_IsNotNull(character._targetGo) then
                    local px, py, pz = GetTransformPositionXYZ(character._targetGo)
                    character:MoveXYZ(px, py, pz)
                end
            end,
            onExit = function(self, character)
                if not character then
                    return
                end
                character:ResetAnim(cysoldierssortie_hero_anim_set.Run)
                character:StopMove()
            end,
        },
        [cysoldierssortie_character_state.Attack] = {
            onEnter = function(self, character)
                if not character then
                    return
                end
                character:PlayAnim(cysoldierssortie_hero_anim_set.Ability)
                if not character._character_entity then
                    return
                end
                minigame_buff_mgr.CheckCondition(character, minigame_buff_mgr.ConditionType.Behind_Enemy)
                if character._character_entity._gpu_anim then
                    local animator = character._character_entity._animator
                    if animator then
                        local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                        local releaseSkillTime, animSpeed, loopInterval = attackMgr:GetReleaseSkillTime(
                            character._unitID, character._heroId)
                        if animSpeed then
                            animator:SetAnimatorSpeed(animSpeed)
                        end
                        if loopInterval then
                            animator:SetAttackLoopInterval(true, loopInterval)
                        end
                    end
                end
                --攻击代理
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if attackMgr and not attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end

                local attackComp = character:GetAttackComp()
                if not attackComp then
                    return
                end
                if attackMgr then
                    attackMgr:RegisterAttackCompProxy(attackComp)
                end
            end,
            onUpdate = function(self, character)
                if not character then
                    return
                end
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if attackMgr and attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end
                character:Fire()
            end,
            onExit = function(self, character)
                if not character then
                    return
                end
                --character:StopFire()
                character:ResetAnim()
                if not character then
                    return
                end
                if not character._character_entity then
                    return
                end
                if character._character_entity._gpu_anim then
                    local animator = character._character_entity._animator
                    if animator then
                        animator:SetAnimatorSpeed(1)
                    end
                end
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if not attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end
                local attackComp = character:GetAttackComp()
                if not attackComp then
                    return
                end
                if attackMgr then
                    attackMgr:UnregisterAttackCompProxy(attackComp)
                end
            end,
        },
        [cysoldierssortie_character_state.LookAt] = {
            onEnter = function(self, character)
                if not character then
                    return
                end

                character:PlayAnim(cysoldierssortie_hero_anim_set.Run)

                if not character._targetGo then
                    return
                end
                local res = xpcall(function()
                    KingShot_Define.ApiHelper.DoLookAt(character.transform, character._targetGo, lookAtTime)
                end, debug.traceback)
                if not res then
                    cysoldierssortie_dotween_extern.DoLookAt(character.transform, character._targetGo.position,
                        lookAtTime)
                end
            end,
            onUpdate = function(self, character)
            end,
            onExit = function(self, character)
            end
        },
    },
    _transitions = {
        {
            from = cysoldierssortie_character_state.Idle,
            to = cysoldierssortie_character_state.Move,
            condition = function(self, character)
                return not character.troopClash_IgnoreBattleUpdate
            end
        },
        {
            from = cysoldierssortie_character_state.Move,
            to = cysoldierssortie_character_state.Attack,
            condition = function(self, character)
                if not bc_IsNotNull(character._targetGo) then
                    return false
                end

                if not character._weapons or #character._weapons <= 0 then
                    return false
                end

                local attackRange = character._attackRange
                local x, y, z = GetTransformPositionXYZ(character.transform)
                local x1, y1, z1 = GetTransformPositionXYZ(character._targetGo)
                local disToTarget = cysoldierssortie_lua_util:GetDistance2D(x, z, x1, z1)
                if disToTarget <= attackRange then
                    return true
                end
                return false
            end
        },
        {
            from = cysoldierssortie_character_state.Attack,
            to = cysoldierssortie_character_state.Move,
            condition = function(self, character)
                if not character then
                    return false
                end
                local countdown = character:AttackCountdown()
                if countdown > 0.05 then
                    return false
                end
                if not bc_IsNotNull(character._targetGo) then
                    return true
                end
                if bc_IsNotNull(character.transform) then
                    local attackRange = character._attackRange
                    local x, y, z = GetTransformPositionXYZ(character.transform)
                    local x1, y1, z1 = GetTransformPositionXYZ(character._targetGo)
                    local disToTarget = cysoldierssortie_lua_util:GetDistance2D(x, z, x1, z1)
                    if disToTarget > attackRange then
                        return true
                    end
                end

                return false
            end
        },
        {
            from = cysoldierssortie_character_state.Attack,
            to = cysoldierssortie_character_state.LookAt,
            condition = function(self, character)
                if not character then
                    return false
                end

                if not bc_IsNotNull(character._targetGo) then
                    return false
                end

                local countdown = character:AttackCountdown()
                if countdown > 0.05 then
                    return false
                end

                local res = false
                local angle = 0
                if not res then
                    angle = cysoldierssortie_lua_util:AngleAndTarget(character.transform, character._targetGo)
                end
                if angle > aoiAngle then
                    return true
                end

                return false
            end
        },
        {
            from = cysoldierssortie_character_state.LookAt,
            to = cysoldierssortie_character_state.Attack,
            condition = function(self, character)
                if not character then
                    return false
                end

                local curTime = bc_Time.time
                if not character._lookAtTimer then
                    character._lookAtTimer = curTime + lookAtTime
                end
                local lookAtTimer = character._lookAtTimer or 0
                if curTime > lookAtTimer then
                    character._lookAtTimer = nil
                    return true
                end

                return false
            end
        },
    }
}

local DroneFSMConfig =
{
    _states = {
        [cysoldierssortie_character_state.Idle] = {
            onEnter = function(self, character)
                if not character then
                    return
                end
                -- log.Error("self._isDrone")
                if not character._character_entity then
                    return
                end
            end,
            onUpdate = function(self, character)
            end,
            onExit = function(self, character)

            end
        },
        [cysoldierssortie_character_state.Attack] = {
            onEnter = function(self, character)
                if not character then
                    return
                end

                if not character._character_entity then
                    return
                end
            end,
            onUpdate = function(self, character)
                if not character then
                    return
                end
                --log.Error("onEnter_isonUpdate")
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if attackMgr and attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end
                character:Fire()
            end,
            onExit = function(self, character)
                if not character then
                    return
                end
                character:ResetAnim()
            end,
        },
    },
    _transitions = {
        {
            from = cysoldierssortie_character_state.Idle,
            to = cysoldierssortie_character_state.Attack,
            condition = function(self, character)
                local level_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
                if not level_mgr then
                    return false
                end
                local curLevel = level_mgr.curLevel
                if curLevel.isStartGame then
                    return true
                end
                return false
            end
        },
        {
            from = cysoldierssortie_character_state.Attack,
            to = cysoldierssortie_character_state.Idle,
            condition = function(self, character)
                local level_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
                if not level_mgr then
                    return false
                end
                local curLevel = level_mgr.curLevel
                if not curLevel.isStartGame then
                    return true
                end
                return false
            end
        },
    }
}

local BuildingFSMConfig =
{
    _states = {
        [cysoldierssortie_character_state.Idle] = {
            onEnter = function(self, character)
                if not character then
                    return
                end
                -- log.Error("self._isDrone")
                if not character._character_entity then
                    return
                end
            end,
            onUpdate = function(self, character)
            end,
            onExit = function(self, character)

            end
        },
        [cysoldierssortie_character_state.Attack] = {
            onEnter = function(self, character)
                if not character then
                    return
                end

                if not character._character_entity then
                    return
                end
            end,
            onUpdate = function(self, character)
                if not character then
                    return
                end
                --log.Error("onEnter_isonUpdate")
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                if attackMgr and attackMgr:EnableProxy(character._unitID, character._unit_type) then
                    return
                end
                character:Fire()
            end,
            onExit = function(self, character)
                if not character then
                    return
                end
                character:ResetAnim()
            end,
        },
    },
    _transitions = {
        {
            from = cysoldierssortie_character_state.Idle,
            to = cysoldierssortie_character_state.Attack,
            condition = function(self, character)
                return character._targetGo ~= nil
            end
        },
        {
            from = cysoldierssortie_character_state.Attack,
            to = cysoldierssortie_character_state.Idle,
            condition = function(self, character)
                return character._targetGo == nil
            end
        },
    }
}
local UnitType_Config =
{
    [cysoldierssortie_unit_type.Soldier] = HeroFSMConfig,
    [cysoldierssortie_unit_type.Hero] = HeroFSMConfig,
    [cysoldierssortie_unit_type.Drone] = DroneFSMConfig,
    [cysoldierssortie_unit_type.NormalEnemy] = EnemyFSMConfig,
    [cysoldierssortie_unit_type.BossEnemy] = EnemyFSMConfig,
    [cysoldierssortie_unit_type.Building] = BuildingFSMConfig,

}
return
{
    HeroFSMConfig = HeroFSMConfig,
    EnemyFSMConfig = EnemyFSMConfig,
    UnitType_Config = UnitType_Config
}
