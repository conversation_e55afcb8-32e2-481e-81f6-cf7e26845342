local uiCtrl = TinyRush_CreateClass("bcmanyknives_uictrl")

uiCtrl.lifeScope = nil
uiCtrl.uiMgr = nil
uiCtrl.ctrlEvent = nil
uiCtrl.joystickBg = nil
uiCtrl.joystickHead = nil
uiCtrl.joystickBindable = nil
uiCtrl.joystickOriPos = nil
uiCtrl.joySize = nil
uiCtrl.headCenterPos = nil
uiCtrl.vec2Zero = nil
uiCtrl.vec2One = nil
uiCtrl.vec2Half = nil
uiCtrl.joyBoundOff = nil

function uiCtrl:ctor(...)
    self.lifeScope, self.ctrlEvent, self.joystickBg, self.joystickHead, self.joystickOriPos = ...
    self.uiMgr = self.lifeScope:get("bcmanyknives_ui_mgr")
    self.joySize = self.joystickBg.sizeDelta * 0.5
    self.joyBoundOff = self.joySize.x + 10
    self.headCenterPos = bc_CS_Vector2.zero
    self.vec2Zero = bc_CS_Vector2.zero
    self.vec2One = bc_CS_Vector2.one
    self.vec2Half = bc_CS_Vector2.one * 0.5
    self.joystickBindable = require("tinyrush_bindable").new(self.vec2Zero)

    local trigger = CS.UnityEngine.EventSystems.EventTrigger.Entry()
    trigger.eventID = CS.UnityEngine.EventSystems.EventTriggerType.PointerDown
    trigger.callback:AddListener(function(eventData)
        self:OnPointerDown(eventData)
    end)
    local trigger2 = CS.UnityEngine.EventSystems.EventTrigger.Entry()
    trigger2.eventID = CS.UnityEngine.EventSystems.EventTriggerType.PointerUp
    trigger2.callback:AddListener(function(eventData)
        self:OnPointerUp(eventData)
    end)
    local trigger3 = CS.UnityEngine.EventSystems.EventTrigger.Entry()
    trigger3.eventID = CS.UnityEngine.EventSystems.EventTriggerType.Drag
    trigger3.callback:AddListener(function(eventData)
        self:OnDrag(eventData)
    end)
    self.ctrlEvent.triggers:Add(trigger)
    self.ctrlEvent.triggers:Add(trigger2)
    self.ctrlEvent.triggers:Add(trigger3)
end

function uiCtrl:Ready()
    self.joystickBg.gameObject:SetActive(true)
end
function uiCtrl:Hide()
    self.joystickBg.gameObject:SetActive(false)
end
function uiCtrl:OnPointerDown(eventData)
    self.joystickBg.anchoredPosition = self.uiMgr.realCanvaSize * (eventData.position / self.lifeScope.ScreenSize)
end
function uiCtrl:OnPointerUp(eventData)
    self.joystickBg.localPosition = self.joystickOriPos.localPosition
    self.joystickHead.anchoredPosition = self.headCenterPos
    self.joystickBindable:setValue(self.vec2Zero)
end
function uiCtrl:OnDrag(eventData)
    local curPos = self.uiMgr.realCanvaSize * (eventData.position / self.lifeScope.ScreenSize)
    local off = curPos - self.joystickBg.anchoredPosition
    -- 摇杆偏移量限制
    if (off.magnitude > self.joySize.x) then
        off = off.normalized * self.joySize.x
    end
    self.joystickHead.anchoredPosition = off
    self.joystickBindable:setValue(off.normalized)
    local bgPos = self.joystickBg.anchoredPosition
    if curPos.x > bgPos.x + self.joyBoundOff then
        bgPos.x = curPos.x - self.joyBoundOff
    elseif curPos.x < bgPos.x - self.joyBoundOff then
        bgPos.x = curPos.x + self.joyBoundOff
    end
    if curPos.y > bgPos.y + self.joyBoundOff then
        bgPos.y = curPos.y - self.joyBoundOff
    elseif curPos.y < bgPos.y - self.joyBoundOff then
        bgPos.y = curPos.y + self.joyBoundOff
    end
    self.joystickBg.anchoredPosition = bgPos
end

function uiCtrl.Evaluate(from, to, time)
    return from + (to - from) * ((0 - time) * (time - 2))
end

function uiCtrl:dispose()
    if self.joystickBindable ~= nil then
        self.joystickBindable:dispose()
        self.joystickBindable = nil
    end
end

return uiCtrl
