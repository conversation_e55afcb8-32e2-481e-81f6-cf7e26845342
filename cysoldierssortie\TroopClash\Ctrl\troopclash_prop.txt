---@class troopclash_prop
local prop = bc_Class("troopclash_prop")
---@type troopclash_scene_mgr
prop.sceneMgr = nil
---@type troopclash_res_mgr
prop.resMgr = nil
---@type fusion_gopool
prop.propUnitPool = nil
---@type fusion_gopool
prop.propItemPool = nil
---@type troopclash_propunit[]
prop.propUnitList = nil
---@type fusion_gopool[]
prop.propFxPoolWithLv = nil
---@type fusion_gopool
prop.boomFxPool = nil
---@type fusion_gopool
prop.soldierDropFxPool = nil

--掉落曲线
prop.DropPath = nil
prop.DropPathCount = nil

---@type number 掉落范围半径
local dropMinRadius = 3
local dropMaxRadius = 4
local dropSoliderRadius = 4
local dropSoliderRadiusMax = 6

function prop:__init(...)
    self.sceneMgr, self.resMgr = ...
    local goPoolCls = require("fusion_gopool")
    self.propUnitPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.PropUnit, "troopclash_propunit")
    self.propUnitPool:Preload(5)
    self.propItemPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.PropItem, "troopclash_propitem")
    self.propItemPool:Preload(5)
    self.propFxPoolWithLv = {
        goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.PropFx1, "troopclash_propfx_item"),
        goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.PropFx2, "troopclash_propfx_item"),
        goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.PropFx3, "troopclash_propfx_item")
    }
    for _, v in ipairs(self.propFxPoolWithLv) do
        v:Preload(5)
    end
    self.boomFxPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.DropBoom, "troopclash_fxitem")
    self.boomFxPool:Preload(5)
    self.soldierDropFxPool = goPoolCls.New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.SoldierDrop,
        "troopclash_fxitem")
    self.soldierDropFxPool:Preload(5)

    self.sceneMgr.TimeSecondBind:Register(function(timer)
        self:TimeSecondListener(timer)
    end)
    local dropPathTransf = self.resMgr.PrefabPair.DropPath
    local childCount = dropPathTransf.childCount - 1
    local tmpPathArray = {}
    local pathIndex = 1
    for i = 0, childCount, 1 do
        local tmp = dropPathTransf:GetChild(i)
        tmpPathArray[pathIndex] = tmp.localPosition
        pathIndex = pathIndex + 1
    end
    self.DropPath = TroopClash_Define.Func_DrawBezierSplineUnlimit(tmpPathArray, 10)
    self.DropPathCount = #self.DropPath
    -- for i = 1, #self.DropPath - 1, 1 do
    --     CS.UnityEngine.Debug.DrawLine(self.DropPath[i], self.DropPath[i + 1], bc_CS_Color.blue, 100)
    -- end
end

function prop:TimeSecondListener(timer)
    local count = #self.sceneMgr.PropDatasByTime
    if count > 0 then
        local i = 1
        while i <= count do
            local propData = self.sceneMgr.PropDatasByTime[i]
            if propData.Delay <= timer then
                local pos = self.sceneMgr:GetSpawnPropPos()
                local unit = self:SpawnProp(propData.PropID, pos)
                unit:ShowImmediately(pos)
                table.remove(self.sceneMgr.PropDatasByTime, i)
                count = count - 1
                i = i - 1
            end
            i = i + 1
        end
    end
end

function prop:SpawnTeamByPosCheck()
    local count = #self.sceneMgr.PropDatasByPos
    if count > 0 then
        local i = 1
        while i <= count do
            local propData = self.sceneMgr.PropDatasByPos[i]
            if self.sceneMgr.cameraCtrl:CheckInView(propData.Pos) then
                local unit = self:SpawnProp(propData.PropID)
                unit:ShowImmediately(propData.Pos)
                table.remove(self.sceneMgr.PropDatasByPos, i)
                count = count - 1
                i = i - 1
            end
            i = i + 1
        end
    end
end

---@param propData troopclash_propData
function prop:SpawnProp(id)
    local config = self.resMgr:GetPropConfigById(id)
    ---@type troopclash_propunit
    local propUnit = self.propUnitPool:PopOne()
    propUnit.transform:SetParent(self.sceneMgr.LevelRoot)
    propUnit:Init(self.sceneMgr, self, config)
    self.propUnitList[#self.propUnitList + 1] = propUnit
    return propUnit
end

---@return troopclash_fxitem
function prop:PopOneBoomFX()
    return self.boomFxPool:PopOne()
end

---@param item troopclash_fxitem
function prop:PushOneBoomFX(item)
    self.boomFxPool:PushOne(item)
end

---@return troopclash_propfx_item
function prop:PopOnePropFX(lv)
    return self.propFxPoolWithLv[lv]:PopOne()
end

---@param lv number
---@param item troopclash_propfx_item
function prop:PushOnePropFX(lv, item)
    self.propFxPoolWithLv[lv]:PushOne(item)
end

---@return troopclash_propitem
function prop:PopOnePropItem()
    return self.propItemPool:PopOne()
end

---@param item troopclash_propitem
function prop:PushOnePropItem(item)
    self.propItemPool:PushOne(item)
end

---@return troopclash_fxitem
function prop:PopOneSoldierDropFX()
    return self.soldierDropFxPool:PopOne()
end

---@param item troopclash_fxitem
function prop:PushOneSoldierDropFX(item)
    self.soldierDropFxPool:PushOne(item)
end

function prop:PopOneSoldierGO(parent)
    return TroopClash_Define.CS.NeeGame.PoolObject(TroopClash_Define.AbPath.SoldierPrefab, parent, false)
end

function prop:PushOneSoldierGO(go)
    go:SetActive(false)
    TroopClash_Define.CS.NeeGame.ReturnObject(go)
end

function prop:ShowBoomFX(pos)
    local boomFX = self:PopOneBoomFX()
    boomFX.transform:SetParent(self.sceneMgr.LevelRoot)
    TroopClash_Define.SetTransformPositionXYZ(boomFX.transform, pos.x, 0.5, pos.z)
    boomFX:Play(true, 0.7, function()
        self:PushOneBoomFX(boomFX)
    end)
end

---小队死亡，掉落道具
---@param ids number[] 道具id
function prop:DropProps(oriPos, ids)
    oriPos.y = 0.1
    for _, id in ipairs(ids) do
        local config = self.resMgr:GetPropConfigById(id)
        if config.Type <= TroopClash_Define.PropType.Weapon then
            local unit = self:SpawnProp(id)
            local randomPos = TroopClash_Define.Func_InsideUnitCircle(dropMinRadius, dropMaxRadius)
            local endPos = { x = oriPos.x + randomPos.x, y = oriPos.y, z = oriPos.z + randomPos.y }
            endPos = self.sceneMgr:GetNavMeshPosition(endPos)
            unit:DropPlay(oriPos, endPos)
        else
            local dropCount = config.Param
            --散开小兵
            for i = 1, config.Param, 1 do
                local fxItem = self:PopOneSoldierDropFX()
                local randomPos = TroopClash_Define.Func_InsideUnitCircle(dropSoliderRadius, dropSoliderRadiusMax)
                local endPos = { x = oriPos.x + randomPos.x, y = oriPos.y, z = oriPos.z + randomPos.y }
                fxItem.transform:SetParent(self.sceneMgr.LevelRoot)
                local soldierGo = self:PopOneSoldierGO(fxItem.transform)
                TroopClash_Define.SetTransformLocalPositionAndLocalRotation(soldierGo.transform, 0, 0, 0, 0, 0, 0)
                TroopClash_Define.SetTransformLocalScale(soldierGo.transform, 1.2, 1.2, 1.2)
                soldierGo.gameObject:SetActive(true)
                fxItem:PlayParabola(oriPos, endPos, self.DropPath, self.DropPathCount,
                    function()
                        self:ShowBoomFX(endPos)
                    end, 0.4, function()
                        self:PushOneSoldierGO(soldierGo)
                        self:PushOneSoldierDropFX(fxItem)
                        dropCount = dropCount - 1
                        if dropCount < 1 then
                            self.sceneMgr.playerCtrl:CreateSolider(config.Param)
                        end
                    end)
            end
        end
    end
end

---@param unit troopclash_propunit
function prop:PickUpProp(unit)
    self.propUnitPool:PushOne(unit)
    if unit.config.Type == TroopClash_Define.PropType.Weapon then
        self.sceneMgr.playerCtrl:SoliderLevelUp(unit.config.Param)
    else
        self.sceneMgr.playerCtrl:CreateSolider(unit.config.Param)
    end
end

function prop:Reset()
    self.propUnitList = {}
    --小兵预加载
    TroopClash_Define.CS.NeeGame.AddNewObjectPool(TroopClash_Define.AbPath.SoldierPrefab, self.resMgr.SoldierPrefab, 70)
end

function prop:Update(deltaTime)
    self:SpawnTeamByPosCheck()
end

function prop:__delete()
    if self.boomFxPool ~= nil then
        self.boomFxPool:Delete()
        self.boomFxPool = nil
    end
    if self.propUnitPool ~= nil then
        self.propUnitPool:Delete()
        self.propUnitPool = nil
    end
    if self.propItemPool ~= nil then
        self.propItemPool:Delete()
        self.propItemPool = nil
    end
    if self.propFxPoolWithLv ~= nil then
        for i, v in ipairs(self.propFxPoolWithLv) do
            v:Delete()
        end
        self.propFxPoolWithLv = nil
    end
    if self.soldierDropFxPool ~= nil then
        self.soldierDropFxPool:Delete()
        self.soldierDropFxPool = nil
    end
end

return prop
