local poolItemBase = TinyRush_CreateClass("bcmanyknives_effectbase_item"):baseClass(require("tinyrush_gopoolitem"))

-- 对应的角色
poolItemBase.roleBase = nil
poolItemBase.type = nil
poolItemBase.collider = nil
poolItemBase.particle = nil
poolItemBase.pauseUnRegister = nil
poolItemBase.effectID = nil

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    self.collider = self.gameObject:GetComponent(typeof(CS.UnityEngine.Collider2D))
    self.particle = self.gameObject:GetComponentInChildren(typeof(CS.UnityEngine.ParticleSystem))
end

function poolItemBase:GetColliderKey()
    return self.collider.gameObject
end

function poolItemBase:Init(sceneMgr, type, roleBase)
    self.roleBase = roleBase
    self.sceneMgr = sceneMgr
    self.type = type
    self:GetColliderKey().layer = self.roleBase.isPlayer and ManyKnivesDefine.layerID.triggerPlayer or
                                      ManyKnivesDefine.layerID.triggerEnemy
    self.collider.enabled = true
    if self.pauseUnRegister == nil then
        self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
            self:PauseListener(value)
        end)
    end
end

function poolItemBase:RefreshPause()
    self:PauseListener(self.sceneMgr.pauseBind.value)
end

function poolItemBase:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.tween ~= nil then
        self.tween.timeScale = timeScale
    end
end

--- func desc
---@param actionType number :1:直线运动,2:缩放,3延迟消失+设置缩放
function poolItemBase:Play(actionType, ...)
    self:KillTween()
    if actionType == 1 then
        self.transform:SetParent(self.sceneMgr.levelRoot.transform)
        local startPos, dir, dis, moveSp, degOff = ...
        self.transform.position = startPos
        self.transform.localScale = bc_CS_Vector3.one
        local deg = bc_CS_Vector2.Angle(bc_CS_Vector2(dir.x, dir.y), bc_CS_Vector2.right)
        if dir.y < 0 then
            deg = -deg
        end
        self.transform.localRotation = bc_CS_Quaternion.Euler(0, 0, deg + degOff)
        self.tween = self.transform:DOMove(startPos + dir * dis, dis / moveSp):SetEase(ManyKnivesDefine.Ease.Linear)
        self.tween:OnComplete(function()
            self.sceneMgr:PushEffect(self.type, self)
        end)
    elseif actionType == 2 then
        local parent, tarScale, dur, param = ...
        self.transform:SetParent(parent)
        self.transform.localPosition = bc_CS_Vector3.zero
        self.transform.localRotation = bc_CS_Quaternion.identity
        self.transform.localScale = bc_CS_Vector3.one * tarScale
        self.tween = ManyKnivesDefine.DOVirtual.DelayedCall(dur, function()
            self.sceneMgr:PushEffect(self.type, self)
        end, false)
        self.effectID = param
    elseif actionType == 3 then
        self.transform:SetParent(self.sceneMgr.levelRoot.transform)
        local startPos, dur, scale = ...
        self.transform.position = startPos
        self.transform.localScale = bc_CS_Vector3.one * (scale ~= nil and scale or 1)
        self.tween = ManyKnivesDefine.DOVirtual.DelayedCall(dur, function()
            self.sceneMgr:PushEffect(self.type, self)
        end, false)
    end
    if self.particle ~= nil then
        self.particle:Simulate(0, true)
        self.particle:Play()
    end
    self:RefreshPause()
end

function poolItemBase:dispose()
    self:KillTween()
    self.__base:dispose()
end

function poolItemBase:recycle()
    self:KillTween()
    if self.pauseUnRegister ~= nil then
        self.pauseUnRegister:unRegister()
        self.pauseUnRegister = nil
    end
end

function poolItemBase:KillTween()
    if self.tween ~= nil then
        self.tween:Kill()
        self.tween = nil
    end
end

-- 生效后，直接回收
function poolItemBase:Trigger()
    self:KillTween()
    self.collider.enabled = false
    self.sceneMgr:PushEffect(self.type, self)
end

return poolItemBase
