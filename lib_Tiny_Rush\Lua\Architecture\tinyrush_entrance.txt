---@class tinyrush_entrance : TRClass @游戏入口基类
---@alias TinyRush_Entrance tinyrush_entrance
local tinyrush_entrance = TinyRush_CreateClass("tinyrush_entrance")
---@type TinyRush_EGameState @游戏状态
tinyrush_entrance.state = nil
tinyrush_entrance.messager = nil
---@type function @ab加载器 System.Action<string, System.Action<Object>>
tinyrush_entrance.loader = nil
---@type number @当前关卡
tinyrush_entrance.level = nil

-------------------游戏内使用
--- 向宿主发送消息
---@param event_name string
---@param data any
function tinyrush_entrance:sendMessage(event_name, data)
    if self.messager then
        self.messager(event_name, data)
    end
end
-------------------

-------------------与宿主交互，宿主主动调用
--- 游戏启动
---@param level number @目标关卡
---@param loader function @ab加载器 System.Action<string, System.Action<Object>>
---@param messager function @向宿主发送消息 System.Action<string, object[]> messager
function tinyrush_entrance:Open(level, loader, messager, ...)
    self.level = level
    self.loader = loader
    self.messager = messager
    local bc_common_game_ctrl = require "bc_common_game_ctrl"
    bc_common_game_ctrl.Init(level, loader, messager, ...)
end

--- 缓存渲染设置
function tinyrush_entrance:CacheRenderSettings()
    local RenderSettings = CS.UnityEngine.RenderSettings
    self.env_fog = RenderSettings.fog
    self.env_FogStart = RenderSettings.fogStartDistance
    self.env_FogEnd = RenderSettings.fogEndDistance
    self.env_FogMode = RenderSettings.fogMode
    self.env_FogColor = RenderSettings.fogColor
    self.env_fogDensity = RenderSettings.fogDensity
    self.env_ambientMode = RenderSettings.ambientMode
    self.env_ambientSkyColor = RenderSettings.ambientSkyColor
    self.env_ambientEquatorColor = RenderSettings.ambientEquatorColor
    self.env_ambientGroundColor = RenderSettings.ambientGroundColor
    self.env_ambientIntensity = RenderSettings.ambientIntensity
    self.env_ambientLight = RenderSettings.ambientLight
    self.env_subtractiveShadowColor = RenderSettings.subtractiveShadowColor
    self.env_skybox = RenderSettings.skybox
    self.env_sun = RenderSettings.sun
    self.env_ambientProbe = RenderSettings.ambientProbe
    self.env_customReflection = RenderSettings.customReflection
    self.env_reflectionIntensity = RenderSettings.reflectionIntensity
    self.env_reflectionBounces = RenderSettings.reflectionBounces
    self.env_defaultReflectionMode = RenderSettings.defaultReflectionMode
    self.env_defaultReflectionResolution = RenderSettings.defaultReflectionResolution
    self.env_haloStrength = RenderSettings.haloStrength
    self.env_flareStrength = RenderSettings.flareStrength
    self.env_flareFadeSpeed = RenderSettings.flareFadeSpeed
end

--- 恢复渲染设置
function tinyrush_entrance:RestoreRenderSettings()
    local RenderSettings = CS.UnityEngine.RenderSettings
    RenderSettings.fog = self.env_fog
    RenderSettings.fogStartDistance = self.env_FogStart
    RenderSettings.fogEndDistance = self.env_FogEnd
    RenderSettings.fogMode = self.env_FogMode
    RenderSettings.fogColor = self.env_FogColor
    RenderSettings.fogDensity = self.env_fogDensity
    RenderSettings.ambientMode = self.env_ambientMode
    RenderSettings.ambientSkyColor = self.env_ambientSkyColor
    RenderSettings.ambientEquatorColor = self.env_ambientEquatorColor
    RenderSettings.ambientGroundColor = self.env_ambientGroundColor
    RenderSettings.ambientIntensity = self.env_ambientIntensity
    RenderSettings.ambientLight = self.env_ambientLight
    RenderSettings.subtractiveShadowColor = self.env_subtractiveShadowColor
    RenderSettings.skybox = self.env_skybox
    RenderSettings.sun = self.env_sun
    RenderSettings.ambientProbe = self.env_ambientProbe
    RenderSettings.customReflection = self.env_customReflection
    RenderSettings.reflectionIntensity = self.env_reflectionIntensity
    RenderSettings.reflectionBounces = self.env_reflectionBounces
    RenderSettings.defaultReflectionMode = self.env_defaultReflectionMode
    RenderSettings.defaultReflectionResolution = self.env_defaultReflectionResolution
    RenderSettings.haloStrength = self.env_haloStrength
    RenderSettings.flareStrength = self.env_flareStrength
    RenderSettings.flareFadeSpeed = self.env_flareFadeSpeed
end

--- 缓存2D物理设置
function tinyrush_entrance:CachePhysics2DSettings()
    local Physics2D = CS.UnityEngine.Physics2D
    self.phys2D_velocityIterations = Physics2D.velocityIterations
    self.phys2D_positionIterations = Physics2D.positionIterations
    self.phys2D_gravity = Physics2D.gravity
    self.phys2D_queriesHitTriggers = Physics2D.queriesHitTriggers
    self.phys2D_queriesStartInColliders = Physics2D.queriesStartInColliders
    self.phys2D_callbacksOnDisable = Physics2D.callbacksOnDisable
    self.phys2D_reuseCollisionCallbacks = Physics2D.reuseCollisionCallbacks
    self.phys2D_autoSyncTransforms = Physics2D.autoSyncTransforms
    self.phys2D_simulationMode = Physics2D.simulationMode
    self.phys2D_velocityThreshold = Physics2D.velocityThreshold
    self.phys2D_maxLinearCorrection = Physics2D.maxLinearCorrection
    self.phys2D_maxAngularCorrection = Physics2D.maxAngularCorrection
    self.phys2D_maxTranslationSpeed = Physics2D.maxTranslationSpeed
    self.phys2D_maxRotationSpeed = Physics2D.maxRotationSpeed
    self.phys2D_defaultContactOffset = Physics2D.defaultContactOffset
    self.phys2D_baumgarteScale = Physics2D.baumgarteScale
    self.phys2D_baumgarteTOIScale = Physics2D.baumgarteTOIScale
    self.phys2D_timeToSleep = Physics2D.timeToSleep
    self.phys2D_linearSleepTolerance = Physics2D.linearSleepTolerance
    self.phys2D_angularSleepTolerance = Physics2D.angularSleepTolerance
    self.phys2D_IgnoreLayer = {}
    local phys2DGetIgnoreLayer = Physics2D.GetIgnoreLayerCollision
    local phys2DIgnoreLayer = Physics2D.IgnoreLayerCollision
    for i = 13, 16, 1 do
        for j = 13, 16, 1 do
            local index = i * 100 + j
            local index2 = i + j * 100
            if self.phys2D_IgnoreLayer[index] == nil and self.phys2D_IgnoreLayer[index2] == nil then
                local tmpFlag = phys2DGetIgnoreLayer(i, j)
                self.phys2D_IgnoreLayer[index] = tmpFlag
                self.phys2D_IgnoreLayer[index2] = tmpFlag
                phys2DIgnoreLayer(i, j, false)
            end
        end
    end
end
--- 恢复2D物理设置
function tinyrush_entrance:RestorePhysics2DSettings()
    local Physics2D = CS.UnityEngine.Physics2D
    Physics2D.velocityIterations = self.phys2D_velocityIterations
    Physics2D.positionIterations = self.phys2D_positionIterations
    Physics2D.gravity = self.phys2D_gravity
    Physics2D.queriesHitTriggers = self.phys2D_queriesHitTriggers
    Physics2D.queriesStartInColliders = self.phys2D_queriesStartInColliders
    Physics2D.callbacksOnDisable = self.phys2D_callbacksOnDisable
    Physics2D.reuseCollisionCallbacks = self.phys2D_reuseCollisionCallbacks
    Physics2D.autoSyncTransforms = self.phys2D_autoSyncTransforms
    Physics2D.simulationMode = self.phys2D_simulationMode
    Physics2D.velocityThreshold = self.phys2D_velocityThreshold
    Physics2D.maxLinearCorrection = self.phys2D_maxLinearCorrection
    Physics2D.maxAngularCorrection = self.phys2D_maxAngularCorrection
    Physics2D.maxTranslationSpeed = self.phys2D_maxTranslationSpeed
    Physics2D.maxRotationSpeed = self.phys2D_maxRotationSpeed
    Physics2D.defaultContactOffset = self.phys2D_defaultContactOffset
    Physics2D.baumgarteScale = self.phys2D_baumgarteScale
    Physics2D.baumgarteTOIScale = self.phys2D_baumgarteTOIScale
    Physics2D.timeToSleep = self.phys2D_timeToSleep
    Physics2D.linearSleepTolerance = self.phys2D_linearSleepTolerance
    Physics2D.angularSleepTolerance = self.phys2D_angularSleepTolerance
    local phys2DIgnoreLayer = Physics2D.IgnoreLayerCollision
    for i = 13, 16, 1 do
        for j = 13, 16, 1 do
            local index = i * 100 + j
            local index2 = i + j * 100
            if self.phys2D_IgnoreLayer[index] ~= nil or self.phys2D_IgnoreLayer[index2] ~= nil then
                local tmpFlag = self.phys2D_IgnoreLayer[index]
                if tmpFlag == nil then
                    tmpFlag = self.phys2D_IgnoreLayer[index2]
                end
                phys2DIgnoreLayer(i, j, tmpFlag)
            end
        end
    end
end
--- 缓存3D物理设置
function tinyrush_entrance:CachePhysics3DSettings()
    local Physics = CS.UnityEngine.Physics
    self.phys_gravity = Physics.gravity
    self.phys_defaultContactOffset = Physics.defaultContactOffset
    self.phys_sleepThreshold = Physics.sleepThreshold
    self.phys_queriesHitTriggers = Physics.queriesHitTriggers
    self.phys_queriesHitBackfaces = Physics.queriesHitBackfaces
    self.phys_bounceThreshold = Physics.bounceThreshold
    self.phys_defaultMaxDepenetrationVelocity = Physics.defaultMaxDepenetrationVelocity
    self.phys_defaultSolverIterations = Physics.defaultSolverIterations
    self.phys_defaultSolverVelocityIterations = Physics.defaultSolverVelocityIterations
    self.phys_defaultMaxAngularSpeed = Physics.defaultMaxAngularSpeed
    self.phys_autoSimulation = Physics.autoSimulation
    self.phys_autoSyncTransforms = Physics.autoSyncTransforms
    self.phys_reuseCollisionCallbacks = Physics.reuseCollisionCallbacks
    self.phys_IgnoreLayer = {}
    local physGetIgnoreLayer = Physics.GetIgnoreLayerCollision
    local physIgnoreLayer = Physics.IgnoreLayerCollision
    for i = 13, 16, 1 do
        for j = 13, 16, 1 do
            local index = i * 100 + j
            local index2 = i + j * 100
            if self.phys_IgnoreLayer[index] == nil and self.phys_IgnoreLayer[index2] == nil then
                local tmpFlag = physGetIgnoreLayer(i, j)
                self.phys_IgnoreLayer[index] = tmpFlag
                self.phys_IgnoreLayer[index2] = tmpFlag
                physIgnoreLayer(i, j, false)
            end
        end
    end
end
--- 恢复3D物理设置
function tinyrush_entrance:RestorePhysics3DSettings()
    local Physics = CS.UnityEngine.Physics
    Physics.gravity = self.phys_gravity
    Physics.defaultContactOffset = self.phys_defaultContactOffset
    Physics.sleepThreshold = self.phys_sleepThreshold
    Physics.queriesHitTriggers = self.phys_queriesHitTriggers
    Physics.queriesHitBackfaces = self.phys_queriesHitBackfaces
    Physics.bounceThreshold = self.phys_bounceThreshold
    Physics.defaultMaxDepenetrationVelocity = self.phys_defaultMaxDepenetrationVelocity
    Physics.defaultSolverIterations = self.phys_defaultSolverIterations
    Physics.defaultSolverVelocityIterations = self.phys_defaultSolverVelocityIterations
    Physics.defaultMaxAngularSpeed = self.phys_defaultMaxAngularSpeed
    Physics.autoSimulation = self.phys_autoSimulation
    Physics.autoSyncTransforms = self.phys_autoSyncTransforms
    Physics.reuseCollisionCallbacks = self.phys_reuseCollisionCallbacks
    local physIgnoreLayer = Physics.IgnoreLayerCollision
    for i = 13, 16, 1 do
        for j = 13, 16, 1 do
            local index = i * 100 + j
            local index2 = i + j * 100
            if self.phys_IgnoreLayer[index] ~= nil or self.phys_IgnoreLayer[index2] ~= nil then
                local tmpFlag = self.phys_IgnoreLayer[index]
                if tmpFlag == nil then
                    tmpFlag = self.phys_IgnoreLayer[index2]
                end
                physIgnoreLayer(i, j, tmpFlag)
            end
        end
    end
end
--- 缓存质量设置
function tinyrush_entrance:CacheQualitySettings()
    local QualitySettings = CS.UnityEngine.QualitySettings
    self.qlty_shadowDistance = QualitySettings.shadowDistance
    self.qlty_shadows = QualitySettings.shadows
    self.qlty_shadowResolution = QualitySettings.shadowResolution
    self.qlty_shadowProjection = QualitySettings.shadowProjection
    self.qlty_softParticles = QualitySettings.softParticles
    self.qlty_antiAliasing = QualitySettings.antiAliasing
end
--- 恢复质量设置
function tinyrush_entrance:RestoreQualitySettings()
    local QualitySettings = CS.UnityEngine.QualitySettings
    QualitySettings.shadowDistance = self.qlty_shadowDistance
    QualitySettings.shadows = self.qlty_shadows
    QualitySettings.shadowResolution = self.qlty_shadowResolution
    QualitySettings.shadowProjection = self.qlty_shadowProjection
    QualitySettings.softParticles = self.qlty_softParticles
    QualitySettings.antiAliasing = self.qlty_antiAliasing
end
--- 缓存其他App设置
function tinyrush_entrance:CacheAppSettings()
    self.cache_targetFrameRate = CS.UnityEngine.Application.targetFrameRate
    self.cache_fixedDeltaTime = CS.UnityEngine.Time.fixedDeltaTime
    self.cache_timeScale = CS.UnityEngine.Time.timeScale
    self.cache_pixelDragThreshold = CS.UnityEngine.EventSystems.EventSystem.current.pixelDragThreshold
    self.cache_multiTouchEnabled = CS.UnityEngine.multiTouchEnabled
end
--- 恢复其他App设置
function tinyrush_entrance:RestoreAppSettings()
    CS.UnityEngine.Application.targetFrameRate = self.cache_targetFrameRate
    CS.UnityEngine.Time.fixedDeltaTime = self.cache_fixedDeltaTime
    CS.UnityEngine.Time.timeScale = self.cache_timeScale
    CS.UnityEngine.EventSystems.EventSystem.current.pixelDragThreshold = self.cache_pixelDragThreshold
    CS.UnityEngine.multiTouchEnabled = self.cache_multiTouchEnabled
end

--- 当前游戏状态
function tinyrush_entrance:State()
    return self.state or TinyRush_EGameState.NONE
end
--- 获取游戏信息，找策划要
function tinyrush_entrance:GameData()
    return {
        name = "小游戏XXXXX",
        type = 10086,
        useCommonUI = true
    }
end
--- 重置游戏
function tinyrush_entrance:Reset()
    self.state = TinyRush_EGameState.PLAYING
    TinyRush_Log(self.__name .. ">>Reset")
end
--- 暂停游戏（无用）
function tinyrush_entrance:Pause()
    self.state = TinyRush_EGameState.PAUSE
    TinyRush_Log(self.__name .. ">>Pause")
end
--- 恢复游戏（无用）
function tinyrush_entrance:Resume()
    self.state = TinyRush_EGameState.PLAYING
    TinyRush_Log(self.__name .. ">>Resume")
end
--- 用户主动点击退出,触发游戏失败
function tinyrush_entrance:Fail()
    self.state = TinyRush_EGameState.FINISH
    TinyRush_Log(self.__name .. ">>Fail")
    self:sendMessage("Finish", {false, 0})
end
--- 主动退出游戏成功，释放资源
function tinyrush_entrance:Close()
    self.state = TinyRush_EGameState.NONE
    TinyRush_Log(self.__name .. ">>Close")
    self:sendMessage("Close", nil)
end
--- 游戏通关
function tinyrush_entrance:Win()
    self.state = TinyRush_EGameState.FINISH
    TinyRush_Log(self.__name .. ">>Win")
    self:sendMessage("Finish", {true, 0})
end
-------------------
return tinyrush_entrance
