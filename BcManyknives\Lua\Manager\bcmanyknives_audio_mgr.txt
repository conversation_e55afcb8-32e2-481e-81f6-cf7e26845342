local bcmanyknives_audio_mgr = TinyRush_CreateClass("bcmanyknives_audio_mgr"):baseClass(TinyRush_Scope):interface(
    TinyRush_IInit, TinyRush_IUpdate)

bcmanyknives_audio_mgr.resMgr = nil
bcmanyknives_audio_mgr.gameMgr = nil
bcmanyknives_audio_mgr.sceneMgr = nil

bcmanyknives_audio_mgr.audioPair = nil
bcmanyknives_audio_mgr.BGMVolume = nil
bcmanyknives_audio_mgr.FXVolume = nil
bcmanyknives_audio_mgr.hitFlag = nil
bcmanyknives_audio_mgr.hitTimer = nil
bcmanyknives_audio_mgr.knifeFightFlag = nil
bcmanyknives_audio_mgr.knifeFightTimer = nil
bcmanyknives_audio_mgr.knifePickFlag = nil
bcmanyknives_audio_mgr.knifePickTimer = nil
bcmanyknives_audio_mgr.freezeFlag = nil
bcmanyknives_audio_mgr.freezeTimer = nil
local hitTimeCD = 0.1
local knife_fightTimeCD = 0.1
local knifePickTimeCD = 0.1
local freezeTimeCD = 0.5

local fxVolumePair = {}
fxVolumePair[ManyKnivesDefine.AudioClips.shengli] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.return_blood] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.pick_knife] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.monster_death] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.monster_death2] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.monster_dash] = 0.4
fxVolumePair[ManyKnivesDefine.AudioClips.lightning] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.knife_fight] = 0.7
fxVolumePair[ManyKnivesDefine.AudioClips.diski_firlat] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.character_fireball] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.boss_fireball] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.boss_fall] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.boss_dash] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.boss_appears] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.bingdong] = 0.4
fxVolumePair[ManyKnivesDefine.AudioClips.accelerate] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.hit] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.violent] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.Invincible] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.shibai] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.boss_die] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.boss_die2] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.pick_props] = 0.5
fxVolumePair[ManyKnivesDefine.AudioClips.BGM2] = 1

function bcmanyknives_audio_mgr:rush_OnInit()
    self.resMgr = self.lifeScope:get("bcmanyknives_res_mgr")
    self.gameMgr = self.lifeScope:get("bcmanyknives_game_mgr")
    self.sceneMgr = self.lifeScope:get("bcmanyknives_scene_mgr")
    -- 获取系统音量
    local tinyMgr = require("tiny_music_contorller")
    self.BGMVolume, self.FXVolume = tinyMgr.GetVolumeAndMuteMainGameVolume()
end

function bcmanyknives_audio_mgr:Ready()
    local lua = self.resMgr.allAudioPrefab:GetComponent(typeof(CS.GameLuaBehaviour_New))
    lua:Awake()
    local dataSrc = CshapToLuaValue_New(lua)
    self.audioPair = {}
    for _, v in pairs(ManyKnivesDefine.AudioClips) do
        self.audioPair[v] = dataSrc[v]
    end
    self.sceneMgr.AS_BGM.volume = self.BGMVolume * fxVolumePair[ManyKnivesDefine.AudioClips.BGM2]
    self.sceneMgr.AS_BGM.clip = self.audioPair[ManyKnivesDefine.AudioClips.BGM2]
    self.sceneMgr.AS_FX.volume = self.FXVolume
    self.sceneMgr.AS_FX_Loop_Craze.volume = self.FXVolume * fxVolumePair[ManyKnivesDefine.AudioClips.violent]
    self.sceneMgr.AS_FX_Loop_Craze.clip = self.audioPair[ManyKnivesDefine.AudioClips.violent]
    self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
        self:PauseListener(value)
    end)
    self.sceneMgr.rolePlayer.crazeBind:register(function(value)
        if value then
            self.sceneMgr.AS_FX_Loop_Craze:Play(0)
        else
            self.sceneMgr.AS_FX_Loop_Craze:Stop()
        end
    end)
    self:ResetGame()
end

function bcmanyknives_audio_mgr:PauseListener(pause)
    if pause then
        self.sceneMgr.AS_BGM:Pause()
        self.sceneMgr.AS_FX:Pause()
        self.sceneMgr.AS_FX_Loop_Craze:Pause()
    else
        self.sceneMgr.AS_BGM:Play()
        self.sceneMgr.AS_FX:Play()
        if self.sceneMgr.rolePlayer.crazeBind.value then
            self.sceneMgr.AS_FX_Loop_Craze:Play()
        end
    end
end

function bcmanyknives_audio_mgr:PlayOneShot(clip)
    self.sceneMgr.AS_FX:PlayOneShot(self.audioPair[clip], fxVolumePair[clip] or 0.5)
end

function bcmanyknives_audio_mgr:PlayFreezeSound()
    if self.freezeFlag then
        return
    end
    self:PlayOneShot(ManyKnivesDefine.AudioClips.bingdong)
    self.freezeFlag = true
    self.freezeTimer = 0
end

function bcmanyknives_audio_mgr:PlayKnifePickSound()
    if self.knifePickFlag then
        return
    end
    self:PlayOneShot(ManyKnivesDefine.AudioClips.pick_knife)
    self.knifePickFlag = true
    self.knifePickTimer = 0
end

function bcmanyknives_audio_mgr:PlayKnifeFightSound(bladeType)
    if self.knifeFightFlag then
        return
    end
    self:PlayOneShot(ManyKnivesDefine.AudioClips.knife_fight)
    self.knifeFightFlag = true
    self.knifeFightTimer = 0
end

function bcmanyknives_audio_mgr:PlayHitSound()
    if self.hitFlag then
        return
    end
    self:PlayOneShot(ManyKnivesDefine.AudioClips.hit)
    self.hitFlag = true
    self.hitTimer = 0
end

function bcmanyknives_audio_mgr:rush_OnUpdate(deltaTime)
    if self.hitFlag then
        self.hitTimer = self.hitTimer + deltaTime
        if self.hitTimer >= hitTimeCD then
            self.hitFlag = false
            self.hitTimer = 0
        end
    end
    if self.knifeFightFlag then
        self.knifeFightTimer = self.knifeFightTimer + deltaTime
        if self.knifeFightTimer >= knife_fightTimeCD then
            self.knifeFightFlag = false
            self.knifeFightTimer = 0
        end
    end
    if self.knifePickFlag then
        self.knifePickTimer = self.knifePickTimer + deltaTime
        if self.knifePickTimer >= knifePickTimeCD then
            self.knifePickFlag = false
            self.knifePickTimer = 0
        end
    end
    if self.freezeFlag then
        self.freezeTimer = self.freezeTimer + deltaTime
        if self.freezeTimer >= freezeTimeCD then
            self.freezeFlag = false
            self.freezeTimer = 0
        end
    end
end

function bcmanyknives_audio_mgr:ResetGame()
    self.sceneMgr.AS_BGM:Play(0)
    self.sceneMgr.AS_FX:Stop()
    self.sceneMgr.AS_FX_Loop_Craze:Stop()
    self.hitFlag = false
    self.knifeFightFlag = false
    self.knifePickFlag = false
    self.freezeFlag = false
end
function bcmanyknives_audio_mgr:StartBattle()

end
function bcmanyknives_audio_mgr:OverBattle()
    self.sceneMgr.AS_FX_Loop_Craze:Stop()
end

return bcmanyknives_audio_mgr
