---@class tinyrush_bindable : TRClass @数据监听对象，数据改变时会触发回调。【数据类型不限】
local bindable = TinyRush_CreateClass("tinyrush_bindable")
local bindableUnRegister = require("tinyrush_bindableUnRegister")
bindable.value = nil
bindable.callback = nil

function bindable:ctor(...)
    self.value = ...
    self.callback = {}
end

function bindable:dispose()
    self.value = nil
    self.callback = nil
end

--- 强制执行一次回调
function bindable:invokeEvent()
    for _, v in pairs(self.callback) do
        v(self.value)
    end
end

--- 改变值,并执行回调
---@param v any
---@param withoutEvent boolean @可选择是否执行回调
function bindable:setValue(v, withoutEvent)
    if withoutEvent == nil then
        withoutEvent = false
    end
    if self.value ~= v then
        self.value = v
        if not withoutEvent then
            for _, v in pairs(self.callback) do
                v(self.value)
            end
        end
    end
end

--- 获取当前值
function bindable:getValue()
    return self.value
end

--- 绑定事件
---@param onValueChanged function @改变值后回调
---@param withInitValue boolean @可选择是否立刻执行一次回调
---@return tinyrush_bindableUnRegister
function bindable:register(onValueChanged, withInitValue)
    TinyRush_Table_Add(self.callback, onValueChanged)
    if withInitValue == nil then
        withInitValue = false
    end
    if withInitValue then
        onValueChanged(self.value)
    end
    return bindableUnRegister.new(self, onValueChanged)
end

--- 移除事件
---@param func function
function bindable:unRegister(func)
    TinyRush_Table_Remove(self.callback, func)
end

return bindable
