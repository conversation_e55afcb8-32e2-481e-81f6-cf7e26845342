---@class kingshot_buildingitem : fusion_gopoolitem
local item = bc_Class("kingshot_buildingitem", require("fusion_gopoolitem"))
item.DataSrc = nil
---@type kingshot_BuildingConfig
item.config = nil
item.buildingArray = nil

function item:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
    self.buildingArray = {}
    self.buildingArray[1001] = self.DataSrc.Building1 
    self.buildingArray[1002] = self.DataSrc.Building2 
    self.buildingArray[1003] = self.DataSrc.Building3 
end

---@param config kingshot_BuildingConfig
function item:Init(config)
    self.config = config
    if self.config.IsMain then
        self.DataSrc.CoinUI.gameObject:SetActive(false)
        self.DataSrc.Building.gameObject:SetActive(true)
        for i, v in pairs(self.buildingArray) do
            v.gameObject:SetActive(i == self.config.BuildingID)
        end
        self.curLevel = 1
    else
        self.DataSrc.CoinUI.gameObject:SetActive(true)
        self.DataSrc.Building.gameObject:SetActive(false)
        self.DataSrc.Text.text = tostring(self.config.CoinUse)
        self.curLevel = 0
    end
    self.gameObject:SetActive(true)
end

function item:Upgrade()
    self.DataSrc.CoinUI.gameObject:SetActive(false)
    self.DataSrc.Building.gameObject:SetActive(true)
    for i, v in pairs(self.buildingArray) do
        v.gameObject:SetActive(i == self.config.BuildingID)
    end
    self.curLevel = 1
end

---是否可以升级
function item:CanUpgrade()
    if self.curLevel == 0  then
        return true
    end
    return false
end

return item
