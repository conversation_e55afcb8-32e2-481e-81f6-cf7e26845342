local manyknives_role_boss = TinyRush_CreateClass("bcmanyknives_role_rush_retinue"):baseClass(require(
    "bcmanyknives_rolebase_item"))
manyknives_role_boss.actionFlag = nil
-- 入场演出
manyknives_role_boss.cgTween = nil
manyknives_role_boss.initPos = nil

function manyknives_role_boss:ctor(...)
    self.__base:ctor(...)
end

function manyknives_role_boss:Init(...)
    self.__base:Init(...)
    _, _, _, _, self.initPos = ...
    -- 行动标记
    self.actionFlag = false
    self:InitBlade(self.roleData.bladeType, self.roleData.bladeNum)
end

function manyknives_role_boss:Start()
    self.__base:Start()
    self:EntrancePlay(self.initPos)
end

function manyknives_role_boss:Ready()
    self.actionFlag = true
    self.fixedlyFlag = false
    self:SetAICanMove(true)
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
end

function manyknives_role_boss:PauseListener(pause)
    self.__base:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.cgTween ~= nil then
        self.cgTween.timeScale = timeScale
    end
end

function manyknives_role_boss:EntrancePlay(initPos)
    self:KillCGTween()
    self:NoInjury(true)
    self.fixedlyFlag = true
    self:SetAICanMove(false)
    self.actionFlag = false
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.entrance)
    self.cgTween = ManyKnivesDefine.DOTween.Sequence()
    self.cgTween:InsertCallback(0.3, function()
        local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_dimian)
        fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_dimian)
        fx:Play(1, initPos, 2, 0.5)
    end)
    self.cgTween:InsertCallback(1, function()
        self.__entity:Ready()
        self:NoInjury(false)
    end)
    self:RefreshPause()
end

function manyknives_role_boss:KillCGTween()
    if self.cgTween ~= nil then
        self.cgTween:Kill()
        self.cgTween = nil
    end
end
function manyknives_role_boss:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    if self.deadFlag or not self.actionFlag then
        return
    end
    self:MoveFollow(deltaTime)
end

function manyknives_role_boss:deaded()
    self.__entity:recycle()
    self.__base:deaded()
end

function manyknives_role_boss:recycle()
    self:KillCGTween()
    self.__base:recycle()
end
function manyknives_role_boss:dispose()
    self.__entity:recycle()
    self.__base:dispose()
end

return manyknives_role_boss
