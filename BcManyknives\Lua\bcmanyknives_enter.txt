require("tinyrush_architecture")
local manyknives_enter = TinyRush_CreateClass("bcmanyknives_enter"):baseClass(TinyRush_Entrance)
manyknives_enter.lifeScope = nil
TinyRush_ShowLog = false

function manyknives_enter:Open(level, loader, messager, ...)
    level = 1
    self.__base:Open(level, loader, messager, ...)
    require("bcmanyknives_define")
    self:InitApp()
    self.state = TinyRush_EGameState.LOADING
    -- 生成小游戏生命域对象，执行启动
    self.lifeScope = require("bcmanyknives_lifescope").new(self)
    self.lifeScope:rush()
end

local getIgnoreLayer = CS.UnityEngine.Physics2D.GetIgnoreLayerCollision
local ignoreLayer = CS.UnityEngine.Physics2D.IgnoreLayerCollision

-- 忽略碰撞：刀和障碍、道具； 怪物和刀 ；怪物和怪物
function manyknives_enter:InitApp()
    self.cacheFrame = CS.UnityEngine.Application.targetFrameRate
    self.cacheFixedDeltaTime = CS.UnityEngine.Time.fixedDeltaTime
    self.cacheTimeScale = CS.UnityEngine.Time.timeScale

    self.colMapAndColMap = getIgnoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.collisionMap)
    self.colMapAndTriPlayer = getIgnoreLayer(ManyKnivesDefine.layerID.collisionMap,
        ManyKnivesDefine.layerID.triggerPlayer)
    self.colMapAndTriEnemy =
        getIgnoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.triggerEnemy)
    self.triPlayerAndTriEnemy = getIgnoreLayer(ManyKnivesDefine.layerID.triggerPlayer,
        ManyKnivesDefine.layerID.triggerEnemy)
    self.triPlayerAndTriPlayer = getIgnoreLayer(ManyKnivesDefine.layerID.triggerPlayer,
        ManyKnivesDefine.layerID.triggerPlayer)
    self.triEnemyAndTriEnemy = getIgnoreLayer(ManyKnivesDefine.layerID.triggerEnemy,
        ManyKnivesDefine.layerID.triggerEnemy)
    self.colMapAndColEnemy = getIgnoreLayer(ManyKnivesDefine.layerID.collisionMap,
        ManyKnivesDefine.layerID.collisionEnemy)
    self.colEnemyAndColEnemy = getIgnoreLayer(ManyKnivesDefine.layerID.collisionEnemy,
        ManyKnivesDefine.layerID.collisionEnemy)
    self.colEnemyAndTriPlayer = getIgnoreLayer(ManyKnivesDefine.layerID.collisionEnemy,
        ManyKnivesDefine.layerID.triggerPlayer)
    self.colEnemyAndTriEnemy = getIgnoreLayer(ManyKnivesDefine.layerID.collisionEnemy,
        ManyKnivesDefine.layerID.triggerEnemy)

    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.collisionMap, false)
    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.collisionEnemy, false)
    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.triggerPlayer, true)
    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.triggerEnemy, true)
    ignoreLayer(ManyKnivesDefine.layerID.collisionEnemy, ManyKnivesDefine.layerID.collisionEnemy, true)
    ignoreLayer(ManyKnivesDefine.layerID.collisionEnemy, ManyKnivesDefine.layerID.triggerPlayer, true)
    ignoreLayer(ManyKnivesDefine.layerID.collisionEnemy, ManyKnivesDefine.layerID.triggerEnemy, true)
    ignoreLayer(ManyKnivesDefine.layerID.triggerPlayer, ManyKnivesDefine.layerID.triggerEnemy, false)
    ignoreLayer(ManyKnivesDefine.layerID.triggerPlayer, ManyKnivesDefine.layerID.triggerPlayer, true)
    ignoreLayer(ManyKnivesDefine.layerID.triggerEnemy, ManyKnivesDefine.layerID.triggerEnemy, true)

    CS.UnityEngine.Application.targetFrameRate = 60
    CS.UnityEngine.Time.fixedDeltaTime = 0.02
end

function manyknives_enter:RecoverApp()
    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.collisionMap, self.colMapAndColMap)
    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.triggerPlayer, self.colMapAndTriPlayer)
    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.triggerEnemy, self.colMapAndTriEnemy)
    ignoreLayer(ManyKnivesDefine.layerID.triggerPlayer, ManyKnivesDefine.layerID.triggerEnemy, self.triPlayerAndTriEnemy)
    ignoreLayer(ManyKnivesDefine.layerID.triggerPlayer, ManyKnivesDefine.layerID.triggerPlayer,
        self.triPlayerAndTriPlayer)
    ignoreLayer(ManyKnivesDefine.layerID.triggerEnemy, ManyKnivesDefine.layerID.triggerEnemy, self.triEnemyAndTriEnemy)
    ignoreLayer(ManyKnivesDefine.layerID.collisionMap, ManyKnivesDefine.layerID.collisionEnemy, self.colMapAndColEnemy)
    ignoreLayer(ManyKnivesDefine.layerID.collisionEnemy, ManyKnivesDefine.layerID.collisionEnemy,
        self.colEnemyAndColEnemy)
    ignoreLayer(ManyKnivesDefine.layerID.collisionEnemy, ManyKnivesDefine.layerID.triggerPlayer,
        self.colEnemyAndTriPlayer)
    ignoreLayer(ManyKnivesDefine.layerID.collisionEnemy, ManyKnivesDefine.layerID.triggerEnemy, self.colEnemyAndTriEnemy)

    CS.UnityEngine.Application.targetFrameRate = self.cacheFrame
    CS.UnityEngine.Time.fixedDeltaTime = self.cacheFixedDeltaTime
    CS.UnityEngine.Time.timeScale = self.cacheTimeScale
end

function manyknives_enter:Close()
    if self.lifeScope ~= nil then
        self.lifeScope:dispose()
        self:RecoverApp()
    end
    self.lifeScope = nil
    self.__base:Close()
end
function manyknives_enter:Reset()
    if self.lifeScope ~= nil then
        self.lifeScope:reset()
    end
    self.__base:Reset()
end
function manyknives_enter:Fail()
    if self.lifeScope ~= nil then
        self.lifeScope:fail()
    end
    self.__base:Fail()
end

function manyknives_enter:GameData()
    return {
        name = "BcManyknives",
        type = 1059,
        useCommonUI = true
    }
end

return manyknives_enter.new()
