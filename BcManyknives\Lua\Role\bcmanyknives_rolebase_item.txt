local poolItemBase = TinyRush_CreateClass("bcmanyknives_rolebase_item"):baseClass(require("tinyrush_gopoolitem"))
local tinyrush_bindable = require("tinyrush_bindable")

local easeMove = ManyKnivesDefine.Ease.OutQuad
local easeHit = ManyKnivesDefine.Ease.OutQuad
local easeHit2 = ManyKnivesDefine.Ease.InQuad

local itemNames = {
    bladeTriggerListener = "bladeTriggerListener",
    collider = "collider",
    collision = "collision",
    animator = "animator",
    sort_blade = "sort_blade",
    sort_role = "sort_role",
    skinMeshRenderer = "skinMeshRenderer",
    sort_effect = "sort_effect",
    display = "display"
}
poolItemBase.collider = nil
poolItemBase.collision = nil
poolItemBase.collisionRadius = nil
--- 角色物理碰撞监听
poolItemBase.triggerListener = nil
---刀刃碰撞监听
poolItemBase.bladeRigbody = nil
poolItemBase.bladeTriggerListener = nil
--- 给角色暴漏出lifeScope
poolItemBase.sceneMgr = nil
poolItemBase.uiMgr = nil
-- 是否为boss
poolItemBase.isBoss = nil
-- 角色名字
poolItemBase.roleName = nil
-- 角色配置
poolItemBase.roleData = nil
-- 初始位置
poolItemBase.initPos = nil

poolItemBase.bladeList = nil
poolItemBase.display = nil
-- 无敌状态
poolItemBase.invincible = nil
-- 无伤状态
poolItemBase.noInjury = nil
-- 不受击退控制
poolItemBase.uncontrolled = nil
-- 移动速度
poolItemBase.moveSpBind = nil
-- 记得当前速度
poolItemBase.curMoveSp = nil
poolItemBase.aiMoveBind = nil
-- 特殊静止状态
poolItemBase.fixedlyFlag = nil
-- 攻击速度
poolItemBase.atkSpBind = nil
-- 刀刃旋转速度
poolItemBase.bladeSpBind = nil
poolItemBase.bladeRotTween = nil
-- 真实血量
poolItemBase.hpValueBind = nil
-- 最大血量
poolItemBase.hpMaxBind = nil
-- 刀刃数量
poolItemBase.bladeNumBind = nil

poolItemBase.animCtrl = nil
poolItemBase.hpSlider = nil
-- 血条的位置
poolItemBase.hpAnchor = nil
-- 伤害数字的位置
poolItemBase.dmgOffY = nil
-- 冷冻特效
poolItemBase.freezeFx = nil
-- 闪电特效
poolItemBase.fx_Light = nil

-- Debuff相关
-- 减速标记
poolItemBase.debuff_moveSp_Flag = nil
poolItemBase.debuff_moveSp_Timer = nil
poolItemBase.debuff_freeze_Flag = nil
poolItemBase.debuff_freeze_Timer = nil
poolItemBase.debuff_light_Flag = nil
poolItemBase.debuff_light_Timer = nil
poolItemBase.debuff_lightID = nil
-- 碰到了多少个毒气
poolItemBase.hurtByMiasmaPair = nil
-- 毒气计时器
poolItemBase.hurtByMiasmaTimer = nil
poolItemBase.hurtByMiasmaCount = nil
poolItemBase.miasmaObj = nil
poolItemBase.miasmaDmg = nil
-- 死亡回收标记
poolItemBase.deadTween = nil
-- 是否准备好
poolItemBase.readyFlag = nil
-- 移动方向随机偏移
poolItemBase.moveOffRot = nil
poolItemBase.moveOffTimer = nil
-- 寻路相关
poolItemBase.AIDestinationSetter = nil
poolItemBase.AIPath = nil
poolItemBase.lastAIPosition = nil

poolItemBase.pauseUnRegister = nil

local mpb_FillPhase = "_FillPhase"
local mpb_FillColor = "_FillColor"
local mpb_DissolveThreshold = "_DissolveThreshold"
local mpb_Color = "_Color"

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    local tmp = self.gameObject:GetComponent(typeof(CS.GameLuaBehaviour_New))
    tmp:Awake()
    self.dataSrc = CshapToLuaValue_New(tmp)
    self.AIDestinationSetter = self.dataSrc["AIDestinationSetter"]
    self.AIPath = self.dataSrc["AIPath"]
    self.triggerListener = self.dataSrc["colliderListener"]
    self.rigidbody = self.dataSrc["rigidbody"]
    self.rigidbody.interpolation = CS.UnityEngine.RigidbodyInterpolation.Interpolate
    self.bladeTriggerListener = self.dataSrc[itemNames.bladeTriggerListener]
    self.bladeRigbody = self.dataSrc["bladeRigbody"]
    self.bladeTran = self.bladeTriggerListener.transform
    self.collider = self.dataSrc[itemNames.collider]
    self.collision = self.dataSrc[itemNames.collision]
    self.collisionRadius = self.collision.radius
    -- self.collision.gameObject:SetActive(false)
    self.animator = self.dataSrc[itemNames.animator]
    self.skinMeshRenderer = self.dataSrc[itemNames.skinMeshRenderer]
    self.material = self.skinMeshRenderer.material
    self.display = self.dataSrc[itemNames.display]
    self.effectParent = self.dataSrc["effectTran"]
    self.moveSpBind = tinyrush_bindable.new(1)
    self.atkSpBind = tinyrush_bindable.new(1)
    self.hpValueBind = tinyrush_bindable.new(0)
    self.hpMaxBind = tinyrush_bindable.new(0)
    self.bladeSpBind = tinyrush_bindable.new(0)
    self.bladeNumBind = tinyrush_bindable.new(0)
    self.animCtrl = require("bcmanyknives_anim_ctrl").new(self.animator, self.moveSpBind, self.atkSpBind)
    self.hpAnchor = self.dataSrc["hpAnchor"]
    self.dmgOffY = self.skinMeshRenderer.bounds.size.y * 0.5
    self.freezeFx = self.dataSrc["fx_snow02"]
    self.fx_Light = self.dataSrc["fx_shandianjingu"]
    -- 注册监听事件
    self.triggerListener:RegisterTriggerEnter2D(function(collider2D)
        self.__entity:OnTriggerEnter2D(collider2D)
    end)
    self.triggerListener:RegisterTriggerExit2D(function(collider2D)
        self.__entity:OnTriggerExit2D(collider2D)
    end)
    self.bladeTriggerListener:RegisterTriggerEnter2D(function(collider2D)
        self.__entity:BladeOnTriggerEnter2D(collider2D)
    end)
    self.aiMoveBind = tinyrush_bindable.new(true)
    if self.AIPath ~= nil then
        -- self.AIPath.repathRate = 2
        self.moveSpBind:register(function(value)
            self.AIPath.maxSpeed = value
        end)
        self.aiMoveBind:register(function(value)
            self.AIPath.canMove = value
        end)
    end
end

function poolItemBase:GetColliderKey()
    return self.collider.gameObject
end

function poolItemBase:SetFillPhase(value, upLoad)
    self.material:SetFloat(mpb_FillPhase, value)
end
function poolItemBase:SetFillColor(value, upLoad)
    self.material:SetColor(mpb_FillColor, value)
end
function poolItemBase:SetDissolve(value, upLoad)
    self.material:SetFloat(mpb_DissolveThreshold, value)
end
function poolItemBase:SetColor(value, upLoad)
    self.material:SetColor(mpb_Color, value)
end

function poolItemBase:Init(...)
    self.sceneMgr, self.uiMgr, self.roleName, self.roleData, self.initPos = ...
    self.transform.position = self.initPos
    self.isPlayer = self.roleName == ManyKnivesDefine.roleNames.player
    self.player = self.sceneMgr.rolePlayer
    if self.isPlayer then
        self.bladeSpBind:setValue(ManyKnivesDefine.bladeInitSpeed)
    else
        self.bladeSpBind:setValue(ManyKnivesDefine.bladeInitSpeed_Enemy)
    end
    self.hpMaxBind:setValue(self.roleData.hp, true)
    self.hpValueBind:setValue(self.roleData.hp)
    self.curMoveSp = self.roleData.speed
    self.moveSpBind:setValue(self.curMoveSp)
    self.bladeNumBind:setValue(0, true)
    self:SetFillPhase(0, false)
    self:SetDissolve(0, true)
    self.collider.enabled = true
    self.rigidbody.simulated = true
    self.bladeRigbody.simulated = true
    self.collider.gameObject.layer = self.isPlayer and ManyKnivesDefine.layerID.triggerPlayer or
                                         ManyKnivesDefine.layerID.triggerEnemy

    self.collision.gameObject.layer = self.isPlayer and ManyKnivesDefine.layerID.collisionMap or
                                          ManyKnivesDefine.layerID.collisionEnemy
    if not self.isPlayer then
        ManyKnivesDefine.Func_IgnoreCollision(self.player.collision, self.collision)
        ManyKnivesDefine.Func_IgnoreCollision(self.player.collectRangeCollider, self.collision)
        ManyKnivesDefine.Func_IgnoreCollision(self.player.collectRangeCollider, self.collider)
    end
    self.noInjury = false
    self.invincible = false
    self.uncontrolled = false
    self.deadFlag = false
    self.isBoss = false
    self.fixedlyFlag = false
    self.hurtByMiasmaPair = {}
    self.hurtByRoleCount = 0
    self.hurtByMiasmaTimer = 0
    self.hurtByMiasmaCount = 0
    self.freezeFx.gameObject:SetActive(false)
    self.fx_Light.gameObject:SetActive(false)
    self.debuff_freeze_Flag = false
    self.debuff_light_Flag = false
    self.debuff_lightID = 0
    self.animCtrl:Freeze(false)
    if not self.isPlayer then
        self.__entity:Start()
    end
    if self.pauseUnRegister == nil then
        self.pauseUnRegister = self.sceneMgr.pauseBind:register(function(value)
            self.__entity:PauseListener(value)
        end)
    end
end

function poolItemBase:RefreshPause()
    self.__entity:PauseListener(self.sceneMgr.pauseBind.value)
end

function poolItemBase:PauseListener(pause)
    local timeScale = pause and 0 or 1
    if self.backUpTween ~= nil then
        self.backUpTween.timeScale = timeScale
    end
    if self.hitTween ~= nil then
        self.hitTween.timeScale = timeScale
    end
    if self.deadTween ~= nil then
        self.deadTween.timeScale = timeScale
    end
    if pause then
        self.animCtrl:Freeze(true)
        self:SetAICanMove(false)
    else
        self:SetAICanMove(true)
        if not (self.debuff_freeze_Flag or self.debuff_light_Flag) then
            self.animCtrl:Freeze(false)
        end
    end
end

function poolItemBase:Start()
    self.hpSlider = self.uiMgr:popHpSlider(self, self.hpValueBind, self.hpMaxBind)
    self:UpdateHPSliderPos()
    self.readyFlag = true
    self.moveOffTimer = ManyKnivesDefine.roleMoveOff.offTimeCD
    self.moveOffRot = bc_CS_Quaternion.identity
    -- if self.AIDestinationSetter ~= nil then
    --     self.AIDestinationSetter.target = self.player.transform
    -- end
    self.lastAIPosition = nil
    self.aiMoveBind:setValue(true, true)
    self.aiMoveBind:invokeEvent()
end

function poolItemBase:SetAICanMove(value)
    if self.AIPath ~= nil then
        local canM = value and not (self.debuff_freeze_Flag or self.debuff_light_Flag or self.fixedlyFlag)
        self.aiMoveBind:setValue(canM)
        -- self.moveSpBind:setValue(canM and self.curMoveSp or 0)
    end
end

-- 跟随玩家
function poolItemBase:MoveFollow(deltaTime)
    if self.deadFlag then
        return
    end
    local tarPos = self.player.transform.position
    self.AIPath.destination = tarPos
    self:RefreshDisplayFlip()
end

function poolItemBase:RefreshDisplayFlip()
    local curPos = self.transform.position
    if self.lastAIPosition == nil or math.abs(curPos.x - self.lastAIPosition.x) >= 0.05 then
        self:SetDisplayFlip(self.AIPath.desiredVelocity.x < 0)
        self.lastAIPosition = curPos
    end
    -- self:SetDisplayFlip(tarPos.x < self.transform.position.x)
    -- self:SetDisplayFlip(self.AIPath.desiredVelocity.x < 0)
end

-- 刀刃碰撞检测
function poolItemBase:BladeOnTriggerEnter2D(collider2D)
    if not self.readyFlag or self.deadFlag or self.sceneMgr.overFlag or self.noInjury then
        return
    end
    local splitStrs = string.bc_split(collider2D.name, ManyKnivesDefine.names.split)
    local type = tonumber(splitStrs[1])
    local targetClass = nil
    local targetObj = collider2D.gameObject
    -- 碰到了刀刃
    if type == ManyKnivesDefine.triggerType.blade then
        targetClass = self.sceneMgr.bladePairWithGO[targetObj]
        -- 刀刃只生效一次就会被处理为nil。 或者碰到了自己的刀刃
        if targetClass == nil or not targetClass.valid or targetClass.roleBase == nil or targetClass.roleBase == self or
            targetClass.roleBase.invincible then
            return
        end
        local pos = collider2D.bounds:ClosestPoint(self.transform.position)
        -- 从角色刀刃池中移除
        targetClass.roleBase.bladeList[targetClass.gameObject] = nil
        targetClass.roleBase.bladeNumBind:setValue(targetClass.roleBase.bladeNumBind.value - 1)
        local bladeFlyDir = targetClass:Trigger(self.rigidbody.position)
        local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_pindao)
        fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_pindao)
        fx:Play(2, pos, 0.5)
        fx.transform.localScale = bc_CS_Vector3.one * math.lerp(0.8, 1, math.random())
        fx.transform.localRotation = bc_CS_Quaternion.Euler(0, 0, math.random(0, 360))
        if not targetClass.roleBase.isPlayer then
            -- 往一个方向上抖动
            self.sceneMgr.cameraCtrl:Shake_BladeTrigger(bladeFlyDir)
            targetClass.roleBase:BackUpTween(self.transform.position)
            self.sceneMgr.audioMgr:PlayKnifeFightSound(self.curBladeType)
        end
        -- 碰到了角色
    elseif type == ManyKnivesDefine.triggerType.role then
        targetClass = self.sceneMgr.rolePairWithGO[targetObj]
        -- 碰到了自己的角色
        if targetClass == self or targetClass.invincible then
            return
        end
        if self.isPlayer then
            self.sceneMgr.cameraCtrl:Shake_EnemyHurt()
            local dmg = ManyKnivesDefine.bladeDmgWithType[ManyKnivesDefine.propType.blade_default] -
                            targetClass.roleData.defense
            dmg = dmg * (ManyKnivesDefine.bladeDmgWithType[self.curBladeType] /
                      ManyKnivesDefine.bladeDmgWithType[ManyKnivesDefine.propType.blade_default])
            targetClass:RoleTrigger(self.transform.position, dmg, true, true)
        else
            targetClass:RoleTrigger(self.transform.position, self.roleData.bladeDmg, false)
        end
        self.sceneMgr.audioMgr:PlayHitSound()
    end
end

function poolItemBase:OnTriggerExit2D(collider2D)
    if self.deadFlag or self.sceneMgr.overFlag then
        return
    end
    local splitStrs = string.bc_split(collider2D.name, ManyKnivesDefine.names.split)
    local type = tonumber(splitStrs[1])
    local targetClass = nil
    local targetObj = collider2D.gameObject
    -- 通用部分
    -- 碰到了特效
    if type == ManyKnivesDefine.triggerType.effect then
        local effectType = tonumber(splitStrs[3])
        -- 都能生效的特效
        if effectType == ManyKnivesDefine.effectType.miasma then
            if self.hurtByMiasmaPair[targetObj] ~= nil then
                self.hurtByMiasmaPair[targetObj] = nil
                self.hurtByMiasmaCount = self.hurtByMiasmaCount - 1
            end
        end
    end
end

function poolItemBase:OnTriggerEnter2D(collider2D)
    if self.deadFlag or self.sceneMgr.overFlag then
        return
    end
    local splitStrs = string.bc_split(collider2D.name, ManyKnivesDefine.names.split)
    local type = tonumber(splitStrs[1])
    local targetClass = nil
    local targetObj = collider2D.gameObject
    -- 碰到了特效
    if type == ManyKnivesDefine.triggerType.effect then
        targetClass = self.sceneMgr.effectPairWithGO[targetObj]
        -- 碰到了自己的特效
        if targetClass == nil or targetClass.roleBase == self then
            return
        end
        local dmg = nil
        local effectType = tonumber(splitStrs[3])
        if not self.noInjury and not self.invincible then
            -- 只有敌人生效的特效
            if not self.isPlayer then
                if effectType == ManyKnivesDefine.effectType.lightning then
                    if self.debuff_lightID ~= targetClass.effectID then
                        self.debuff_light_Flag = true
                        self.debuff_light_Timer = 0
                        self.fx_Light.gameObject:SetActive(true)
                        self.animCtrl:Freeze(true)
                        self:SetAICanMove(false)
                        dmg = ManyKnivesDefine.playerEffect_Dmg.lightning / 100 * self.hpMaxBind.value
                        self:RoleTrigger(targetObj.transform.position, dmg, true)
                        self.debuff_lightID = targetClass.effectID
                    end
                    -- 冰冻
                elseif effectType == ManyKnivesDefine.effectType.snow then
                    targetClass:Trigger()
                    self.debuff_freeze_Flag = true
                    self.debuff_freeze_Timer = 0
                    self.freezeFx.gameObject:SetActive(true)
                    self.animCtrl:Freeze(true)
                    self:SetAICanMove(false)
                    dmg = ManyKnivesDefine.playerEffect_Dmg.snow / 100 * self.hpMaxBind.value
                    self:RoleTrigger(targetClass.roleBase.transform.position, dmg, true)
                    self.sceneMgr.audioMgr:PlayFreezeSound()
                end
            end
        end
        -- 都能生效的特效
        if effectType == ManyKnivesDefine.effectType.miasma then
            if self.hurtByMiasmaPair[targetObj] == nil then
                if self.hurtByMiasmaCount < 1 then
                    self.hurtByMiasmaTimer = ManyKnivesDefine.roleDebuffConfig.hurtByMiasmaCD
                end
                targetClass = self.sceneMgr.effectPairWithGO[targetObj]
                if targetClass.roleBase.isPlayer then
                    self.miasmaDmg = ManyKnivesDefine.playerEffect_Dmg.miasma / 100 * self.hpMaxBind.value
                else
                    self.miasmaDmg = targetClass.roleBase.miasmaDmg
                end
                self.miasmaObj = targetObj
                self.hurtByMiasmaCount = self.hurtByMiasmaCount + 1
                self.hurtByMiasmaPair[targetObj] = 1
            end
        elseif effectType == ManyKnivesDefine.effectType.fire then
            if not self.noInjury and not self.invincible then
                if targetClass.roleBase.isPlayer then
                    dmg = ManyKnivesDefine.playerEffect_Dmg.fire / 100 * self.hpMaxBind.value
                else
                    if targetClass.roleBase.isBoss then
                        dmg = targetClass.roleBase.fireBossDmg
                    else
                        dmg = targetClass.roleBase.roleData.skillDmg
                    end
                end
                self:RoleTrigger(targetClass.roleBase.transform.position, dmg, true)
                targetClass:Trigger()
            end
        end
    end
end

function poolItemBase:UpdateHPSliderPos()
    -- 血条
    if self.hpSlider ~= nil then
        self.hpSlider:RefreshPos(self.hpAnchor.position)
    end
end

function poolItemBase:OnUpdate(deltaTime)
    if self.deadFlag then
        return
    end
    if self.bladeTran ~= nil then
        self.bladeTran:Rotate(bc_CS_Vector3(0, 0, self.bladeSpBind.value * -deltaTime))
    end
    self:UpdateHPSliderPos()
    self.__entity:UpdateDebuff(deltaTime)
end

function poolItemBase:OnLateUpdate(deltaTime)
    if self.deadFlag then
        return
    end
    self:UpdateHPSliderPos()
end

function poolItemBase:UpdateDebuff(deltaTime)
    -- 只有敌人才生效
    if not self.isPlayer then
        -- 冷冻
        if self.debuff_freeze_Flag then
            self.debuff_freeze_Timer = self.debuff_freeze_Timer + deltaTime
            if self.debuff_freeze_Timer >= ManyKnivesDefine.roleDebuffConfig.debuff_freeze_CD then
                self.debuff_freeze_Flag = false
                self.freezeFx.gameObject:SetActive(false)
                self:SetAICanMove(true)
                if not self.debuff_light_Flag then
                    self.animCtrl:Freeze(false)
                end
            end
        end
        if self.debuff_light_Flag then
            self.debuff_light_Timer = self.debuff_light_Timer + deltaTime
            if self.debuff_light_Timer >= ManyKnivesDefine.roleDebuffConfig.debuff_light_CD then
                self.debuff_light_Flag = false
                self.fx_Light.gameObject:SetActive(false)
                self:SetAICanMove(true)
                if not self.debuff_freeze_Flag then
                    self.animCtrl:Freeze(false)
                end
            end
        end
    end
    if not self.invincible and self.hurtByMiasmaCount > 0 then
        self.hurtByMiasmaTimer = self.hurtByMiasmaTimer + deltaTime
        if self.hurtByMiasmaTimer >= ManyKnivesDefine.roleDebuffConfig.hurtByMiasmaCD then
            self.hurtByMiasmaTimer = 0
            self:RoleTrigger(self.miasmaObj.transform.position, self.miasmaDmg, not self.isPlayer)
        end
    end
end

--- 掉落刀刃
function poolItemBase:DropBlade()
    if self.isBoss then
        return
    end
    -- 带刀刃小兵，必掉刀刃
    if self.roleData.bladeType > 0 then
        local oriPos = self.transform.position
        for i = 1, 5, 1 do
            -- 随机出现在周围
            local tmpPos = bc_CS_Vector3(oriPos.x + math.lerp(-1, 1, math.random()) * 1.5,
                oriPos.y + math.lerp(-1, 1, math.random()) * 1.5)
            local newBlade = self.sceneMgr:BladePoolPopOne(self.curBladeType)
            newBlade:Init(self.sceneMgr, self.curBladeType, self)
            newBlade:Drop(tmpPos, true)
        end
    end
end
--- 掉落道具
function poolItemBase:DropProp()
    if self.isPlayer or self.isBoss or self.roleData.dropConfig == nil then
        return
    end
    local config = self.roleData.dropConfig
    local num = config.num
    while num > 0 do
        local type = math.random(config.typeMin, config.typeMax)
        self.sceneMgr:SpawnPropPrefab(1, type, self.transform.position)
        num = num - 1
    end
end

function poolItemBase:InitBlade(bladeType, bladeNum)
    self.bladeList = {}
    self.curBladeType = bladeType
    self.bladeNumBind:setValue(bladeNum)
    local rotation = 360 / bladeNum * ManyKnivesDefine.bladeSide
    for i = 1, bladeNum do
        local tmpGO = self.sceneMgr:BladePoolPopOne(bladeType)
        tmpGO.transform:SetParent(self.bladeTran.transform)
        tmpGO:Init(self.sceneMgr, bladeType, self)
        local deg = (i - 1) * rotation
        local radius = ManyKnivesDefine.bladeRadius
        local x = radius * math.cos(math.rad(deg))
        local y = radius * math.sin(math.rad(deg))
        local localRot = bc_CS_Quaternion.Euler(0, 0, deg - 90)
        tmpGO.transform.localPosition = bc_CS_Vector3(x, y, 0)
        tmpGO.transform.localRotation = localRot
        tmpGO.transform.localScale = bc_CS_Vector3.one
        self.bladeList[tmpGO.gameObject] = tmpGO
    end
end

-- 受伤掉血
function poolItemBase:RoleTrigger(point, dmgValue, realInjury, bladeFlag)
    if self.deadFlag then
        return
    end
    -- 敌人有防御值
    if not realInjury and not self.isPlayer then
        dmgValue = math.max(0, dmgValue - self.roleData.defense)
    end
    dmgValue = math.floor(dmgValue)
    local dieFlag = false
    self.uiMgr:ShowDmgText((not bladeFlag and realInjury) and 2 or 1, dmgValue,
        self.transform.position + bc_CS_Vector3(0, self.dmgOffY, 0))
    if dmgValue > 0 then
        local fx = self.sceneMgr:PopEffect(ManyKnivesDefine.skillNames.fx_juese_shouji)
        fx:Init(self.sceneMgr, ManyKnivesDefine.skillNames.fx_juese_shouji)
        local fxPos = self.transform.position
        fxPos.x = fxPos.x + math.lerp(-0.2, 0.2, math.random())
        fxPos.y = fxPos.y + math.lerp(0, self.dmgOffY, math.random())
        fx:Play(2, fxPos, 0.5)
        fx.transform.localScale = bc_CS_Vector3.one * math.lerp(0.4, 0.6, math.random())
        fx.transform.localRotation = bc_CS_Quaternion.Euler(0, 0, math.random(0, 360))
        self.hpValueBind:setValue(self.hpValueBind.value - dmgValue)
        if self.hpValueBind.value <= 0 then
            -- 死亡被击飞
            dieFlag = true
            self.__entity:deaded()
        end
    end
    self:HitTween(point, dieFlag)
end
-- 击退
function poolItemBase:BackUpTween(point, dis)
    if self.deadFlag or self.uncontrolled then
        return
    end
    self:KillBackUpTween()
    local dir = (self.transform.position - point)
    self:SetDisplayFlip(dir.x > 0)
    dir.z = 0
    dir = dir.normalized
    if dis == nil then
        dis = 0.2
    end
    local tarPos = self.sceneMgr:GetSafetyPosition(self.transform.position + (dir * dis))
    self.backUpTween = self.transform:DOMove(tarPos, 0.1):SetEase(easeMove)
    if self.AIPath ~= nil then
        self:SetAICanMove(false)
        self.backUpTween:OnComplete(function()
            self:SetAICanMove(true)
        end)
    end
    self:RefreshPause()
end

function poolItemBase:KillBackUpTween()
    if self.backUpTween ~= nil then
        self.backUpTween:Kill()
        self.backUpTween = nil
    end
end

function poolItemBase:KillHitTween()
    if self.hitTween ~= nil then
        self.hitTween:Kill()
        self.hitTween = nil
    end
end

-- 受伤效果
function poolItemBase:HitTween(point, dieFlag)
    self:KillHitTween()
    self.hitTween = ManyKnivesDefine.DOTween.Sequence()
    self.hitTween:Insert(0, ManyKnivesDefine.DOVirtual.Float(0, 1, ManyKnivesDefine.MK_Hit.fillColDur1, function(value)
        self:SetFillPhase(value)
    end):SetEase(easeHit))
    self.hitTween:Insert(ManyKnivesDefine.MK_Hit.fillColDur1,
        ManyKnivesDefine.DOVirtual.Float(1, 0, ManyKnivesDefine.MK_Hit.fillColDur2, function(value)
            self:SetFillPhase(value)
        end):SetEase(easeHit2))
    if dieFlag then
        if self.isBoss then
            self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.boss_die)
        else
            self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.monster_death)
        end
        local limitDis = 5
        if bc_CS_Vector3.Distance(self.transform.position, point) < limitDis then
            local dir = (self.transform.position - point)
            self:SetDisplayFlip(dir.x > 0)
            dir.z = 0
            dir = dir.normalized
            local tarPos = self.sceneMgr:GetSafetyPosition(point + (dir * 5.5))
            self.hitTween:Insert(0, self.transform:DOMove(tarPos, 0.5):SetEase(easeMove))
        end
        self.hitTween:InsertCallback(0.22, function()
            if self.isBoss then
                self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.boss_die2)
            else
                self.sceneMgr.audioMgr:PlayOneShot(ManyKnivesDefine.AudioClips.monster_death2)
            end
        end)
        self.hitTween:InsertCallback(0.5, function()
            self:DropProp()
        end)
    end
    if self.isPlayer then
        self.sceneMgr.cameraCtrl:Shake_PlayerHurt()
    end
    self:RefreshPause()
end

function poolItemBase:dispose()
    self:KillDeadTween()
    self:KillHitTween()
    self:KillBackUpTween()
    self.animCtrl:dispose()
    self.animCtrl = nil
    self.__base:dispose()
end

function poolItemBase:deaded()
    if self.deadFlag then
        return
    end
    self.deadFlag = true
    self.readyFlag = false
    -- 敌人死亡时，身上有刀刃，直接回收
    if not self.isPlayer then
        self:DropBlade()
        if self.bladeNumBind.value > 0 then
            for _, v in pairs(self.bladeList) do
                v:PushInPool()
            end
        end
        self.bladeList = nil
    end
    self.rigidbody.simulated = false
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.die)
    self.animCtrl:Freeze(false)
    self.freezeFx.gameObject:SetActive(false)
    self.fx_Light.gameObject:SetActive(false)
    self.uiMgr:pushHpSlider(self.hpSlider)
    self.hpSlider = nil
    -- 死亡回收
    self.deadTween = ManyKnivesDefine.DOTween.Sequence()
    self.deadTween:Insert(0.6, ManyKnivesDefine.DOVirtual.Float(0, 1, 0.5, function(value)
        self:SetDissolve(value)
    end))
    -- 主角不回收
    if self.sceneMgr:EnemyDie(self) then
        self.deadTween:InsertCallback(1.1, function()
            self.sceneMgr:RolePoolPushOne(self.roleName, self)
        end)
    end
    self:RefreshPause()
end

function poolItemBase:KillDeadTween()
    if self.deadTween ~= nil then
        self.deadTween:Kill()
        self.deadTween = nil
    end
end

function poolItemBase:recycle()
    self:KillDeadTween()
    self:KillHitTween()
    self:KillBackUpTween()
    self.readyFlag = false
    if self.pauseUnRegister ~= nil then
        self.pauseUnRegister:unRegister()
        self.pauseUnRegister = nil
    end
end

function poolItemBase:Stop()
    if not self.deadFlag then
        self.moveSpBind:setValue(0)
    end
    self.readyFlag = false
    self:SetAICanMove(false)
end

-- 演出状态下，角色无伤
function poolItemBase:NoInjury(value)
    self.noInjury = value
end

-- 显示：水平翻转
function poolItemBase:SetDisplayFlip(forward)
    self.display.localScale = forward and ManyKnivesDefine.roleDir.forward or ManyKnivesDefine.roleDir.back
end

return poolItemBase
