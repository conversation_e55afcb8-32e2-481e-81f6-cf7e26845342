---@class tinyrush_lifescope : TinyRush_Scope @生命周期类
---@alias TinyRush_LifeScope tinyrush_lifescope
local tinyrush_lifescope = TinyRush_CreateClass("tinyrush_lifescope"):baseClass(TinyRush_Scope)
tinyrush_lifescope.disposeFlag = nil
tinyrush_lifescope.parent = nil
tinyrush_lifescope.children = nil
tinyrush_lifescope.allScope = nil
tinyrush_lifescope.ioc = nil
tinyrush_lifescope.builtHandle = nil
tinyrush_lifescope.builders = nil
-- 记录所有要初始化的方法
tinyrush_lifescope.initCaches = nil
-- Mono调度器相关
tinyrush_lifescope.mono = nil
-- 基类Mono的实现方法
tinyrush_lifescope.fixedUpdates = nil
tinyrush_lifescope.updates = nil
tinyrush_lifescope.lateUpdates = nil

function tinyrush_lifescope:ctor(...)
    self.ioc = require("tinyrush_ioc_container").new()
    self.children = {}
    self.allScope = {}
    self.builtHandle = {}
    self.builders = {}
    self.initCaches = {}
    self.fixedUpdates = {}
    self.updates = {}
    self.lateUpdates = {}
    self.disposeFlag = false
    self.__base:ctor(...)
end

function tinyrush_lifescope:dispose()
    self.disposeFlag = true
    if self.parent then
        self.parent:removeChild(self)
    end
    if #self.children > 0 then
        for _, value in pairs(self.children) do
            value:dispose()
        end
    end
    if #self.allScope > 0 then
        for _, value in pairs(self.allScope) do
            value:dispose()
        end
    end
    if self.ioc ~= nil then
        self.ioc:dispose()
    end
    self.mono = nil
    self.builders = nil
    self.parent = nil
    self.ioc = nil
    self.children = nil
    self.allScope = nil
    self.builtHandle = nil
    self.initCaches = nil
    self.fixedUpdates = nil
    self.updates = nil
    self.lateUpdates = nil
    self.__base:dispose()
end

--- 注册table,默认使用class.__name作为获取的key,也可以使用别名(只能二选一)
---@param class table
---@param alias string
function tinyrush_lifescope:register(class, alias)
    TinyRush_Table_Add(self.builders, class)
    self.ioc:register(class, alias)
end

--- 获取table,默认使用class.__name作为获取的key,也可以使用别名,默认向父节点域中查找
---@param className string
---@param findInParent boolean
function tinyrush_lifescope:get(name, findInParent)
    if findInParent == nil then
        findInParent = true
    end
    local result = self.ioc:get(name)
    if result == nil and not findInParent and self.parent then
        result = self.parent:get(name)
    end
    return result
end

function tinyrush_lifescope:registerBuildCallback(callback)
    self.builtHandle[#self.builtHandle + 1] = callback
end

--- 流程，启动！
function tinyrush_lifescope:rush()
    self:configure()
    self.mono = require("tinyrush_mono").new()
    self:register(self.mono)
    self:buildAndInit()
    for _, value in pairs(self.builtHandle) do
        value()
    end
    self.builtHandle = nil
    self:InitUnityDispatcher()
end

---  注册此生命期内用到的所有脚本
function tinyrush_lifescope:configure()
end

--- 功能分类+初始化
function tinyrush_lifescope:buildAndInit()
    for _, value in pairs(self.builders) do
        if TinyRush_IsScope(value) then
            value:setLifeScope(self)
            TinyRush_Table_Add(self.allScope, value)
        end
        if value.__interfaces ~= nil then
            for _, interface in pairs(value.__interfaces) do
                local tmpType = TinyRush_LoopTimingWithType[interface]
                if tmpType == TinyRush_EPlayerLoopTiming.Init then
                    TinyRush_Table_Add(self.initCaches, {
                        obj = value,
                        func = value.rush_OnInit
                    })
                elseif tmpType == TinyRush_EPlayerLoopTiming.FixedUpdate then
                    TinyRush_Table_Add(self.fixedUpdates, {
                        obj = value,
                        func = value.rush_OnFixedUpdate
                    })
                elseif tmpType == TinyRush_EPlayerLoopTiming.Update then
                    TinyRush_Table_Add(self.updates, {
                        obj = value,
                        func = value.rush_OnUpdate
                    })
                elseif tmpType == TinyRush_EPlayerLoopTiming.LateUpdate then
                    TinyRush_Table_Add(self.lateUpdates, {
                        obj = value,
                        func = value.rush_OnLateUpdate
                    })
                end
            end
        end
    end
    for _, v in pairs(self.initCaches) do
        v.func(v.obj)
    end
    self.initCaches = nil
    self.builders = nil
end
--- Mono最后才初始化
function tinyrush_lifescope:InitUnityDispatcher()
    self.mono:initMonoRun(self.monoRun)
end
function tinyrush_lifescope:monoRun(loopItemType, deltaTime)
    local table = nil
    if loopItemType == TinyRush_EPlayerLoopTiming.FixedUpdate then
        table = self.fixedUpdates
    elseif loopItemType == TinyRush_EPlayerLoopTiming.Update then
        table = self.updates
    elseif loopItemType == TinyRush_EPlayerLoopTiming.LateUpdate then
        table = self.lateUpdates
    end
    if table then
        for _, v in pairs(table) do
            v.func(v.obj, deltaTime)
        end
    end
end

--- 设置父节点
---@param lifeScope "Lua.Architecture.tinyrush_lifescope"
function tinyrush_lifescope:setParent(lifeScope)
    if self.disposeFlag then
        return
    end
    self.parent = lifeScope
    self.parent:addChild(self)
end

--- 增加子节点
---@param lifeScope "Lua.Architecture.tinyrush_lifescope"
function tinyrush_lifescope:addChild(lifeScope)
    if self.disposeFlag then
        return
    end
    TinyRush_Table_Add(self.children, lifeScope)
end

--- 删除子节点
---@param lifeScope "Lua.Architecture.tinyrush_lifescope"
function tinyrush_lifescope:removeChild(lifeScope)
    if self.disposeFlag then
        return
    end
    TinyRush_Table_Remove(self.children, lifeScope)
end

return tinyrush_lifescope
