---@class tinyrush_taskscheduling_mgr : <PERSON><PERSON>ush_Scope, <PERSON>Rush_IInit, TinyRush_IUpdate
local taskMgr = TinyRush_CreateClass("tinyrush_taskscheduling_mgr"):baseClass(TinyRush_Scope):interface(TinyRush_IInit,
    TinyRush_IUpdate)
-- 是否使用协程方式，默认false
taskMgr.byCoroutine = nil
-- 是否输出日志
taskMgr.showLog = nil
-- 任务队列
taskMgr.taskQueue = nil
-- 携程对象
taskMgr.taskCo = nil
taskMgr.taskCount = nil
taskMgr.updateFrameCount = nil

function taskMgr:ctor(...)
    local logFlag, cFlag = ...
    if type(logFlag) == "boolean" then
        self.showLog = logFlag
    else
        self.showLog = false
    end
    if type(cFlag) == "boolean" then
        self.byCoroutine = cFlag
    else
        self.byCoroutine = false
    end
    self.__base:ctor()
end
function taskMgr:dispose()
    self.taskQueue = nil
    self.taskCo = nil
    self.__base:dispose()
end

function taskMgr:rush_OnInit()
    self.taskQueue = {}
    self.taskCount = 0
    self.updateFrameCount = 0
    if self.byCoroutine then
        self.taskCo = coroutine.create(self.invokeTaskCoroutine)
    end
end
function taskMgr:rush_OnUpdate()
    if self.taskCount < 1 then
        return
    end
    if self.byCoroutine then
        if self.taskCo then
            coroutine.resume(self.taskCo, self)
        end
    else
        self:invokeTaskUpdate()
    end
end
function taskMgr:invokeTaskUpdate()
    if self.taskCount > 0 then
        self.updateFrameCount = self.updateFrameCount + 1
        local tmpTask = self.taskQueue[1]
        if self.updateFrameCount >= tmpTask.frame then
            if self.showLog then
                TinyRush_Log("任务调度_Update>>", "剩余任务数量:" .. #self.taskQueue)
            end
            tmpTask.func()
            self.updateFrameCount = 0
            table.remove(self.taskQueue, 1)
            self.taskCount = self.taskCount - 1
        end
    end
end

---协程任务
function taskMgr.invokeTaskCoroutine(self)
    local waitFrame = 0
    while true do
        coroutine.yield()
        if self.taskCount > 0 then
            waitFrame = waitFrame + 1
            local tmpTask = self.taskQueue[1]
            -- 等待几帧执行任务
            if waitFrame >= tmpTask.frame then
                if self.showLog then
                    TinyRush_Log("任务调度_协程>>", "剩余任务数量:" .. #self.taskQueue)
                    local t = debug.getinfo(tmpTask.func, "lnS")
                    for key, value in pairs(t) do
                        TinyRush_Log(key, value)
                    end
                end
                tmpTask.func()
                table.remove(self.taskQueue, 1)
                self.taskCount = self.taskCount - 1
                waitFrame = 0
            end
        end
    end
end

--- 增加一个任务：每帧只执行一个任务。
---@param func function 
---@param delay number 延迟几帧 默认1
function taskMgr:addTask(func, delay)
    if delay == nil then
        delay = 1
    end
    self.taskCount = self.taskCount + 1
    self.taskQueue[self.taskCount] = {
        frame = delay,
        func = func
    }
end

return taskMgr
