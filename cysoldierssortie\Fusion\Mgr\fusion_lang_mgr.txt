---@class fusion_lang_mgr : fusion_mgrbase
local mgr = bc_Class("fusion_lang_mgr", Fusion.MgrBase)
local lang = require("lang")
local enCode = "en"
local emptyStr = ""

---@type string 当前语种
mgr.useLang = nil
mgr.dataTable = nil

function mgr:__init(...)
    self.useLang = lang.GetUseLang()
end

--- 获取当前语种
function mgr:GetUseLang()
    return self.useLang
end

---@param id number 文案ID
---@return string 文案
function mgr:GetLangById(id)
    return self.dataTable[id] or emptyStr
end

function mgr:ParseData(text)
    self.dataTable = {}
    --当前语种的列，如果没找到，优先找en，en没有去找其他第一个有的
    local curLangColumns = nil
    local enColumns = nil
    -- 按行划分
    local lineStr = string.split(text, '\r\n')
    local colSplit = ","
    local lineStrLength = #lineStr
    local headLine = 7
    local tmpI = 1
    while tmpI <= lineStrLength do
        -- 一行中，每一列的内容
        local content = string.split(lineStr[tmpI], colSplit)
        local col = #content
        --第一行获取语种
        if tmpI == 1 then
            for i = 4, col do
                local tmpCode = content[i]
                if tmpCode == enCode then
                    enColumns = i
                end
                if self.useLang == tmpCode then
                    curLangColumns = i
                end
            end
            if curLangColumns == nil then
                curLangColumns = enColumns
            end
            tmpI = headLine
        else
            local id = content[1]
            if not string.IsNullOrEmpty(id) then
                --没有找到当前语种的列和en的列，就用第一个有值的列
                if curLangColumns == nil then
                    for i = 4, col do
                        if not string.IsNullOrEmpty(content[i]) then
                            curLangColumns = i
                        end
                    end
                end
                self.dataTable[tonumber(id)] = content[curLangColumns]
            end
        end
        tmpI = tmpI + 1
    end
end

return mgr
