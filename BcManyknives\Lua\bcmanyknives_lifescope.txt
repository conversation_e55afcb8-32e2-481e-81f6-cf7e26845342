require("bcmanyknives_define")
local manyknives_lifescope = TinyRush_CreateClass("bcmanyknives_lifescope"):baseClass(TinyRush_LifeScope)
manyknives_lifescope.entrance = nil
manyknives_lifescope.ScreenSize = nil

function manyknives_lifescope:ctor(...)
    -- 构造中传入游戏入口对象
    self.entrance = ...
    self.__base:ctor(...)
end

function manyknives_lifescope:dispose()
    self.__base:dispose()
end

--- 往域内注册要使用的类对象
function manyknives_lifescope:configure()
    self.ScreenSize = bc_CS_Vector2(CS.UnityEngine.Screen.width, CS.UnityEngine.Screen.height)
    self.TopRatio = 0
    self.BotRatio = 0
    if CS.UnityEngine.Screen.height > CS.UnityEngine.Screen.safeArea.yMax then
        self.TopRatio = (CS.UnityEngine.Screen.height - CS.UnityEngine.Screen.safeArea.yMax) /
                            CS.UnityEngine.Screen.height
    end
    if CS.UnityEngine.Screen.safeArea.yMin > 0 then
        self.BotRatio = CS.UnityEngine.Screen.safeArea.yMin / CS.UnityEngine.Screen.height
    end

    -- 注册加载管理对象，传入加载器。
    self:register(require("tinyrush_load_mgr").new(self.entrance.loader))
    -- 注册游戏管理对象，传入继承于tinyrush_game_mgr的对象，传入游戏入口对象
    self:register(require("bcmanyknives_game_mgr").new(self.entrance))
    -- 注册其他自定义的管理对象
    self:register(require("bcmanyknives_res_mgr").new())
    self:register(require("bcmanyknives_ui_mgr").new())
    self:register(require("bcmanyknives_scene_mgr").new())
    self:register(require("bcmanyknives_tutorial_mgr").new())
    self:register(require("bcmanyknives_audio_mgr").new())
    self:register(require("bcmanyknives_lang_mgr").new())

    -- 注册域初始化回调
    self:registerBuildCallback(function()
        self:gameReady()
    end)
end

function manyknives_lifescope:gameReady()
    -- 先加载资源
    self:get("bcmanyknives_res_mgr"):loadAllAssets(function()
        -- 再启动游戏逻辑
        self:get("bcmanyknives_game_mgr"):GameReady()
        self.entrance.state = TinyRush_EGameState.PLAYING
        local event = require "event"
        event.Trigger(event.MINIGAME_SCENE_LOAD_SUCCESS)
    end)
end

function manyknives_lifescope:reset()
    self:get("bcmanyknives_game_mgr"):GameReset()
end
function manyknives_lifescope:fail()
    self:get("bcmanyknives_game_mgr"):GameFailByUser()
end

return manyknives_lifescope
