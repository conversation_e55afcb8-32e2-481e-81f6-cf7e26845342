---@class kingshot_playerunit
local unit = bc_Class("kingshot_playerunit")
unit.unitId = nil
unit.character = nil
unit.heroData = nil
---@type boolean
unit.AliveFlag = nil

unit.targetRotate = nil
unit.MoveFlag = nil
---@type boolean
unit.fireFlag = nil
---@type number
unit.fireTimer = nil
unit.lastTargetGo = nil
unit.lastTargetPos = nil

--- 角色攻击后摇固定1秒，攻击过程中一直锁定目标
local focusCD = 1
local rotateSp = 12

function unit:__init(...)
    self.unitId, self.character, self.heroData = ...
end

function unit:Reset()
    self.AliveFlag = true
    self.fireFlag = false
    self.lastTargetGo = nil
    self.lastTargetPos = nil
    self.character.troopClash_IgnoreBattleUpdate = true
    KingShot_Define.minigame_buff_mgr.AddBuffCfg(self.unitId, self.character)
end

function unit:Die()
    self.AliveFlag = false
end

function unit:SetMoveFlag(flag)
    if not self.AliveFlag then
        return
    end
    self.MoveFlag = flag
end

function unit:Update(tarRot, deltaTime)
    if not self.AliveFlag then
        return
    end
    self.character:UpdateAI()
    self.character:UpdateHp()
    if tarRot ~= nil then
        self.targetRotate = tarRot
    end
    local targetGo = self.character:GetTargetGo()
    local tmpTargetPos = nil
    if self.fireFlag then
        if targetGo ~= nil then
            self.lastTargetGo = targetGo
        end
        if self.lastTargetGo ~= nil then
            self.lastTargetPos = self.lastTargetGo.transform.position
        end
        tmpTargetPos = self.lastTargetPos
        self.fireTimer = self.fireTimer + deltaTime
        if self.fireTimer >= focusCD then
            self.fireFlag = false
            self.lastTargetGo = nil
            self.lastTargetPos = nil
        end
    else
        if targetGo ~= nil then
            tmpTargetPos = targetGo.transform.position
        end
    end
    if tmpTargetPos ~= nil then
        local dir = tmpTargetPos - self.character.transform.position
        dir.y = 0
        if dir.sqrMagnitude > 0.01 then
            self.targetRotate = KingShot_Define.CS.Quaternion.LookRotation(dir.normalized)
        end
    end
    if self.targetRotate ~= nil then
        local oldRot = self.character.transform.rotation
        self.character.transform.rotation = KingShot_Define.CS.Quaternion.Lerp(oldRot, self.targetRotate,
                deltaTime * rotateSp)
    end
end

function unit:Fire()
    self.fireFlag = true
    self.fireTimer = 0
end

return unit
