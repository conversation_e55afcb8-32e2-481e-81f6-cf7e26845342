---@class kingshot_propunit : fusion_gopoolitem
local unit = bc_Class("kingshot_propunit", require("fusion_gopoolitem"))

---@type kingshot_scene_mgr
unit.sceneMgr = nil
---@type kingshot_team
unit.propCtrl = nil
unit.DataSrc = nil
---@type kingshot_PropConfig
unit.config = nil
---@type kingshot_propData
unit.propData = nil

function unit:Init(sceneMgr, ctrl, data, config)
    self.sceneMgr = sceneMgr
    self.propCtrl = ctrl
    self.propData = data
    self.config = config
    if self.propData.Type == KingShot_Define.TeamSpawnType.Pos then
        KingShot_Define.SetTransformPositionXYZ(self.transform, self.propData.Pos.x, self.propData.Pos.y,
            self.propData.Pos.z)
    else
        local newPos = self.sceneMgr:GetSpawnPropPos()
        KingShot_Define.SetTransformPositionXYZ(self.transform, newPos.x, newPos.y, newPos.z)
    end
    self.gameObject:SetActive(true)
end

return unit
