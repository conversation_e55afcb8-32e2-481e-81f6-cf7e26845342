local manyknives_role_knight = TinyRush_CreateClass("bcmanyknives_role_knight"):baseClass(require(
    "bcmanyknives_rolebase_item"))

-- 出生点设置
function manyknives_role_knight:Init(...)
    self.__base:Init(...)
    self:InitBlade(self.roleData.bladeType, self.roleData.bladeNum)
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
end
function manyknives_role_knight:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    self:MoveFollow(deltaTime)
end

return manyknives_role_knight
