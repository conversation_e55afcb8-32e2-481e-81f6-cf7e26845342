---@class tinyrush_delegate : TRClass 模拟C#的delegate和event的实现
local delegate = TinyRush_CreateClass("tinyrush_delegate")
local delegateUnRegister = require("tinyrush_delegateUnRegister")
delegate.events = nil

function delegate:ctor(...)
    self.events = {}
end

--- 添加事件
---@param event function
---@return tinyrush_delegateUnRegister
function delegate:add(event)
    TinyRush_Table_Add(self.events, event)
    return delegateUnRegister.new(self, event)
end

--- 移除事件
function delegate:remove(event)
    TinyRush_Table_Remove(self.events, event)
end

function delegate:invoke(...)
    for _, v in pairs(self.events) do
        v(...)
    end
end

return delegate
