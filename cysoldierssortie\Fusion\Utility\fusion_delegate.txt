---@class fusion_delegate  模拟C#的delegate和event的实现
local delegate = bc_Class("fusion_delegate")
local delegateUnRegister = require("fusion_delegateUnRegister")
delegate.events = nil

function delegate:__init(...)
    self.events = {}
end

--- 添加事件
---@param event fun()
---@return fusion_delegateUnRegister
function delegate:Add(event)
    self.events[#self.events + 1] = event
    return delegateUnRegister.New(self, event)
end

--- 移除事件
function delegate:Remove(event)
    table.remove_value(self.events, event)
end

function delegate:Invoke(...)
    for _, v in ipairs(self.events) do
        v(...)
    end
end

return delegate
