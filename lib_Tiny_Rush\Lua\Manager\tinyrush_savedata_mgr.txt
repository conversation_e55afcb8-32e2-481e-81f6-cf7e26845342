---@class tinyrush_savedata_mgr : <PERSON><PERSON><PERSON>_Scope,<PERSON><PERSON>ush_IInit,TinyRush_IUpdate
local mgr = TinyRush_CreateClass("tinyrush_savedata_mgr"):baseClass(TinyRush_Scope):interface(TinyRush_IInit,
    TinyRush_IUpdate)
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local puzzleMgr = require("puzzlegame_mgr")

---@type tinyrush_entrance
mgr.entrance = nil
mgr.gameData = nil
---@type string 保存路径
mgr.savePath = nil
---@type table<string,string> dataKey : 本地数据
mgr.localDataCache = nil
---@type number 服务器获取数据超时设置
mgr.serverTimeout = nil
---@type table<string,TR_SaveData_REQ_Task> 记录为完成的请求任务
mgr.reqTaskWithKey = nil
---@type number 记录当前请求任务数量
mgr.reqTaskCount = nil
---@type boolean 是否加载完毕。loading结束后任务没完成则清除
mgr.loadingOutFlag = nil
---@type boolean 是否在PC端编辑器中
mgr.inPcEditor = nil
---@type string 在PC端的保存路径
mgr.inPcSaveFolder = nil
--- 构造
---@param entrance tinyrush_entrance 小游戏入口tinyrush_entrance
---@param serverTimeout number 服务器获取数据超时设置
function mgr:ctor(...)
    self.entrance, self.serverTimeout, self.inPcEditor, self.inPcSaveFolder = ...
    self.gameData = self.entrance:GameData()
    self.savePath = string.format("%s_%s", self.gameData.name, self.gameData.type) .. "_%s"
    self.__base:ctor()
end

function mgr:rush_OnInit()
    self.localDataCache = {}
    self:ClearAllTask()
    self.loadingOutFlag = false
end

function mgr:rush_OnUpdate(deltaTime)
    if self.reqTaskCount > 0 then
        for k, v in pairs(self.reqTaskWithKey) do
            v.Timer = v.Timer + deltaTime
            if v.Timer >= self.serverTimeout and self.loadingOutFlag then
                self:InvokeServerData(k, false, nil)
            end
        end
    end
end

--- 标记Loading结束
function mgr:LoadingOut()
    self.loadingOutFlag = true
end

function mgr:ClearAllTask()
    self.reqTaskCount = 0
    self.reqTaskWithKey = {}
end

---@return boolean 是否存在请求任务
function mgr:HasTask()
    return self.reqTaskCount > 0
end

--- 主动请求服务器数据: 异步。在游戏Loading中调用
---@param dataKey string 数据标识，用于区分不同的数据。每个小游戏可以存在多个Key
---@param callback fun(ok:boolean,jsonData:string,errorCode:number) 请求回调，ok为true表示请求成功，data为json字符串
function mgr:LoadServerData(dataKey, callback)
    if self.inPcEditor then
        return
    end
    if self.reqTaskWithKey[dataKey] ~= nil then
        return
    end
    if puzzleMgr ~= nil and puzzleMgr.Get_PuzzleGame_Prop_Json_REQ ~= nil then
        ---@class TR_SaveData_REQ_Task
        local mewTask = {
            Timer = 0,
            Key = dataKey,
            Callback = callback,
        }
        self.reqTaskCount = self.reqTaskCount + 1
        self.reqTaskWithKey[dataKey] = mewTask
        puzzleMgr.Get_PuzzleGame_Prop_Json_REQ(tostring(self.gameData.type), dataKey, function(msg)
            self:InvokeServerData(msg.key, true, msg.data, msg.errorcode)
        end)
    end
end

function mgr:InvokeServerData(key, ok, data, errorCode)
    ---@type TR_SaveData_REQ_Task
    local task = self.reqTaskWithKey[key]
    if task == nil then
        return
    end
    task.Callback(ok, data, errorCode)
    self.reqTaskCount = self.reqTaskCount - 1
    self.reqTaskWithKey[key] = nil
end

--- 上报数据到服务器
---@param dataKey string 数据标识，用于区分不同的数据。每个小游戏可以存在多个Key
---@param jsonData string json字符串数据
function mgr:SendServerData(dataKey, jsonData)
    if self.inPcEditor then
        return
    end
    if puzzleMgr ~= nil and puzzleMgr.Set_PuzzleGame_Prop_Json_REQ ~= nil then
        puzzleMgr.Set_PuzzleGame_Prop_Json_REQ(tostring(self.gameData.type), jsonData, dataKey, nil)
    end
end

--- 加载本地数据
function mgr:LoadLocalData(dataKey)
    if self.localDataCache[dataKey] == nil then
        if self.inPcEditor then
            local fillPath = self.inPcSaveFolder .. "/" .. dataKey
            local File = CS.System.IO.File
            if File.Exists(fillPath) then
                self.localDataCache[dataKey] = File.ReadAllText(fillPath)
            end
        else
            local newPath = string.format(self.savePath, dataKey)
            if PlayerPrefs.HasKey(newPath) then
                self.localDataCache[dataKey] = PlayerPrefs.GetString(newPath)
            end
        end
    end
    return self.localDataCache[dataKey]
end

--- 保存本地数据
function mgr:SaveLocalData(dataKey, jsonData)
    if self.inPcEditor then
        local fillPath = self.inPcSaveFolder .. "/" .. dataKey
        local File = CS.System.IO.File
        if not File.Exists(fillPath) then
            local tmpSR = File.CreateText(fillPath)
            tmpSR:Close()
        end
        File.WriteAllText(fillPath, jsonData, CS.System.Text.Encoding.UTF8)
    else
        local newPath = string.format(self.savePath, dataKey)
        PlayerPrefs.SetString(newPath, jsonData)
        PlayerPrefs.Save()
    end
    self.localDataCache[dataKey] = jsonData
end

--- 清除本地数据
function mgr:ClearLocalData(dataKey)
    if self.inPcEditor then
        local fillPath = self.inPcSaveFolder .. "/" .. dataKey
        local File = CS.System.IO.File
        if File.Exists(fillPath) then
            File.Delete(fillPath)
        end
    else
        local newPath = string.format(self.savePath, dataKey)
        PlayerPrefs.DeleteKey(newPath)
        PlayerPrefs.Save()
    end
    self.localDataCache[dataKey] = nil
end

function mgr:dispose()
    self:ClearAllTask()
    self.__base:dispose()
end

return mgr
