---@class tinyrush_game_mgr : TinyRush_Scope
local tinyrush_game_mgr = TinyRush_CreateClass("tinyrush_game_mgr"):baseClass(TinyRush_Scope)
---@type TinyRush_Entrance
tinyrush_game_mgr.entrance = nil

function tinyrush_game_mgr:ctor(...)
    self.entrance = ...
    self.__base:ctor(...)
end

function tinyrush_game_mgr:dispose()
    self.entrance = nil
    self.__base:dispose()
end

--- 游戏逻辑：通关
function tinyrush_game_mgr:gameWin()
    self.entrance:Win()
end
--- 游戏逻辑：失败
function tinyrush_game_mgr:gameFail()
    self.entrance:Fail()
end

return tinyrush_game_mgr
