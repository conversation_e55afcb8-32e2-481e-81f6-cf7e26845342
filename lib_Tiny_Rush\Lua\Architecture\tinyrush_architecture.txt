TinyRush_ShowLog = false
local TinyRush_NameSplit = "."
local Debug = CS.UnityEngine.Debug
---@class TinyRush_EGameState @游戏状态枚举
TinyRush_EGameState = {
    NONE = 0, -- 未开始
    LOADING = 1, -- 加载中
    PLAYING = 2, -- 正在游戏中
    PAUSE = 3, -- 暂停
    FINISH = 4, -- 结束
    SUSPEND = 5 -- 挂起状态，游戏场景不显示，退出小游戏界面，但是核心资源不销毁
}
-------------------扩展方法
TinyRush_Log = function(...)
    if TinyRush_ShowLog then
        print("<color=#EEFF00><b>TinyRush>></b></color>", ...)
    end
end

TinyRush_Break = function()
    if TinyRush_ShowLog then
        Debug.Break()
    end
end
--- 是否属于域的子类
TinyRush_IsScope = function(table)
    local f = table.isScope
    return f ~= nil and f()
end

TinyRush_Table_Contains = function(table, value)
    if #table > 0 then
        local tableLength = #table
        for i = 1, tableLength, 1 do
            if table[i] == value then
                return true
            end
        end
    end
    return false
end
TinyRush_Table_Add = function(table, value)
    if #table < 1 then
        table[1] = value
        return true
    else
        if not TinyRush_Table_Contains(table, value) then
            table[#table + 1] = value
            return true
        end
    end
    return false
end
TinyRush_Table_Remove = function(t, value)
    if #t < 1 then
        return false
    else
        local idx = nil
        local tLength = #t
        for i = 1, tLength, 1 do
            if t[i] == value then
                idx = i
                break
            end
        end
        if idx ~= nil then
            table.remove(t, idx)
            return true
        end
    end
    return false
end
-------------------

-------------------面向对象

local tinyRush_Keyword = {"__base", "__entity", "__name", "__fullName"}
local tinyRush_KeywordStart = "__"
local tinyRush_Type_Func = "function"
local function string_starts(String, Start)
    return string.sub(String, 1, string.len(Start)) == Start
end
--- 是否属于关键字
---@param str string
local function tinyRush_CheckKeyword(str)
    if string_starts(str, tinyRush_KeywordStart) then
        for _, v in pairs(tinyRush_Keyword) do
            if str == v then
                return true
            end
        end
    end
    return false
end

local function tinyRush_SearchMember(entity, baseClass, key)
    -- 查找父类
    local result = baseClass[key]
    if result == nil and baseClass.__base ~= nil then
        local tmpBase = baseClass.__base
        while tmpBase ~= nil do
            result = tmpBase[key]
            if result ~= nil then
                break
            end
            tmpBase = tmpBase.__base
        end
    end
    -- 查找自身接口
    if result == nil and baseClass.__interfaces ~= nil then
        local iNum = #baseClass.__interfaces
        for i = 1, iNum do
            result = baseClass.__interfaces[i][key]
            if result ~= nil then
                break
            end
        end
    end
    -- 查找结果赋值给最上层，非函数
    -- TinyRush_Log(key, type(result), type(result) == tinyRush_Type_Func)
    if result ~= nil and type(result) ~= tinyRush_Type_Func and not tinyRush_CheckKeyword(tostring(key)) then
        rawset(entity, key, result)
    end
    return result
end
local function tinyRush_SearchMemberByEntity(entity, table, baseClass, key)
    -- 先查找自身对象。自身对象本身只会有 "__base", "__entity" 关键字，其他变量都去最上层拿。
    local result = rawget(table, key)
    if result ~= nil then
        return result
    end
    -- 再去最上层查找，只查找非关键字
    if not tinyRush_CheckKeyword(tostring(key)) then
        result = rawget(entity, key)
        if result ~= nil then
            return result
        end
    end
    -- 再去查找父类
    return tinyRush_SearchMember(entity, baseClass, key)
end
-- 重载运算符
local function tinyRush_override_operator(table, class)
    table.__tostring = class.__tostring
    table.__add = class.__add
    table.__sub = class.__sub
    table.__mul = class.__mul
    table.__div = class.__div
    table.__mod = class.__mod
    table.__pow = class.__pow
    table.__unm = class.__unm
    table.__idiv = class.__idiv
    table.__band = class.__band
    table.__bor = class.__bor
    table.__bxor = class.__bxor
    table.__bnot = class.__bnot
    table.__shl = class.__shl
    table.__shr = class.__shr
    table.__concat = class.__concat
    table.__len = class.__len
    table.__eq = class.__eq
    table.__lt = class.__lt
    table.__le = class.__le
    table.__call = class.__call
end

---@param className string
---@return TRClass
function TinyRush_CreateClass(className)
    assert(type(className) == "string", "class name must be string")
    ---@class TRClass @TinyRush框架基类
    local newClass = {}
    ---@type string @类名
    newClass.__name = className
    ---@type string @类名全名
    newClass.__fullName = className
    ---@type TRClass @父对象
    newClass.__base = nil
    ---@type table[]
    newClass.__interfaces = nil
    newClass.__index = newClass
    ---指向自身最上层对象
    newClass.__entity = nil

    ---继承一个基类
    ---@param bClass TRClass
    ---@return TRClass
    function newClass:baseClass(bClass)
        if bClass then
            self.__base = bClass
            self.__fullName = bClass.__fullName .. TinyRush_NameSplit .. self.__fullName
            -- 继承父类的所有接口
            if bClass.__interfaces ~= nil then
                for _, value in pairs(bClass.__interfaces) do
                    TinyRush_Table_Add(self.__interfaces, value)
                end
            end
        end
        return self
    end

    -- 继承多个接口
    ---@vararg table[]
    ---@return TRClass
    function newClass:interface(...)
        if ... then
            if self.__interfaces == nil then
                self.__interfaces = {}
            end
            local tmpInterface = {...}
            for _, value in pairs(tmpInterface) do
                TinyRush_Table_Add(self.__interfaces, value)
            end
        end
        return self
    end

    --- 判断是否为一个类的子类
    ---@param class TRClass
    function newClass:isSubclassOf(class)
        return string.find(self.__fullName, class.__fullName, 1, true) == 1
    end

    ---new函数，用于创建对象
    ---@return TRClass @返回新对象
    function newClass.new(...)
        local current = {}
        local tmpMT = {
            -- 向父类查找属性
            __index = function(t, k)
                return tinyRush_SearchMember(current, newClass, k)
            end
        }
        tinyRush_override_operator(tmpMT, newClass)
        setmetatable(current, tmpMT)
        current.__entity = current
        local tmpCur = current
        local tmpBase = newClass.__base
        while tmpBase ~= nil do
            local tmp = {}
            local cacheBase = tmpBase
            local tmpMT2 = {
                -- 往最上层查找
                __index = function(_, k)
                    return tinyRush_SearchMemberByEntity(current, tmp, cacheBase, k)
                end,
                -- 赋值都给最上层
                __newindex = function(_, k, v)
                    if not tinyRush_CheckKeyword(tostring(k)) then
                        rawset(current, k, v)
                    else
                        rawset(tmp, k, v)
                    end
                end
            }
            tinyRush_override_operator(tmpMT2, cacheBase)
            setmetatable(tmp, tmpMT2)
            -- 每个父类记录最上层对象
            tmp.__entity = current
            -- 父类重新指向新对象
            tmpCur.__base = tmp
            tmpCur = tmp
            tmpBase = tmpBase.__base
        end
        current:ctor(...)
        return current
    end

    --- 默认构造函数
    function newClass:ctor(...)
        TinyRush_Log("+ctor>>" .. self.__fullName)
        if self.__base then
            self.__base:ctor(...)
        end
    end

    --- 默认析构函数
    function newClass:dispose()
        TinyRush_Log("-dispose>>" .. self.__fullName)
        if self.__base then
            self.__base:dispose()
        end
    end

    ---@return string
    function newClass:toString()
        return self.__name
    end

    return newClass
end
-------------------

-------------------常用接口
---@class TinyRush_EPlayerLoopTiming @Mono调度器类型
TinyRush_EPlayerLoopTiming = {
    Init = 0,
    FixedUpdate = 1,
    Update = 2,
    LateUpdate = 3
}
---@class TinyRush_IInit : table @位于域启动之前
TinyRush_IInit = {
    rush_OnInit = function(self)
    end
}
---@class TinyRush_IFixedUpdate : table @Mono调度器 OnFixedUpdate
TinyRush_IFixedUpdate = {
    rush_OnFixedUpdate = function(self, deltaTime)
    end
}
---@class TinyRush_IUpdate : table @Mono调度器 OnUpdate
TinyRush_IUpdate = {
    rush_OnUpdate = function(self, deltaTime)
    end
}
---@class TinyRush_ILateUpdate : table @Mono调度器 OnLateUpdate
TinyRush_ILateUpdate = {
    rush_OnLateUpdate = function(self, deltaTime)
    end
}

---@type table<table,TinyRush_EPlayerLoopTiming>
TinyRush_LoopTimingWithType = {}
TinyRush_LoopTimingWithType[TinyRush_IInit] = TinyRush_EPlayerLoopTiming.Init
TinyRush_LoopTimingWithType[TinyRush_IFixedUpdate] = TinyRush_EPlayerLoopTiming.FixedUpdate
TinyRush_LoopTimingWithType[TinyRush_IUpdate] = TinyRush_EPlayerLoopTiming.Update
TinyRush_LoopTimingWithType[TinyRush_ILateUpdate] = TinyRush_EPlayerLoopTiming.LateUpdate
-------------------

-------------------常用基类
-- 所有域类继承此
---@type tinyrush_scope
TinyRush_Scope = require("tinyrush_scope")
-- 自定义生命域继承此
---@type tinyrush_lifescope
TinyRush_LifeScope = require("tinyrush_lifescope")
-- 小游戏入口，继承此类
---@type tinyrush_entrance
TinyRush_Entrance = require("tinyrush_entrance")
-------------------
