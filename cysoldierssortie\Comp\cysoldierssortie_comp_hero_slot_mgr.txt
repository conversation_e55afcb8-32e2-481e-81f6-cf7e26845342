local cysoldierssortie_comp_hero_slot_mgr = bc_Class("cysoldierssortie_comp_hero_slot_mgr") --类名用小游戏名加后缀保证全局唯一
local event = require "event"
local table = table
local gw_hero_mgr                = require "gw_hero_mgr"
local card_assets = require "card_sprite_asset"
local tostring = tostring
local gw_power_mgr = require "gw_power_mgr"
local util = require "util"
local GWG   = GWG
function cysoldierssortie_comp_hero_slot_mgr.__init(self, luaMono, referCol, luaData, ...)
    if luaMono then
        self.luaMono = luaMono
    end
    if referCol then
        referCol:Bind(self)
    end
    if luaData then
        cysoldierssortie_InitLuaData(self, luaData)
    end
end
-- lua脚本正式开始

--生命周期函数
function cysoldierssortie_comp_hero_slot_mgr:OnEnable(data)
    if self.enabledOnce then
        return
    end
    
    self.enabledOnce = true;

    self.dataSrc = cysoldierssortie_CshapToLuaValue(data)
    self.gameObject = self.dataSrc.selfCshap.gameObject
    self.transform = self.dataSrc.selfCshap.transform

end

function cysoldierssortie_comp_hero_slot_mgr:GetHeroPowerSum()
    if not self._slots then
        return
    end

    local power = 0
    power=GWG.GWHomeMgr.droneData.GetDronePower()
    for i = 1,#self._slots do
        local slot =  self._slots[i]
        if not slot:IsEmptySlot() then
            local hero = slot._hero
            power = power + gw_power_mgr.GetHeroPowerByCfgId(hero.heroID)
        end
    end
    return power
end

function cysoldierssortie_comp_hero_slot_mgr:Start()
    self._selectedHero = function(_,dataItem,save_datas,data)
        self._save_datas = save_datas
        if dataItem.isUp == 1 then
            self:CreateHero(dataItem,save_datas)
        else
            self:RemoveHero(dataItem,save_datas)
        end

        local player_power_txt = data.player_power_txt
        if not player_power_txt:IsNull() then
            local hero_power = self:GetHeroPowerSum()
            local minigame_mgr = require "minigame_mgr"
            minigame_mgr.SetHeroPower(hero_power)
            player_power_txt.text = util.NumberWithUnit2(hero_power)
        end
    end
    
    event.Register(event.SELECTED_HERO,self._selectedHero)

    self._onMiniSoldierLevel = function(_,data)
        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        local curLevel = levelMgr.curLevel
        if not curLevel then
            return
        end
        local enemy_avatar_icon = data.enemy_avatar_icon
        local heroID = curLevel.MiniLevelCfg.ShowFaces
        local  facePath =   gw_hero_mgr.ChangeHeroIcon(heroID,1)
        self.face_spriteAsset = self.face_spriteAsset or card_assets.CreateHeroAsset()
        self.face_spriteAsset:GetSprite(facePath,
                function(sprite)
                    enemy_avatar_icon.sprite = sprite
                end)
        
        local level_num_txt = data.level_num_txt
        local minigame_mgr = require "minigame_mgr"
        level_num_txt.text = tostring(minigame_mgr.GetHookLevelId())
        
        local enemy_power_txt = data.enemy_power_txt
        enemy_power_txt.text = util.NumberWithUnit2(curLevel.MiniLevelCfg.LevelPower or 0)
    end
    event.Register(event.MINI_SOLIDER_LEVEL,self._onMiniSoldierLevel)
end

function cysoldierssortie_comp_hero_slot_mgr:OnDestroy()
    event.Unregister(event.SELECTED_HERO,self._selectedHero)
    event.Unregister(event.MINI_SOLIDER_LEVEL,self._onMiniSoldierLevel)
end

function cysoldierssortie_comp_hero_slot_mgr:AddSlot(slot)
    self._slots = self._slots or {}
    self._slots[#self._slots+1] = slot
    slot._slotIndex = #self._slots
end

function cysoldierssortie_comp_hero_slot_mgr:RemoveSlot(slot)
    table.remove_value(self._slots,slot)
end

function cysoldierssortie_comp_hero_slot_mgr:CreateHero(hero,save_datas)
    if not self._slots then
        return
    end
    
    local slotIndex = hero.slotIndex
    if slotIndex then
        self._slots[slotIndex]:CreateHero(hero,slotIndex)
    else
        for i = 1,#self._slots do
            local slot =  self._slots[i]
            if slot:IsEmptySlot() then
                slot:CreateHero(hero,i)
                if save_datas then
                    slotIndex = i
                    hero.slotIndex = slotIndex
                    save_datas[hero.heroID] = hero.slotIndex
                end
                return true
            end
        end
    end
    
    return false
end

function cysoldierssortie_comp_hero_slot_mgr:RemoveHero(hero,save_datas)
    if not self._slots then
        return
    end

    for i = 1,#self._slots do
        local slot =  self._slots[i]
        if not slot:IsEmptySlot() then
            if slot._hero.heroID == hero.heroID then
                slot:RemoveHero()
                if save_datas then
                    save_datas[hero.heroID] = nil
                end
                return
            end
        end
    end
end

function cysoldierssortie_comp_hero_slot_mgr:Swap(slot1,slot2)
    local tmp_parent_1 = slot1.transform.parent
    local entity_1 = slot1._character._character_entity
    entity_1.transform.parent = slot2.transform.parent
    if slot2._character then
        local entity_2 = slot2._character._character_entity
        if entity_2 then
            entity_2.transform.parent = tmp_parent_1
            entity_2.transform.localPosition = {x=0,y=0,z=0}
        end
    end
    entity_1.transform.localPosition = {x=0,y=0,z=0}
    
    
    local tmp_heroID1 = slot1._hero.heroID
    local tmp_heroID2 = slot2._hero and slot2._hero.heroID or nil
    
    local tmp_character = slot1._character
    local tmp_hero = slot1._hero
    slot1._character = slot2._character
    slot1._hero = slot2._hero
    slot2._character = tmp_character
    slot2._hero = tmp_hero

    if self._save_datas then
        self._save_datas[tmp_heroID1] = slot2._slotIndex
        if tmp_heroID2 then
            self._save_datas[tmp_heroID2] = slot1._slotIndex
        end
    end
    
    slot1:RefreshView()
    slot2:RefreshView()

    if cysoldierssortie_TroopClash then
        ---@type troopclash_mgr_level
        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        levelMgr.sceneMgr.playerCtrl:SwapSlot(slot1._slotIndex, slot2._slotIndex)
    end
end

function cysoldierssortie_comp_hero_slot_mgr:GetClosestSlot(heroID,position)
    if not self._slots then
        return
    end
    local minDistance = 9999
    local closest_slot = nil
    for i = 1,#self._slots do
        local slot =  self._slots[i]
        if not slot:IsEmptySlot() then
            if slot._hero.heroID ~= heroID then
                local dist = bc_CS_Vector3.Distance(position,slot:GetPos())
                if dist< minDistance then
                    minDistance = dist
                    closest_slot = slot
                end
            end
        else
            local dist = bc_CS_Vector3.Distance(position,slot:GetPos())
            if dist< minDistance then
                minDistance = dist
                closest_slot = slot
            end
        end
    end
    
    return minDistance,closest_slot
end

function cysoldierssortie_comp_hero_slot_mgr:GetSlotCharacterByIndex(index)
    local character = nil
    local slot = self._slots[index]
    if not slot:IsEmptySlot() then
        character = slot._character
    end
    return character
end

return cysoldierssortie_comp_hero_slot_mgr