---@class tinyrush_mono : TinyRush_Scope @Mono调度器
local tinyrush_mono = TinyRush_CreateClass("tinyrush_mono"):baseClass(TinyRush_Scope)
-- 全局用的Mono
tinyrush_mono.globalMonoGO = nil
tinyrush_mono.luaContainer = nil
tinyrush_mono.monoRun = nil
tinyrush_mono.unity_time = nil

function tinyrush_mono:ctor(...)
    self.globalMonoGO = bc_CS_GameObject(self.__name)
    bc_CS_GameObject.DontDestroyOnLoad(self.globalMonoGO)
    self.luaContainer = bc_CS_GameObjectComs.AddOrGetLuaContainer(self.globalMonoGO)
    self.luaContainer:BindLuaGameObject(self)
    self.__base:ctor(...)
end

function tinyrush_mono:dispose()
    bc_CS_GameObject.Destroy(self.globalMonoGO)
    self.globalMonoGO = nil
    self.luaContainer = nil
    self.unity_time = nil
    self.monoRun = nil
    self.__base:dispose()
end

function tinyrush_mono:initMonoRun(run)
    self.unity_time = CS.UnityEngine.Time
    self.monoRun = run
end

---------------可重写 Unity生命周期------------------
function tinyrush_mono.OnEnable(self)
end
function tinyrush_mono.OnDisable(self)
end
function tinyrush_mono.OnDestroy(self)
end

function tinyrush_mono.Update(self)
    if self.monoRun then
        self.monoRun(self.lifeScope, TinyRush_EPlayerLoopTiming.Update, self.unity_time.deltaTime)
    end
end

function tinyrush_mono.FixedUpdate(self)
    if self.monoRun then
        self.monoRun(self.lifeScope, TinyRush_EPlayerLoopTiming.FixedUpdate, self.unity_time.fixedDeltaTime)
    end
end

function tinyrush_mono.LateUpdate(self)
    if self.monoRun then
        self.monoRun(self.lifeScope, TinyRush_EPlayerLoopTiming.LateUpdate, self.unity_time.deltaTime)
    end
end
-------------------

return tinyrush_mono
