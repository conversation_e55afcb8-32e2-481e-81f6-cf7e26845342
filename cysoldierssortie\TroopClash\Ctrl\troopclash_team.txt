---@class troopclash_team
local team = bc_Class("troopclash_team")
---@type troopclash_scene_mgr
team.sceneMgr = nil
---@type troopclash_res_mgr
team.resMgr = nil
---@type troopclash_ui_mgr
team.uiMgr = nil

---@type fusion_gopool
team.teamPool = nil
---@type troopclash_teamunit[] 记录存活的所有队伍
team.TeamUnitList = nil
---@type troopclash_teamunit[]
team.TeamUnitWithCharacter = nil

function team:__init(...)
    self.sceneMgr, self.resMgr = ...
    self.uiMgr = self.sceneMgr.uiMgr
    self.teamPool = require("fusion_gopool").New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.TeamUnit,
        "troopclash_teamunit")
    self.teamPool:Preload(5)
    self.sceneMgr.TimeSecondBind:Register(function(timer)
        self:TimeSecondListener(timer)
    end)
    self.LookAtTargetSystemInstance = TroopClash_Define.LookAtTargetSystem.Instance
end

function team:Reset()
    self.TeamUnitList = {}
    self.TeamUnitWithCharacter = {}
end

function team:TimeSecondListener(timer)
    local count = #self.sceneMgr.TeamDatasByTime
    if count > 0 then
        local i = 1
        while i <= count do
            local teamData = self.sceneMgr.TeamDatasByTime[i]
            if teamData.Delay <= timer then
                self:SpawnTeam(teamData)
                table.remove(self.sceneMgr.TeamDatasByTime, i)
                count = count - 1
                i = i - 1
            end
            i = i + 1
        end
    end
end

function team:SpawnTeamByPosCheck()
    local count = #self.sceneMgr.TeamDatasByPos
    if count > 0 then
        local i = 1
        while i <= count do
            local teamData = self.sceneMgr.TeamDatasByPos[i]
            if self.sceneMgr.cameraCtrl:CheckInView(teamData.Pos) then
                self:SpawnTeam(teamData)
                table.remove(self.sceneMgr.TeamDatasByPos, i)
                count = count - 1
                i = i - 1
            end
            i = i + 1
        end
    end
end

function team:SpawnTeam(teamData)
    local teamConfig = self.resMgr:GetTeamConfigById(teamData.TeamID)
    ---@type troopclash_teamunit
    local teamUnit = self.teamPool:PopOne()
    teamUnit.transform:SetParent(self.sceneMgr.LevelRoot)
    teamUnit:Init(self.sceneMgr, self, teamData, teamConfig)
    self.TeamUnitList[#self.TeamUnitList + 1] = teamUnit
    if teamConfig.AttentFlag then
        self.uiMgr:ShowBossAttent(true)
    end
end

function team:Update(deltaTime)
    local pX, _, pZ = self.sceneMgr.playerCtrl:GetCenterXYZ()
    self:SpawnTeamByPosCheck()
    for _, v in ipairs(self.TeamUnitList) do
        v:Update(deltaTime, self.sceneMgr.cameraCtrl.ViewCenterWorldPos, pX, pZ)
    end
end

function team:EnemyBeHit(character)
    local teamUnit = self.TeamUnitWithCharacter[character]
    teamUnit:Dissolution()
end

function team:EnemyDie(character)
    character.transform:SetParent(self.sceneMgr.LevelRoot)
    local teamUnit = self.TeamUnitWithCharacter[character]
    if teamUnit ~= nil then
        self.TeamUnitWithCharacter[character] = nil
        local overFlag = teamUnit:CharacterDie(character)
        if overFlag then
            table.remove_value(self.TeamUnitList, teamUnit)
            self.teamPool:PushOne(teamUnit)
        end
    end
end

return team
