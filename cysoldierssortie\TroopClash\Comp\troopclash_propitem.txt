---@class troopclash_propitem : fusion_gopoolitem
local item = bc_Class("troopclash_propitem", require("fusion_gopoolitem"))
item.DataSrc = nil
---@type troopclash_PropConfig
item.config = nil
item.weaponArray = nil

function item:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
    self.weaponArray = { self.DataSrc.Weapon1, self.DataSrc.Weapon2, self.DataSrc.Weapon3 }
end

---@param config troopclash_PropConfig
function item:Init(config)
    self.config = config
    if self.config.Type == TroopClash_Define.PropType.Soldier then
        self.DataSrc.Soldier.gameObject:SetActive(true)
        self.DataSrc.Weapon.gameObject:SetActive(false)
        self.DataSrc.Text.text = tostring(self.config.Param)
    elseif self.config.Type == TroopClash_Define.PropType.Weapon then
        self.DataSrc.Soldier.gameObject:SetActive(false)
        self.DataSrc.Weapon.gameObject:SetActive(true)
        for i, v in ipairs(self.weaponArray) do
            v.gameObject:SetActive(i == self.config.Level)
        end
    end
    self.gameObject:SetActive(true)
end

return item
