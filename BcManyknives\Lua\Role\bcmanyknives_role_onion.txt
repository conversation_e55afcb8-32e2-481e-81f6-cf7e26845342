local manyknives_role_onion = TinyRush_CreateClass("bcmanyknives_role_onion"):baseClass(require(
    "bcmanyknives_rolebase_item"))

-- 出生点设置
function manyknives_role_onion:Init(...)
    self.__base:Init(...)
    self:InitBlade(self.roleData.bladeType, self.roleData.bladeNum)
    self.animCtrl:Play(ManyKnivesDefine.AnimatorState.walk)
end

function manyknives_role_onion:OnUpdate(deltaTime)
    self.__base:OnUpdate(deltaTime)
    self:MoveFollow(deltaTime)
end

return manyknives_role_onion
