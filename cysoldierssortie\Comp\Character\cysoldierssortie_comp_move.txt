local cysoldierssortie_comp_move = bc_Class("cysoldierssortie_comp_move") --类名用小游戏名加后缀保证全局唯一
local UnityEngine = CS.UnityEngine
local math = math
local BulletSystem = CS.cysoldierssortie.BulletSystem
local bc_Time = bc_Time

local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local cysoldierssortie_StartCoroutine = cysoldierssortie_StartCoroutine
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_StopCoroutine = cysoldierssortie_StopCoroutine
local Random = UnityEngine.Random
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local cysoldierssortie_StopDelayCall = cysoldierssortie_StopDelayCall
local log = log

--巡逻状态
local PatrolState = 
{
    Idle = 1,
    Move = 2,
}

local MoveState = 
{
    Normal = 1, --正常移动
    Scared = 2 --惊慌失措
}

local MaxPatrolPointNum = 3
local PatrolRadius = 6
local PatrolInterval = 2

local ScaredRadius = 15 --慌张半径
local MaxPatrolPointWidth = 6   --地图自定义宽度
local MaxPatrolPointHeight = 10 --z轴移动限制
local ClosePlayerLimitDis = 12  --小于这个距离才开始慌张
local DisableObstacleAvoidWhenScared = true --是否慌张的时候自动避障失效

function cysoldierssortie_comp_move:CreatePatrolPoints(patrolInterval,radius)
    self._CurPatrolIndex = 1
    for i = 1,MaxPatrolPointNum do
        local unitSphere =  Random.onUnitSphere
        local x,y,z = GetTransformPositionXYZ(self._transform)
        unitSphere.y = 0
        unitSphere = unitSphere * radius
        unitSphere.x = unitSphere.x + x
        unitSphere.x = math.clamp(unitSphere.x,-MaxPatrolPointWidth/2,MaxPatrolPointWidth/2)

        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        local curLevel = levelMgr.curLevel
        local playerZ = curLevel:GetPlayerZ()
        unitSphere.z = unitSphere.z + z
        unitSphere.z = math.clamp(unitSphere.z,playerZ+5,z+MaxPatrolPointHeight/2)
        self._patrolPoint = self._patrolPoint or {}
        self._patrolPoint[i] = unitSphere
    end
    self._PatrolIdleTimer = bc_Time.time + patrolInterval
    local randomState =  math.random()
    if randomState > 0.5 then
        self._curPatrolState = PatrolState.Move
    else
        self._curPatrolState = PatrolState.Idle
    end
end

function cysoldierssortie_comp_move.__init(self,data)
    self._moveSpeed = data.moveSpeed
    self._character = data.character
    self._character_entity = self._character._character_entity
    self._transform = self._character_entity._modelRoot.transform
    self._oriSpeed = self._moveSpeed
    local levelMgr  = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel = levelMgr.curLevel
    self._ai_nav =  curLevel._ai_nav
    self._ai_state = false
    local enableMoveForward = curLevel:IsEnemyAutoMoveForward()
    if not enableMoveForward then
        self:CreatePatrolPoints(PatrolInterval,PatrolRadius)
    end
    
    self._moveState = MoveState.Normal
end

function  cysoldierssortie_comp_move.__delete(self)
    self:StopDecelerationCoroutine()
    if self._ai_nav then 
        self:StopMove()
        self._character_entity:RemoveNavMeshAgent()
    end

    if not self._ai_state then
        BulletSystem.Instance:UnregisterBullet(self._character.transform)
    end
end

--惊慌表现先和巡逻共用一套
function cysoldierssortie_comp_move:SetScared()
    if self._moveState == MoveState.Scared then
        return
    end    
    
    local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel = levelMgr.curLevel
    local playerZ = curLevel:GetPlayerZ()
    local x,y,z = GetTransformPositionXYZ(self._transform)
    local dis =  cysoldierssortie_lua_util:GetDistance1D(z,playerZ)
    if dis > ClosePlayerLimitDis then
       return
    end
    
    self._moveState = MoveState.Scared
    self:CreatePatrolPoints(0,ScaredRadius)
    self:RandomChangeMoveSpeed()
    if DisableObstacleAvoidWhenScared then
        self._character._character_entity:SwitchObstacleAvoidState(false)
    end
end

function cysoldierssortie_comp_move:ExeMove(px, py, pz,dontCheck)
    if self._moveState == MoveState.Normal then
        self:MoveXYZ(px,py,pz,dontCheck)
    elseif self._moveState == MoveState.Scared then
        self:ScaredMove()
    end
end

--像头雄狮巡逻自己的领地
function cysoldierssortie_comp_move:Patrol()
    if not self._patrolPoint then
        return
    end
    
    if self._curPatrolState == PatrolState.Idle then
        self._character:PlayAnim(cysoldierssortie_hero_anim_set.Stand)
        if bc_Time.time > self._PatrolIdleTimer then
            self._CurPatrolIndex = self._CurPatrolIndex + 1
            self._curPatrolState = PatrolState.Move
            self._last_stuck_check_time = nil
            self._character_entity:StopNav(false)
        end
    elseif self._curPatrolState == PatrolState.Move then
        if self._CurPatrolIndex > #self._patrolPoint then
            self._CurPatrolIndex = 1
        end
        local targetPatrolPoint = self._patrolPoint[self._CurPatrolIndex]
        self:MoveXYZ(targetPatrolPoint.x,targetPatrolPoint.y,targetPatrolPoint.z,true)
        self._character:PlayAnim(cysoldierssortie_hero_anim_set.Run)
        local x,y,z = GetTransformPositionXYZ(self._transform)
        local disToTarget =   cysoldierssortie_lua_util:GetDistance2D(x,z,targetPatrolPoint.x,targetPatrolPoint.z)
        if disToTarget <= 0.1 then
            self._curPatrolState = PatrolState.Idle
            self._PatrolIdleTimer = bc_Time.time + PatrolInterval
            self._character_entity:StopNav(true)
        end
    end
end

function cysoldierssortie_comp_move:ScaredMove()
    if self._CurPatrolIndex > #self._patrolPoint then
        self._CurPatrolIndex = 1
    end
    local targetPatrolPoint = self._patrolPoint[self._CurPatrolIndex]
    self:MoveXYZ(targetPatrolPoint.x,targetPatrolPoint.y,targetPatrolPoint.z,true)
    local x,y,z = GetTransformPositionXYZ(self._transform)
    local disToTarget =   cysoldierssortie_lua_util:GetDistance2D(x,z,targetPatrolPoint.x,targetPatrolPoint.z)
    if disToTarget <= 0.1 then
        self._CurPatrolIndex = self._CurPatrolIndex + 1
    end
end

function cysoldierssortie_comp_move:CreateNavAgent()
    if self._ai_nav then
        self._character_entity:CreateNavMeshAgent()
        self._character_entity:UpdateNavAgentProp(self._moveSpeed)
    end
end

function cysoldierssortie_comp_move:StopDecelerationCoroutine()
    if self._decelerationCoroutine then
        cysoldierssortie_StopDelayCall(self._decelerationCoroutine)
        self._decelerationCoroutine = nil
        self._moveSpeed = self._oriSpeed
        if self._ai_nav then
            self._character_entity:UpdateNavAgentProp(self._moveSpeed)
        end
    end
end

function cysoldierssortie_comp_move:ChangeMoveSpeed(time,percent)
    local hitDeceleration = self._character._hitDeceleration
    if hitDeceleration and hitDeceleration == 1 then 
        self:StopDecelerationCoroutine()
        self._moveSpeed = self._moveSpeed * percent
        if self._ai_nav then
            self._character_entity:UpdateNavAgentProp(self._moveSpeed)
        end
        self._decelerationCoroutine =  cysoldierssortie_DelayCallOnce(time,function()
            self._moveSpeed = self._oriSpeed
            if self._ai_nav then
                self._character_entity:UpdateNavAgentProp(self._moveSpeed)
            end
        end) 
    end
end


local AddSpeedProbability = 0.8 --80%概率增加速度
local ReduceSpeedProbability = 0.2  --20%概率减少速度
function cysoldierssortie_comp_move:RandomChangeMoveSpeed()
    local panicSpeedMultiplier = 1
    if math.random() < AddSpeedProbability then
        --1.2 最小加速倍率 2.0 最大加速倍率
        panicSpeedMultiplier = math.random(1.2,2.0)
    else
        -- 0.6 最小减速倍率 0.9 最大减速倍率
        panicSpeedMultiplier = math.random(0.6,0.9)
    end
    self._moveSpeed = self._moveSpeed * panicSpeedMultiplier
    self._oriSpeed = self._moveSpeed
    if self._ai_nav then
        self._character_entity:UpdateNavAgentProp(self._moveSpeed)
    end
end

function cysoldierssortie_comp_move:CheckStuck()
    -- 卡住检测处理逻辑
    local current_time = bc_Time.time -- 使用Unity的时间系统
    local check_interval = 5 -- 检测间隔5秒

    -- 初始化检测变量
    local x,y,z
    if not self._last_stuck_check_time then
        self._last_stuck_check_time = current_time
        x,y,z = GetTransformPositionXYZ(self._character.transform)
        self._last_stuck_z = z
        self._last_stuck_x = x
    end

    -- 到达检测时间间隔
    if current_time - self._last_stuck_check_time >= check_interval then
        x,y,z = GetTransformPositionXYZ(self._character.transform)
        local px = 0 
        local py = 0
        local pz = 0
        self._player = self._player or   self._character._character_entity._player
        if self._player then
            px,py,pz = self._player:GetPositionXYZ()
        end
        
        local current_z = z
        local current_x = x
        if (pz-current_z)>=10 then
            self._character:Dead()
            return
        end
        -- Z轴位置未变化（考虑0.01的容差）
        if math.abs(current_z - self._last_stuck_z) < 0.01 and math.abs(current_x - self._last_stuck_x)<0.01 then
            self._character:Dead()
            return 
        end

        -- 更新检测记录
        self._last_stuck_z = current_z
        self._last_stuck_x = current_x
        self._last_stuck_check_time = current_time
    end

end

function cysoldierssortie_comp_move:MoveXYZ(px, py, pz,dontCheck)
    if not dontCheck and not cysoldierssortie_TroopClash and not cysoldierssortie_KingShot then
        self:CheckStuck()
    end
    if self._ai_nav and self._character_entity:IsActiveNavAgent() then
        if not self._last_posX or self._last_posX ~= px or self._last_posZ~=pz then
            if not self._ai_state  then
                self._ai_state = true
                BulletSystem.Instance:UnregisterBullet(self._character.transform)
            end
            self._character_entity:SetDestinationXYZ(px, py, pz)
            self._last_posX = px
            self._last_posY = py
            self._last_posZ = pz 
        end
    end
    
    self._character:UpdateEarlyWaning()
end

function cysoldierssortie_comp_move:StartMove()
    self._last_stuck_check_time = nil
    if not self._ai_nav or not self._character_entity:IsActiveNavAgent() then
        BulletSystem.Instance:RegisterBullet(self._character.transform,self._moveSpeed,self._transform.forward)
        self._ai_state = false
    else
        if self._ai_nav  then
            self._character_entity:StopNav(false)
        end
    end
end

function cysoldierssortie_comp_move:StopMove()
    if not self._ai_nav or not self._character_entity:IsActiveNavAgent() then
        BulletSystem.Instance:UnregisterBullet(self._character.transform)
        self._ai_state = false
    else
        if self._ai_nav  then
            self._character_entity:StopNav(true)
        end
    end
end

return cysoldierssortie_comp_move