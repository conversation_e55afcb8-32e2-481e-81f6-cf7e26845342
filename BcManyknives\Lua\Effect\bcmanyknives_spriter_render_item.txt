local poolItemBase = TinyRush_CreateClass("bcmanyknives_spriter_render_item"):baseClass(require("tinyrush_gopoolitem"))

poolItemBase.sceneMgr = nil
poolItemBase.type = nil
poolItemBase.mat = nil
-- 1:boxFill,2:AngleFill
poolItemBase.fillFlag = nil
poolItemBase.targetSize = nil

local Name_Angle = "_Angle"
local Name_Scale = "_Scale"

local boxBound = 0.087 * 2
local scaleBySize = 0.78125

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    self.spriteRender = self.gameObject:GetComponent(typeof(CS.UnityEngine.SpriteRenderer))
    self.spriteRenderFill = self.transform:GetChild(0):GetComponent(typeof(CS.UnityEngine.SpriteRenderer))
    if self.spriteRenderFill.gameObject.name == "Fill" then
        self.fillFlag = 1
    elseif self.spriteRenderFill.gameObject.name == "FillFan" then
        self.fillFlag = 2
        self.mat = bc_CS_GameObject.Instantiate(self.spriteRenderFill.material)
        self.spriteRenderFill.material = self.mat
        self.spriteRender.material = bc_CS_GameObject.Instantiate(self.spriteRender.material)
    elseif self.spriteRenderFill.gameObject.name == "Circle" then
        self.fillFlag = 0
    end
end

function poolItemBase:Init(sceneMgr, type)
    self.sceneMgr = sceneMgr
    self.type = type
    self.transform:SetParent(self.sceneMgr.levelRoot.transform)
end

--- 1:设置尺寸
---@param type number. 1:方形，2:扇形，3:圆形
function poolItemBase:Play(type, ...)
    if type == 1 then
        self.targetSize, self.transform.position, self.transform.rotation = ...
        self.spriteRender.size = self.targetSize
        self:SetFill(0)
    elseif type == 2 then
        local angle = nil
        angle, self.targetSize, self.transform.position, self.transform.rotation = ...
        local tmpScale = (scaleBySize * self.targetSize.x)
        self.spriteRender.transform.localScale = bc_CS_Vector3.one * tmpScale
        self.spriteRender.material:SetFloat(Name_Angle, angle)
        self.mat:SetFloat(Name_Angle, angle)
        self:SetFill(0)
    elseif type == 3 then
        self.targetSize, self.transform.position, self.transform.rotation = ...
        local tmpScale = (scaleBySize * self.targetSize.x)
        self.spriteRender.transform.localScale = bc_CS_Vector3.one * tmpScale
        self:SetFill(0)
    end
end
function poolItemBase:SetFill(value)
    if self.fillFlag == 0 then
        self.spriteRenderFill.transform.localScale = bc_CS_Vector3.one * value
    elseif self.fillFlag == 1 then
        self.spriteRenderFill.size = bc_CS_Vector2(math.lerp(0, self.targetSize.x, value), self.targetSize.y)
    else
        self.spriteRenderFill.transform.localScale = bc_CS_Vector3.one * value
    end
end

function poolItemBase:PushInPool()
    self.sceneMgr:PushEffect(self.type, self)
end

function poolItemBase:GetColliderKey()
    return self.gameObject
end

function poolItemBase:recycle()
end

function poolItemBase:dispose()
    self.recycle()
    self.__base:dispose()
end

return poolItemBase
