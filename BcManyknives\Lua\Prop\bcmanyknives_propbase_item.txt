local poolItemBase = TinyRush_CreateClass("bcmanyknives_propbase_item"):baseClass(require("tinyrush_gopoolitem"))

poolItemBase.type = nil
poolItemBase.collider = nil

function poolItemBase:ctor(...)
    self.__base:ctor(...)
    self.collider = self.gameObject:GetComponent(typeof(CS.UnityEngine.Collider2D))
end

function poolItemBase:Init(sceneMgr, type)
    self.sceneMgr = sceneMgr
    self.type = type
    self:GetColliderKey().layer = ManyKnivesDefine.layerID.triggerEnemy
end

function poolItemBase:GetColl<PERSON><PERSON><PERSON>()
    return self.collider.gameObject
end

function poolItemBase:Trigger()
    self.sceneMgr:PropPoolPushOne(self.type, self)
    return self.type
end

return poolItemBase
