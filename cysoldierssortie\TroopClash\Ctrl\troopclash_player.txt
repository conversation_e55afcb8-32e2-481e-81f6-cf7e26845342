---@class troopclash_player
local player = bc_Class("troopclash_player")
---@type troopclash_scene_mgr
player.sceneMgr = nil
player.DataSrc = nil
player.Transform = nil
player.HeroRoot = nil
player.TargetPoints = nil
player.SlotPoints = nil
player.SlotArray = nil

---@type troopclash_playerunit[] 所有玩家单位
player.unitList = nil
---@type number 记录当前单位数量
player.unitCount = nil
---@type troopclash_playerunit[]
player.unitWithCharacter = nil
---@type troopclash_playerunit[] 所有英雄单位
player.heroList = nil
---@type number 记录当前英雄数量
player.heroCount = nil

player.curVelocity = nil
---@type boolean 是否在移动
player.MotionFlag = nil

---@type fusion_delegateUnRegister
player.dragUnreg = nil
---@type fusion_bindableUnRegister
player.battleFlagUnreg = nil

---@type fusion_bindable 角色半径
player.RadiusBind = nil
---@type number 最大士兵数量,外圈+英雄空余站位
player.maxSoldierNum = nil
---@type table<number,number> 记录每个圆圈的士兵数量
player.CircleNumWithIndex = nil

---@type fusion_bindable 士兵整体等级
player.SoldierLevelBind = nil
---@type table<number,table> 记录每个站圈的位置
player.CirclePosWithIndex = nil
---@type table<number,troopclash_playerunit> 记录每个士兵的圆圈索引
player.SoldierUnitWithCircleIndex = nil
---@type table<number,troopclash_playerunit> 记录站在插槽位置的角色
player.UnitWithSlotIndex = nil

player.tween_SlotToTarget = nil

local slotNum = 5
local centerOriPos = { x = 0, y = 0, z = -0.9 }
local playerUnitClass = require "troopclash_playerunit"
local emptyArray = {}
---@class troopclash_SoldierPosConfig
local soldierPosConfig = {
    baseRadius = 3,       --第一个圆的半径
    increasingRadius = 1, --后面每个圆的半径增量
    spacing = 0.6,        --每个圈的士兵士兵的间隔
    startAngle = math.pi, --开始生成士兵的角度,顺时针
    maxGenerateGroup = 2
}

function player:__init(...)
    self.sceneMgr, self.Transform = ...
    self.DataSrc = {}
    self.Transform:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection):Bind(self.DataSrc)
    self.TargetPoints = {}
    self.SlotPoints = {}
    self.SlotArray = {}
    self.HeroRoot = self.DataSrc._heroRoot
    for i = 1, slotNum do
        self.SlotPoints[i] = self.HeroRoot:Find("Point_" .. i)
        self.SlotArray[i] = self.SlotPoints[i]:Find("Slot")
        self.TargetPoints[i] = self.HeroRoot:Find("TargetPoint_" .. i).localPosition
    end

    self.unitWithCharacter = {}
    self.unitList = {}
    self.unitCount = 0
    self.heroList = {}
    self.heroCount = 0
    self.SoldierUnitWithCircleIndex = {}
    self.UnitWithSlotIndex = {}

    self.battleFlagUnreg = self.sceneMgr.BattleFlagBind:Register(function(value)
        self:BattleFlagListener(value)
    end)
    self.RadiusBind = require("fusion_bindable").New(0)
    self.RadiusBind:Register(function(value)
        --- 调整碰撞体大小
        self.DataSrc.Collider.radius = value
    end)
    self.SoldierLevelBind = require("fusion_bindable").New(1)
    self.maxSoldierNum = self:CalculateMaxSoldierNum()
end

function player:Reset()
    if self.dragUnreg == nil then
        self.dragUnreg = self.sceneMgr.uiMgr.DragEvent:Add(function(vec2)
            self:JoystickOnDrag(vec2)
        end)
    end
    self.curVelocity = TroopClash_Define.CS.Vector3(0, 0, 0)
    TroopClash_Define.SetTransformPositionXYZ(self.Transform, self.sceneMgr.PlayerData.Pos.x,
        self.sceneMgr.PlayerData.Pos.y, self.sceneMgr.PlayerData.Pos.z)
    self:MotionColliderEnabled(false)
    self._actor_instance_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    self.HeroRoot.gameObject:SetActive(true)
    self:ShowSelectHeroUI(true)
end

function player:MotionColliderEnabled(flag)
    self.DataSrc.Collider.enabled = flag
end

function player:ChangeCenterPos(pos)
    TroopClash_Define.SetTransformLocalPositionAndLocalRotation(self.DataSrc.TeamCenter, pos.x, pos.y, pos.z, 0, 0, 0)
    self.DataSrc.Collider.center = { x = pos.x, y = self.RadiusBind.value, z = pos.z }
    self.DataSrc.Rigidbody.centerOfMass = pos
end

---拾取小兵
function player:CreateSolider(num)
    local addNum = math.min(num, self.maxSoldierNum + slotNum - self.unitCount)
    if addNum > 0 then
        for i = 1, addNum do
            self:CreatePlayerCharacter(nil, nil, nil, true)
        end
        self:CalculateColliderSize()
    end
    local offNum = num - addNum
    if offNum > 0 then
        local minLv, maxLv = 999, -1
        for _, v in ipairs(self.unitList) do
            if not v.IsHero then
                minLv = math.min(minLv, v.Level)
                maxLv = math.max(maxLv, v.Level)
            end
        end
        --存在未满级的士兵
        while minLv < TroopClash_Define.Params.SoldierMaxLevel and offNum > 0 do
            for _, v in ipairs(self.unitList) do
                if not v.IsHero then
                    if v.Level == minLv then
                        v:LevelUp(1)
                        offNum = offNum - 1
                    end
                end
            end
            minLv = minLv + 1
        end
    end
end

function player:SoliderLevelUp(lvNum)
    self.SoldierLevelBind:SetValue(math.min(TroopClash_Define.Params.SoldierMaxLevel, self.SoldierLevelBind.value + lvNum))
    for _, v in ipairs(self.unitList) do
        v:LevelUp(lvNum)
    end
end

function player:BattleFlagListener(value)
    if value then
        for i, v in ipairs(self.SlotArray) do
            v.gameObject:SetActive(false)
        end
        self:MotionColliderEnabled(true)
    end
end

function player:SlotToTargetPoint()
    self.tween_SlotToTarget = TroopClash_Define.DOTween.Sequence()
    local moveDur = 0.5
    for i, v in ipairs(self.SlotPoints) do
        self.tween_SlotToTarget:Insert(0, v:DOLocalMove(self.TargetPoints[i], moveDur))
    end
    self.tween_SlotToTarget:SetAutoKill(true)
end

function player:JoystickOnDrag(vec2)
    self.curVelocity = TroopClash_Define.CS.Vector3(vec2.x, 0, vec2.y)
end

function player:ShowSelectHeroUI(show)
    self.DataSrc._selectHeroUI:SetActive(show)
end

function player:GetCenterXYZ()
    return TroopClash_Define.GetTransformPositionXYZ(self.DataSrc.TeamCenter)
end

function player:GetSlotFollowXYZ()
    return TroopClash_Define.GetTransformPositionXYZ(self.DataSrc.rfCameraFollow)
end

function player:GetCenterVec3()
    return self.DataSrc.TeamCenter.position
end

function player:Update(deltaTime)
    --是否在移动
    self.MotionFlag = self.curVelocity.x ~= 0 or self.curVelocity.z ~= 0
    self.DataSrc.Rigidbody.velocity = self.curVelocity * TroopClash_Define.Params.PlayerMoveSpeed
    local tarRotate = nil
    if self.MotionFlag then
        tarRotate = TroopClash_Define.CS.Quaternion.LookRotation(self.curVelocity)
    end
    for _, v in ipairs(self.unitList) do
        v:SetMoveFlag(self.MotionFlag)
        v:Update(tarRotate, deltaTime)
    end
end

function player:StopAll()
    for _, v in ipairs(self.unitList) do
        v.character:ResetCooldown()
    end
end

function player:KillTween_SlotToTarget()
    if self.tween_SlotToTarget ~= nil then
        self.tween_SlotToTarget:Kill()
        self.tween_SlotToTarget = nil
    end
end

function player:__delete()
    self:KillTween_SlotToTarget()
    if self.dragUnreg ~= nil then
        self.dragUnreg:UnRegister()
        self.dragUnreg = nil
    end
    if self.battleFlagUnreg ~= nil then
        self.battleFlagUnreg:UnRegister()
        self.battleFlagUnreg = nil
    end
end

function player:SwapSlot(index1, index2)
    ---@type troopclash_playerunit
    local unit1 = self.UnitWithSlotIndex[index1]
    ---@type troopclash_playerunit
    local unit2 = self.UnitWithSlotIndex[index2]
    if unit1 ~= nil then
        unit1.PosIndex = index2
    end
    if unit2 ~= nil then
        unit1.PosIndex = index1
    end
    self.UnitWithSlotIndex[index1] = unit2
    self.UnitWithSlotIndex[index2] = unit1
end

---计算碰撞体尺寸
function player:CalculateColliderSize()
    local newCenterPos = nil
    local max_radius = 1
    local sum_x, sum_z = 0, 0
    local sumSlotNum = 0
    for i = 1, slotNum do
        if self.UnitWithSlotIndex[i] ~= nil then
            sumSlotNum = sumSlotNum + 1
            local tmpPos = self.TargetPoints[i]
            sum_x = sum_x + tmpPos.x
            sum_z = sum_z + tmpPos.z
        end
    end
    local circleRadius = 0
    local circleIndex = 0
    local tmpI = 1
    while tmpI <= self.maxSoldierNum do
        if self.SoldierUnitWithCircleIndex[tmpI] ~= nil then
            local curIndex = 1
            for i = 1, soldierPosConfig.maxGenerateGroup do
                if tmpI <= self.CircleNumWithIndex[i] then
                    curIndex = i
                    break
                end
            end
            if curIndex > circleIndex then
                circleIndex = curIndex
                tmpI = self.CircleNumWithIndex[curIndex]
            end
            local newRadius = soldierPosConfig.baseRadius + (curIndex - 1) * soldierPosConfig.increasingRadius
            circleRadius = math.max(newRadius, circleRadius)
            if circleIndex >= soldierPosConfig.maxGenerateGroup then
                break
            end
        end
        tmpI = tmpI + 1
    end
    if circleRadius > 0 then
        max_radius = math.max(max_radius, circleRadius)
        newCenterPos = centerOriPos
    else
        -- 计算最大距离（半径）
        local centerX, centerZ = sum_x / sumSlotNum, sum_z / sumSlotNum
        for i = 1, slotNum do
            if self.UnitWithSlotIndex[i] ~= nil then
                local tmpPos = self.TargetPoints[i]
                local dx = tmpPos.x - centerX
                local dz = tmpPos.z - centerZ
                local distance = math.sqrt(dx ^ 2 + dz ^ 2)
                max_radius = math.max(max_radius, distance)
            end
        end
        newCenterPos = { x = centerX, y = centerOriPos.y, z = centerZ }
    end
    self.RadiusBind:SetValue(max_radius)
    self:ChangeCenterPos(newCenterPos)
end

---计算最大士兵数量
function player:CalculateMaxSoldierNum()
    self.CirclePosWithIndex = {}
    self.CircleNumWithIndex = {}
    local tmpPosIndex = 0
    local circleRadius = soldierPosConfig.baseRadius
    local totalSoldiers = 0
    local index = 1
    while true do
        local circumference = 2 * math.pi * circleRadius
        local tmpNums = math.floor(circumference / soldierPosConfig.spacing)
        local perAngle = 360 / tmpNums
        for i = 1, tmpNums, 1 do
            local deg = (tmpNums - i) * perAngle
            local x = circleRadius * math.cos(math.rad(deg))
            local y = circleRadius * math.sin(math.rad(deg))
            tmpPosIndex = tmpPosIndex + 1
            self.CirclePosWithIndex[tmpPosIndex] = { x = x, y = 0, z = y }
        end
        self.CircleNumWithIndex[index] = tmpNums
        totalSoldiers = totalSoldiers + tmpNums
        if index >= soldierPosConfig.maxGenerateGroup then
            break
        else
            circleRadius = circleRadius + soldierPosConfig.increasingRadius
        end
        index = index + 1
    end
    return totalSoldiers
end

--- 角色开始射击
function player:CharacterFire(character)
    local unit = self.unitWithCharacter[character]
    unit:Fire()
end

function player:CreateAttackRange(unitID, attackRange)
end

function player:GetTargetObjs(character)
    return emptyArray
end

function player:GetTargetObjsByWeaponID(weaponID)
    return emptyArray
end

function player:RemoveRangeEnemy()
end

function player:GetUnitIDByHeroID(heroID, level)
    level = level or 1
    self._unitIDMapHeroID = self._unitIDMapHeroID or {}
    local levelKey = heroID .. "@" .. level
    local unitID = self._unitIDMapHeroID[levelKey]
    if not unitID then
        for _, unit in pairs(self.sceneMgr.resMgr.UnitConfigs) do
            if unit.UnitType == 4 or unit.UnitType == 6 then -- 4—英雄
                if unit.ModelID and unit.ModelID > 0 then
                    local tmpKey = unit.ModelID .. "@" .. unit.UnitLevel
                    if not self._unitIDMapHeroID[tmpKey] then
                        self._unitIDMapHeroID[tmpKey] = unit.ID
                    end
                end
            end
        end
        unitID = self._unitIDMapHeroID[levelKey]
    end
    return unitID
end

--unitId 单位id(士兵传nil)，hero英雄数据
function player:CreatePlayerCharacter(unitId, hero, slotIndex, playEffect)
    local isHero = false
    local slotEmptyArray, slotEmptyCount = self:GetEmptyHeroSlot()
    -- 是小兵
    if not unitId then
        --队伍已满
        if self.unitCount >= self.maxSoldierNum + slotNum then
            return
        end
        unitId = TroopClash_Define.SoldierUnitIDWithLevel[self.SoldierLevelBind.value]
    end
    local curHeroId = nil
    local hp = nil
    local atk = nil
    if hero then
        isHero = true
        curHeroId = hero and hero.heroID
        hp = hero and hero.battleProp.hp
        atk = hero and hero.battleProp.attack
    end
    local parent = nil
    local localPos = nil
    local circleIndex = nil
    if slotEmptyCount > 0 then
        if slotIndex == nil then
            slotIndex = slotEmptyArray[1]
        end
        parent = self.SlotPoints[slotIndex]
        localPos = TroopClash_Define.CacheVector3.Zero
    else
        parent = self.DataSrc.rfSoldierRoot
        circleIndex = self:GetEmptyCircleIndex()
        localPos = self.CirclePosWithIndex[circleIndex]
    end
    local character = self._actor_instance_mgr:CreateCharacter(unitId, localPos, parent, true,
        curHeroId, hp, atk, self, playEffect)
    ---@type troopclash_playerunit
    local unitItem = playerUnitClass.New(unitId, character, isHero)
    unitItem.InSlot = circleIndex == nil
    unitItem.PosIndex = unitItem.InSlot and slotIndex or circleIndex
    self.unitCount = self.unitCount + 1
    self.unitList[self.unitCount] = unitItem
    self.unitWithCharacter[character] = unitItem
    --是英雄
    if isHero then
        self.heroCount = self.heroCount + 1
        self.heroList[self.heroCount] = unitItem
    end
    if unitItem.InSlot then
        self.UnitWithSlotIndex[slotIndex] = unitItem
    else
        self.SoldierUnitWithCircleIndex[circleIndex] = unitItem
    end
    unitItem:Reset()
    return character
end

function player:GetEmptyHeroSlot()
    local emptyArray = {}
    local emptyCount = 0
    for i = 1, slotNum do
        if self.UnitWithSlotIndex[i] == nil then
            emptyCount = emptyCount + 1
            emptyArray[emptyCount] = i
        end
    end
    return emptyArray, emptyCount
end

---获取空出的圆圈索引
function player:GetEmptyCircleIndex()
    for i = 1, self.maxSoldierNum do
        if self.SoldierUnitWithCircleIndex[i] == nil then
            return i
        end
    end
end

function player:GetCHeroList()
    return self.heroList
end

---玩家单位死亡,英雄和小兵都调用
function player:OnHeroDead(character)
    local unit = self.unitWithCharacter[character]
    unit:Die()
    self.unitWithCharacter[character] = nil
    table.remove_value(self.unitList, unit)
    self.unitCount = self.unitCount - 1
    if unit.InSlot then
        self.UnitWithSlotIndex[unit.PosIndex] = nil
    else
        self.SoldierUnitWithCircleIndex[unit.PosIndex] = nil
    end
    if unit.IsHero then
        table.remove_value(self.heroList, self.unitWithCharacter[character])
        self.heroCount = self.heroCount - 1
    end
    if self.sceneMgr.BattleFlagBind.value then
        if self.unitCount < 1 then
            self.sceneMgr:GameOver(false)
            return
        end
        self:CalculateColliderSize()
    end
end

function player:RecycleSoldierPos(localPos, parent)
end

function player:GetAllSoldierDps()
    return 0
end

return player
