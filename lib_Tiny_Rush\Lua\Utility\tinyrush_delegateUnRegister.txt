---@class tinyrush_delegateUnRegister : TRClass @事件注销
local delegateUnRegister = TinyRush_CreateClass("tinyrush_delegateUnRegister")
delegateUnRegister.delegate = nil
delegateUnRegister.event = nil

function delegateUnRegister:ctor(...)
    self.delegate, self.event = ...
end

function delegateUnRegister:unRegister()
    if self.delegate ~= nil then
        self.delegate:remove(self.event)
    end
    self.delegate = nil
    self.event = nil
end

return delegateUnRegister
