---@class fusion_load_mgr : fusion_mgrbase Loading界面时用于加载必须资源。分帧加载，避免造成IO阻塞。
local mgr = bc_Class("fusion_load_mgr", Fusion.MgrBase)
mgr.taskQueue = nil
mgr.taskCompleted = nil
mgr.uTaskCount = nil
mgr.uTaskIndex = nil
mgr.uTaskContinueFlag = nil
mgr.uTaskOverCount = nil
mgr.uTaskOverIndex = nil
mgr.uTaskComFlag = nil
mgr.readyFlag = nil
---@type boolean 是否显示日志
mgr.showLoadLog = nil
---@type boolean 是否并行加载
mgr.parallelFlag = nil
---@type table[] 加载器列表，用于游戏关闭后释放资源
mgr.loaderList = nil

local asset_loader = require("asset_loader")
local loaderTag = "fusion_load_mgr"

function mgr:__init(...)
    self.loaderList = {}
    local logFlag, parallel = ...
    if type(logFlag) == "boolean" then
        self.showLoadLog = logFlag
    else
        self.showLoadLog = false
    end
    if type(parallel) == "boolean" then
        self.parallelFlag = parallel
    else
        self.parallelFlag = false
    end
    self:ClearTask()
end

function mgr:ClearTask()
    self.taskQueue = {}
    self.taskCompleted = {}
    self.readyFlag = false
end

--- 增加一个任务：每帧只执行一个任务。
function mgr:AddFrameTask(func)
    self.taskQueue[#self.taskQueue + 1] = { nil, func }
end

--- 加载资源任务添加
---@param abName string
---@param call fun()
function mgr:AddLoadTask(abName, call)
    self.taskQueue[#self.taskQueue + 1] = { abName, call }
end

--- 添加所有任务完成后的回调
---@param call fun()
function mgr:AddLoadedCall(call)
    self.taskCompleted[#self.taskCompleted + 1] = call
end

--- 调用此方法，才开始执行加载任务
function mgr:LoadingInvoke()
    self.readyFlag = true
    if self.parallelFlag then
        local taskCount = #self.taskQueue
        local checkCompleted = function()
            taskCount = taskCount - 1
            if taskCount < 1 then
                for _, v in ipairs(self.taskCompleted) do
                    v()
                end
            end
        end
        for _, v in ipairs(self.taskQueue) do
            local abName = v[1]
            local func = v[2]
            if abName ~= nil then
                local newLoader = asset_loader(abName, loaderTag)
                newLoader:load(function(loader)
                    func(loader.asset)
                    checkCompleted()
                end)
            else
                func()
                checkCompleted()
            end
        end
    else
        self.uTaskIndex = 1
        self.uTaskCount = #self.taskQueue
        self.uTaskContinueFlag = false
        self.uTaskOverCount = #self.taskCompleted
        self.uTaskOverIndex = 1
        self.uTaskComFlag = false
    end
end

function mgr:OnUpdate()
    if not self.readyFlag or self.parallelFlag then
        return
    end
    self:invokeTaskUpdate()
end

function mgr:invokeTaskUpdate()
    if self.uTaskCount > 0 and self.uTaskIndex <= self.uTaskCount then
        if not self.uTaskContinueFlag then
            self.uTaskContinueFlag = true
            local tmpT = self.taskQueue[self.uTaskIndex]
            local abName = tmpT[1]
            local func = tmpT[2]
            if self.showLoadLog then
                Fusion.Log("loadTaskQueue:", self.uTaskIndex .. "/" .. self.uTaskCount, abName)
            end
            self.uTaskIndex = self.uTaskIndex + 1
            if abName ~= nil then
                local newLoader = asset_loader(abName, loaderTag)
                newLoader:load(function(loader)
                    func(loader.asset)
                    self.uTaskContinueFlag = false
                    self.uTaskComFlag = self.uTaskIndex > self.uTaskCount and self.uTaskOverCount > 0
                end)
                self.loaderList[#self.loaderList + 1] = newLoader
            else
                func()
                self.uTaskContinueFlag = false
                self.uTaskComFlag = self.uTaskIndex > self.uTaskCount and self.uTaskOverCount > 0
            end
        end
    end
    if self.uTaskComFlag and self.uTaskOverIndex <= self.uTaskOverCount then
        if not self.uTaskContinueFlag then
            self.uTaskContinueFlag = true
            local func = self.taskCompleted[self.uTaskOverIndex]
            if self.showLoadLog then
                Fusion.Log("loadCompleted:", self.uTaskOverIndex .. "/" .. self.uTaskOverCount)
            end
            func()
            self.uTaskContinueFlag = false
            self.uTaskOverIndex = self.uTaskOverIndex + 1
        end
    end
end

function mgr:__delete()
    self.readyFlag = false
    self.taskQueue = nil
    self.taskCompleted = nil
    for _, v in ipairs(self.loaderList) do
        v:Dispose()
    end
    self.loaderList = nil
end

return mgr
