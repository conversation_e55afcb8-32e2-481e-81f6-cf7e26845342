local lang_mgr = TinyRush_CreateClass("bcmanyknives_lang_mgr"):baseClass(TinyRush_Scope):interface(
    TinyRush_IInit)

lang_mgr.resMgr = nil
lang_mgr.tinyLang = nil
lang_mgr.langStr = nil

function lang_mgr:rush_OnInit()
    self.resMgr = self.lifeScope:get("bcmanyknives_res_mgr")
    self.tinyLang = require("tiny_lang")
    self.langStr = self.tinyLang.GetLangStr()
end

function lang_mgr:Ready()
    self.tinyLang.ParserText(self.resMgr.langData.text)
end

function lang_mgr:GetLangById(id)
    return self.tinyLang.GetLangLua(id)
end

function lang_mgr:GetLangStr()
    return self.langStr;
end

return lang_mgr
