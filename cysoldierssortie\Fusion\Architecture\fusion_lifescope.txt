Fusion = require("fusion_define")
---@class fusion_lifescope
local lifeScope = bc_Class("fusion_lifescope")
---@type table 小游戏入口
lifeScope.enter = nil
---@type table MiniLevel内的关卡配置
lifeScope.miniLvConfig = nil
---@type table<string, fusion_mgrbase> 记录所有管理器
lifeScope.iocPairs = nil
---@type table<fusion_MonoLoopType, table[]> Mono调度器
lifeScope.unityDispatcher = nil
---@type fusion_SafeArea 获取安全区域数据
lifeScope.SafeArea = nil

--- 构造函数
function lifeScope:__init(...)
    self:Ctor(...)
end

function lifeScope:Ctor(...)
    self.enter, self.miniLvConfig = ...
    self.iocPairs = {}
    self.unityDispatcher = {}
end

--- 装载管理器,需要重写
function lifeScope:Configure()
end

--- 往 cysoldierssortie_mgrTable 中注册需要替换的mgr ,需要重写
function lifeScope:BuildMiddleware()
end

--- 管理器初始化
function lifeScope:BuildAndInit()
    local updateIndex = 0
    local fixedUpdateIndex = 0
    local lateUpdateIndex = 0
    local updates = {}
    local lateUpdates = {}
    local fixedUpdates = {}
    for _, v in pairs(self.iocPairs) do
        if v.OnUpdate ~= nil then
            updateIndex = updateIndex + 1
            updates[updateIndex] = {
                obj = v,
                func = v.OnUpdate
            }
        end
        if v.OnFixedUpdate ~= nil then
            fixedUpdateIndex = fixedUpdateIndex + 1
            fixedUpdates[fixedUpdateIndex] = {
                obj = v,
                func = v.OnFixedUpdate
            }
        end
        if v.OnLateUpdate ~= nil then
            lateUpdateIndex = lateUpdateIndex + 1
            lateUpdates[lateUpdateIndex] = {
                obj = v,
                func = v.OnLateUpdate
            }
        end
        v:Init(self)
    end
    self.unityDispatcher[Fusion.MonoLoopType.OnUpdate] = updates
    self.unityDispatcher[Fusion.MonoLoopType.OnFixedUpdate] = fixedUpdates
    self.unityDispatcher[Fusion.MonoLoopType.OnLateUpdate] = lateUpdates

    ---@type fusion_mono
    local mono = self:GetMgr("fusion_mono")
    mono:InitMonoRun(self.monoRun)
end

--- 初始化生命周期，结束后运行游戏逻辑
function lifeScope:Build(callBack)
    self:Configure()
    self:BuildAndInit()
    self:BuildMiddleware()
    callBack()
end

function lifeScope:RegisterMgr(mgrClass)
    self.iocPairs[mgrClass._class_type.__cname] = mgrClass
end

function lifeScope:GetMgr(className)
    return self.iocPairs[className]
end

---@param loopItemType fusion_MonoLoopType 循环类型
---@param deltaTime number 时间间隔
function lifeScope:monoRun(loopItemType, deltaTime)
    local table = self.unityDispatcher[loopItemType]
    for _, v in pairs(table) do
        v.func(v.obj, deltaTime)
    end
end

--- 析构函数
function lifeScope:__delete()
    self:Dispose()
end

function lifeScope:Dispose()
    --析构所有Mgr
    for _, v in pairs(self.iocPairs) do
        v:Delete()
    end
    self.iocPairs = nil
end

--- 获取安全区域数据
---@return fusion_SafeArea
function lifeScope:GetSafeArea()
    ---@class fusion_SafeArea
    local safeArea = {
        TopRatio = 0, -- 屏幕占比(0-1)
        BotRatio = 0, --屏幕占比(0-1)
        ScreenWidth = 0,
        ScreenHeight = 0
    }
    local unityScreen = CS.UnityEngine.Screen
    if unityScreen.height > unityScreen.safeArea.yMax then
        safeArea.TopRatio = (unityScreen.height - unityScreen.safeArea.yMax) / unityScreen.height
    end
    if unityScreen.safeArea.yMin > 0 then
        safeArea.BotRatio = unityScreen.safeArea.yMin / unityScreen.height
    end
    safeArea.ScreenHeight = unityScreen.height
    safeArea.ScreenWidth = unityScreen.width
    return safeArea
end

--- 缓存渲染设置
function lifeScope:CacheRenderSettings()
    local RenderSettings = CS.UnityEngine.RenderSettings
    self.env_fog = RenderSettings.fog
    self.env_FogStart = RenderSettings.fogStartDistance
    self.env_FogEnd = RenderSettings.fogEndDistance
    self.env_FogMode = RenderSettings.fogMode
    self.env_FogColor = RenderSettings.fogColor
    self.env_fogDensity = RenderSettings.fogDensity
    self.env_ambientMode = RenderSettings.ambientMode
    self.env_ambientSkyColor = RenderSettings.ambientSkyColor
    self.env_ambientEquatorColor = RenderSettings.ambientEquatorColor
    self.env_ambientGroundColor = RenderSettings.ambientGroundColor
    self.env_ambientIntensity = RenderSettings.ambientIntensity
    self.env_ambientLight = RenderSettings.ambientLight
    self.env_subtractiveShadowColor = RenderSettings.subtractiveShadowColor
    self.env_skybox = RenderSettings.skybox
    self.env_sun = RenderSettings.sun
    self.env_ambientProbe = RenderSettings.ambientProbe
    self.env_customReflection = RenderSettings.customReflection
    self.env_reflectionIntensity = RenderSettings.reflectionIntensity
    self.env_reflectionBounces = RenderSettings.reflectionBounces
    self.env_defaultReflectionMode = RenderSettings.defaultReflectionMode
    self.env_defaultReflectionResolution = RenderSettings.defaultReflectionResolution
    self.env_haloStrength = RenderSettings.haloStrength
    self.env_flareStrength = RenderSettings.flareStrength
    self.env_flareFadeSpeed = RenderSettings.flareFadeSpeed
end

--- 恢复渲染设置
function lifeScope:RestoreRenderSettings()
    local RenderSettings = CS.UnityEngine.RenderSettings
    RenderSettings.fog = self.env_fog
    RenderSettings.fogStartDistance = self.env_FogStart
    RenderSettings.fogEndDistance = self.env_FogEnd
    RenderSettings.fogMode = self.env_FogMode
    RenderSettings.fogColor = self.env_FogColor
    RenderSettings.fogDensity = self.env_fogDensity
    RenderSettings.ambientMode = self.env_ambientMode
    RenderSettings.ambientSkyColor = self.env_ambientSkyColor
    RenderSettings.ambientEquatorColor = self.env_ambientEquatorColor
    RenderSettings.ambientGroundColor = self.env_ambientGroundColor
    RenderSettings.ambientIntensity = self.env_ambientIntensity
    RenderSettings.ambientLight = self.env_ambientLight
    RenderSettings.subtractiveShadowColor = self.env_subtractiveShadowColor
    RenderSettings.skybox = self.env_skybox
    RenderSettings.sun = self.env_sun
    RenderSettings.ambientProbe = self.env_ambientProbe
    RenderSettings.customReflection = self.env_customReflection
    RenderSettings.reflectionIntensity = self.env_reflectionIntensity
    RenderSettings.reflectionBounces = self.env_reflectionBounces
    RenderSettings.defaultReflectionMode = self.env_defaultReflectionMode
    RenderSettings.defaultReflectionResolution = self.env_defaultReflectionResolution
    RenderSettings.haloStrength = self.env_haloStrength
    RenderSettings.flareStrength = self.env_flareStrength
    RenderSettings.flareFadeSpeed = self.env_flareFadeSpeed
end

--- 缓存3D物理设置
function lifeScope:CachePhysics3DSettings()
    local Physics = CS.UnityEngine.Physics
    self.phys_gravity = Physics.gravity
    self.phys_defaultContactOffset = Physics.defaultContactOffset
    self.phys_sleepThreshold = Physics.sleepThreshold
    self.phys_queriesHitTriggers = Physics.queriesHitTriggers
    self.phys_queriesHitBackfaces = Physics.queriesHitBackfaces
    self.phys_bounceThreshold = Physics.bounceThreshold
    self.phys_defaultMaxDepenetrationVelocity = Physics.defaultMaxDepenetrationVelocity
    self.phys_defaultSolverIterations = Physics.defaultSolverIterations
    self.phys_defaultSolverVelocityIterations = Physics.defaultSolverVelocityIterations
    self.phys_defaultMaxAngularSpeed = Physics.defaultMaxAngularSpeed
    self.phys_autoSimulation = Physics.autoSimulation
    self.phys_autoSyncTransforms = Physics.autoSyncTransforms
    self.phys_reuseCollisionCallbacks = Physics.reuseCollisionCallbacks
    self.phys_IgnoreLayer = {}
    local physGetIgnoreLayer = Physics.GetIgnoreLayerCollision
    local physIgnoreLayer = Physics.IgnoreLayerCollision
    local allLayer = { 0, 12, 13, 14, 15, 16 }
    for _, i in ipairs(allLayer) do
        for _, j in ipairs(allLayer) do
            local index = i * 100 + j
            local index2 = i + j * 100
            if self.phys_IgnoreLayer[index] == nil and self.phys_IgnoreLayer[index2] == nil then
                local tmpFlag = physGetIgnoreLayer(i, j)
                self.phys_IgnoreLayer[index] = tmpFlag
                self.phys_IgnoreLayer[index2] = tmpFlag
                physIgnoreLayer(i, j, false)
            end
        end
    end
end

--- 恢复3D物理设置
function lifeScope:RestorePhysics3DSettings()
    local Physics = CS.UnityEngine.Physics
    Physics.gravity = self.phys_gravity
    Physics.defaultContactOffset = self.phys_defaultContactOffset
    Physics.sleepThreshold = self.phys_sleepThreshold
    Physics.queriesHitTriggers = self.phys_queriesHitTriggers
    Physics.queriesHitBackfaces = self.phys_queriesHitBackfaces
    Physics.bounceThreshold = self.phys_bounceThreshold
    Physics.defaultMaxDepenetrationVelocity = self.phys_defaultMaxDepenetrationVelocity
    Physics.defaultSolverIterations = self.phys_defaultSolverIterations
    Physics.defaultSolverVelocityIterations = self.phys_defaultSolverVelocityIterations
    Physics.defaultMaxAngularSpeed = self.phys_defaultMaxAngularSpeed
    Physics.autoSimulation = self.phys_autoSimulation
    Physics.autoSyncTransforms = self.phys_autoSyncTransforms
    Physics.reuseCollisionCallbacks = self.phys_reuseCollisionCallbacks
    local physIgnoreLayer = Physics.IgnoreLayerCollision
    local allLayer = { 0, 12, 13, 14, 15, 16 }
    for _, i in ipairs(allLayer) do
        for _, j in ipairs(allLayer) do
            local index = i * 100 + j
            local index2 = i + j * 100
            if self.phys_IgnoreLayer[index] ~= nil or self.phys_IgnoreLayer[index2] ~= nil then
                local tmpFlag = self.phys_IgnoreLayer[index]
                if tmpFlag == nil then
                    tmpFlag = self.phys_IgnoreLayer[index2]
                end
                physIgnoreLayer(i, j, tmpFlag)
            end
        end
    end
end

--- 缓存质量设置
function lifeScope:CacheQualitySettings()
    local QualitySettings = CS.UnityEngine.QualitySettings
    self.qlty_shadowDistance = QualitySettings.shadowDistance
    self.qlty_shadows = QualitySettings.shadows
    self.qlty_shadowResolution = QualitySettings.shadowResolution
    self.qlty_shadowProjection = QualitySettings.shadowProjection
    self.qlty_softParticles = QualitySettings.softParticles
    self.qlty_antiAliasing = QualitySettings.antiAliasing
end

--- 恢复质量设置
function lifeScope:RestoreQualitySettings()
    local QualitySettings = CS.UnityEngine.QualitySettings
    QualitySettings.shadowDistance = self.qlty_shadowDistance
    QualitySettings.shadows = self.qlty_shadows
    QualitySettings.shadowResolution = self.qlty_shadowResolution
    QualitySettings.shadowProjection = self.qlty_shadowProjection
    QualitySettings.softParticles = self.qlty_softParticles
    QualitySettings.antiAliasing = self.qlty_antiAliasing
end

--- 缓存其他App设置
function lifeScope:CacheAppSettings()
    self.cache_targetFrameRate = CS.UnityEngine.Application.targetFrameRate
    self.cache_fixedDeltaTime = CS.UnityEngine.Time.fixedDeltaTime
    self.cache_timeScale = CS.UnityEngine.Time.timeScale
    self.cache_pixelDragThreshold = CS.UnityEngine.EventSystems.EventSystem.current.pixelDragThreshold
    self.cache_multiTouchEnabled = CS.UnityEngine.multiTouchEnabled
end

--- 恢复其他App设置
function lifeScope:RestoreAppSettings()
    CS.UnityEngine.Application.targetFrameRate = self.cache_targetFrameRate
    CS.UnityEngine.Time.fixedDeltaTime = self.cache_fixedDeltaTime
    CS.UnityEngine.Time.timeScale = self.cache_timeScale
    CS.UnityEngine.EventSystems.EventSystem.current.pixelDragThreshold = self.cache_pixelDragThreshold
    CS.UnityEngine.multiTouchEnabled = self.cache_multiTouchEnabled
end

return lifeScope
