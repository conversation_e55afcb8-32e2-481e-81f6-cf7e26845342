---@class fusion_delegateUnRegister 事件注销
local delegateUnRegister = bc_Class("fusion_delegateUnRegister")
---@type fusion_delegate
delegateUnRegister.delegate = nil
delegateUnRegister.event = nil

function delegateUnRegister:__init(...)
    self.delegate, self.event = ...
end

function delegateUnRegister:UnRegister()
    if self.delegate ~= nil then
        self.delegate:Remove(self.event)
    end
    self.delegate = nil
    self.event = nil
end

return delegateUnRegister
