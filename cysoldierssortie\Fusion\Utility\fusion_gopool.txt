---@class fusion_gopool GameObject对象池
local goPool = bc_Class("fusion_gopool")
goPool.poolTable = nil  -- 物体池
goPool.poolTableCount = nil
goPool.cacheTable = nil -- 已取出的物体
goPool.cacheTableCount = nil
goPool.poolParent = nil
goPool.strFileName = nil
goPool.prefab = nil
---@type boolean 是否绑定了Lua脚本
goPool.bindFlag = nil

local GameObject = CS.UnityEngine.GameObject
-- 新建对象
function goPool:allocate(go)
    return require(self.strFileName).New(go)
end

--- 预加载
---@param count number 预加载数量
function goPool:Preload(count)
    for i = 1, count, 1 do
        local go = GameObject.Instantiate(self.prefab, self.poolParent)
        if self.bindFlag then
            self.poolTable[i] = self:allocate(go)
        else
            self.poolTable[i] = go
        end
        go:SetActive(false)
    end
    self.poolTableCount = count
end

--- 从池里去除一个目标
---@return fusion_gopoolitem
function goPool:PopOne()
    local result = nil
    if self.poolTableCount > 0 then
        result = table.remove(self.poolTable, self.poolTableCount)
        self.poolTableCount = self.poolTableCount - 1
    else
        result = GameObject.Instantiate(self.prefab)
        if self.bindFlag then
            result = self:allocate(result)
        end
    end
    self.cacheTableCount = self.cacheTableCount + 1
    self.cacheTable[self.cacheTableCount] = result
    return result
end

--- 装回到池内
---@param item fusion_gopoolitem
function goPool:PushOne(item)
    if self.bindFlag then
        item:Recycle()
    end
    item.gameObject:SetActive(false)
    item.transform:SetParent(self.poolParent)
    table.remove_value(self.cacheTable, item)
    self.cacheTableCount = self.cacheTableCount - 1
    self.poolTableCount = self.poolTableCount + 1
    self.poolTable[self.poolTableCount] = item
end

--- 清空已用列表，全回到池内
function goPool:Clear()
    if self.cacheTableCount > 0 then
        for _, value in ipairs(self.cacheTable) do
            if self.bindFlag then
                value:Recycle()
            end
            value.gameObject:SetActive(false)
            value.transform:SetParent(self.poolParent)
            self.poolTableCount = self.poolTableCount + 1
            self.poolTable[self.poolTableCount] = value
        end
        self.cacheTable = {}
        self.cacheTableCount = 0
    end
end

--- 构造
---@param poolRoot table GameObject 池父物体
---@param prefab table GameObject 预制
---@param strFileName string 可以绑定lua脚本,继承 tinyrush_gopoolitem
function goPool:__init(poolRoot, prefab, strFileName)
    self.poolTable = {}
    self.cacheTable = {}
    self.poolTableCount = 0
    self.cacheTableCount = 0
    self.strFileName = strFileName
    self.poolParent = poolRoot.transform
    self.prefab = prefab
    self.bindFlag = type(self.strFileName) == "string"
end

--- 析构
function goPool:__delete()
    if self.cacheTable ~= nil and self.cacheTableCount > 0 then
        for i = 1, self.cacheTableCount, 1 do
            if self.bindFlag then
                self.cacheTable[i]:Delete()
            else
                GameObject.Destroy(self.cacheTable[i].gameObject)
            end
        end
    end
    if self.poolTable ~= nil and self.poolTableCount > 0 then
        for i = 1, self.poolTableCount, 1 do
            if self.bindFlag then
                self.poolTable[i]:Delete()
            else
                GameObject.Destroy(self.poolTable[i].gameObject)
            end
        end
    end
    self.poolTable = nil
    self.cacheTable = nil
    self.poolParent = nil
    self.strFileName = nil
    self.prefab = nil
end

return goPool
